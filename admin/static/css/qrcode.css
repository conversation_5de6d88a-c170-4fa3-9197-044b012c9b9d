/* 二维码页面样式 */
.qrcode-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    padding: 20px;
    background-color: #f8f9fa;
    font-family: Arial, sans-serif;
}

.header {
    margin-bottom: 30px;
    text-align: center;
}

.content {
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    padding: 30px;
    width: 100%;
    max-width: 450px;
    text-align: center;
}

.qrcode-img {
    max-width: 250px;
    max-height: 250px;
    margin: 20px auto;
    border: 1px solid #ddd;
    padding: 10px;
}

.timer {
    font-size: 16px;
    margin: 15px 0;
    color: #666;
}

.manual-input {
    margin-top: 20px;
    display: none;
}

.buttons {
    margin-top: 20px;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
}

.btn {
    margin: 5px;
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    display: inline-flex;
    align-items: center;
}

.btn i {
    margin-right: 5px;
}

.btn-primary {
    background-color: #007bff;
    color: white;
}

.btn-primary:hover {
    background-color: #0069d9;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background-color: #5a6268;
}

.btn-success {
    background-color: #28a745;
    color: white;
}

.btn-success:hover {
    background-color: #218838;
}

.alert {
    padding: 15px;
    margin-bottom: 20px;
    border: 1px solid transparent;
    border-radius: 4px;
}

.alert-success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
}

.alert-warning {
    color: #856404;
    background-color: #fff3cd;
    border-color: #ffeeba;
}

.alert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

@media (max-width: 500px) {
    .content {
        padding: 20px 15px;
    }
    .qrcode-img {
        max-width: 200px;
        max-height: 200px;
    }
} 