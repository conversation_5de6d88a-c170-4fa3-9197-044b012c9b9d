<!DOCTYPE html>
<html>
<head>
    <title>XXXBot直接终端</title>
    <style>
        body, html { margin: 0; padding: 0; height: 100%; overflow: hidden; background: #000; }
        .container { display: flex; flex-direction: column; height: 100vh; }
        .header { background: #1a1a1a; color: white; padding: 10px; display: flex; justify-content: space-between; }
        .title { font-size: 18px; font-weight: bold; }
        .content { flex: 1; }
        iframe { width: 100%; height: 100%; border: none; }
    </style>
    <script>
        window.onload = function() {
            const frame = document.getElementById('terminal-frame');
            frame.focus();
            // 添加点击事件
            document.body.addEventListener('click', function() {
                frame.contentWindow.focus();
                frame.focus();
            });
        };
    </script>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="title">XXXBot 直接终端</div>
            <div>
                <a href="/system" style="color: white; text-decoration: none;">返回系统页面</a>
            </div>
        </div>
        <div class="content">
            <iframe id="terminal-frame" src="http://************:3000/?command=/bin/bash&title=XXXBot终端" style="width:100%;height:100%;border:none;"></iframe>
        </div>
    </div>
</body>
</html> 