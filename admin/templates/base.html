<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}XXXBOT管理后台{% endblock %}</title>

    <!-- Favicon图标 -->
    <link rel="icon" href="/static/img/favicon.ico" type="image/x-icon">
    <link rel="shortcut icon" href="/static/img/favicon.ico" type="image/x-icon">

    <!-- 更新到Bootstrap 5.3版本 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Bootstrap图标 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">

    <!-- 添加动画库 -->
    <link href="https://cdn.jsdelivr.net/npm/animate.css@4.1.1/animate.min.css" rel="stylesheet">

    <!-- 添加AOS滚动动画库 -->
    <link href="https://cdn.jsdelivr.net/npm/aos@2.3.4/dist/aos.css" rel="stylesheet">

    <!-- 设计系统字体 -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">

    <!-- 添加自定义CSS -->
    <link href="/static/css/admin.css" rel="stylesheet">

    <!-- 添加渐变色背景和设计元素 -->
    <style>
        body {
            background: linear-gradient(135deg, #F8F9FA 0%, #ECF0F1 100%);
            min-height: 100vh;
            position: relative;
        }

        .gradient-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
            background:
                radial-gradient(circle at 10% 20%, rgba(52, 152, 219, 0.05) 0%, transparent 50%),
                radial-gradient(circle at 90% 80%, rgba(46, 204, 113, 0.05) 0%, transparent 70%),
                radial-gradient(circle at 50% 50%, rgba(236, 240, 241, 0.1) 0%, transparent 60%);
        }

        .design-circle {
            position: fixed;
            border-radius: 50%;
            z-index: -1;
        }

        .design-circle-1 {
            width: 300px;
            height: 300px;
            background: linear-gradient(135deg, rgba(52, 152, 219, 0.1) 0%, rgba(44, 62, 80, 0.05) 100%);
            top: -150px;
            right: -150px;
        }

        .design-circle-2 {
            width: 200px;
            height: 200px;
            background: linear-gradient(135deg, rgba(46, 204, 113, 0.05) 0%, rgba(241, 196, 15, 0.03) 100%);
            bottom: -100px;
            left: -100px;
        }

        .logo-text {
            font-weight: 700;
            background: linear-gradient(45deg, #3498DB, #2C3E50);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        /* Material Design inspired card */
        .mat-card {
            background: white;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .mat-card:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            transform: translateY(-2px);
        }
    </style>

    <!-- 自定义组件样式 -->
    <style>
        .version-info {
            font-size: 1rem;
            display: flex;
            align-items: center;
        }

        /* 版本号标签样式 */
        #version-info .badge {
            font-size: 0.9rem;
            padding: 0.35rem 0.6rem;
        }

        #check-update-btn {
            padding: 0.35rem 0.6rem;
            font-size: 0.9rem;
            border-radius: 0.25rem;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .animate__rotateIn {
            animation: spin 1s linear infinite;
        }

        /* 更新内容样式 */
        .markdown-body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif;
            line-height: 1.6;
            color: #24292e;
        }

        .markdown-body h1 {
            font-size: 1.8rem;
            margin-top: 1.5rem;
            margin-bottom: 1rem;
            font-weight: 600;
            color: #0d6efd;
        }

        .markdown-body h2 {
            font-size: 1.5rem;
            margin-top: 1.5rem;
            margin-bottom: 1rem;
            font-weight: 600;
            border-bottom: 1px solid #eaecef;
            padding-bottom: 0.5rem;
            color: #0d6efd;
        }

        .markdown-body h3 {
            font-size: 1.2rem;
            margin-top: 1.2rem;
            margin-bottom: 0.8rem;
            font-weight: 600;
            color: #0d6efd;
        }

        .markdown-body p {
            margin-bottom: 1rem;
            line-height: 1.7;
        }

        .markdown-body ul {
            margin-bottom: 1rem;
            padding-left: 1.5rem;
        }

        .markdown-body li {
            margin-bottom: 0.5rem;
            position: relative;
            list-style-type: none;
        }

        .markdown-body li:before {
            content: '•';
            color: #0d6efd;
            font-weight: bold;
            position: absolute;
            left: -1.2rem;
        }

        .markdown-body br + li:before {
            content: '•';
            color: #0d6efd;
            font-weight: bold;
            display: inline-block;
            margin-right: 0.5rem;
        }

        /* 更新弹窗颜色和样式 */
        .rounded-pill {
            border-radius: 50rem !important;
        }

        /* 添加渐变效果 */
        .bg-primary.bg-gradient {
            background-image: linear-gradient(to right, #0d6efd, #0a58ca) !important;
        }

        /* 更新列表样式 */
        .update-list {
            padding-left: 0.5rem;
            margin-bottom: 1.5rem;
        }

        .update-list li {
            margin-bottom: 0.7rem;
            padding-left: 1.5rem;
            position: relative;
        }

        .update-list li:before {
            content: '•';
            color: #0d6efd;
            font-weight: bold;
            position: absolute;
            left: 0;
            font-size: 1.2rem;
        }

        .update-content h1:first-child,
        .update-content h2:first-child,
        .update-content h3:first-child {
            margin-top: 0 !important;
        }

        /* 添加动画效果 */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .modal-content {
            animation: fadeIn 0.3s ease-out;
        }

        /* 弹窗居中样式 */
        .modal-dialog-centered {
            display: flex;
            align-items: center;
            min-height: calc(100% - 3.5rem);
        }

        /* 确保弹窗在显示时居中 */
        .modal.show {
            display: flex !important;
            align-items: center;
            justify-content: center;
            padding-top: 0 !important;
        }

        /* 确保弹窗内容完全可见 */
        .modal-dialog {
            max-height: 90vh;
            margin: 1.75rem auto;
            width: auto;
            min-width: 500px;
        }

        /* 确保模态框大小合适 */
        .modal-dialog.modal-lg {
            max-width: 80%;
            width: 800px;
        }

        .modal-body {
            max-height: calc(90vh - 120px);
            overflow-y: auto;
        }

        /* 确保模态框层级正确 */
        .modal {
            z-index: 1050 !important;
        }

        .modal-backdrop {
            z-index: 1040 !important;
        }

        /* 按钮悬停效果 */
        .btn-primary {
            transition: all 0.2s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(13, 110, 253, 0.25);
        }
    </style>

    {% block extra_css %}{% endblock %}
</head>
<body class="animate__animated animate__fadeIn" style="overflow: hidden;">

    <!-- 侧边栏遮罩层 -->
    <div class="gradient-overlay"></div>
    <div class="design-circle design-circle-1"></div>
    <div class="design-circle design-circle-2"></div>

    <!-- 侧边栏 -->
    <nav class="sidebar d-none d-lg-flex" style="overflow-y: hidden;">
        <div class="sidebar-header">
            <h3 class="mb-0">
                <i class="bi bi-robot me-2"></i>
                <span>XXXBOT</span>
            </h3>
            <p class="text-white-50 mb-0 small">管理后台</p>
        </div>

        <div class="sidebar-menu mt-4" style="overflow-y: hidden;">
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a href="/" class="nav-link {% if request.path == '/' %}active{% endif %}">
                        <i class="bi bi-house-door"></i>
                        <span>控制面板</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/plugins" class="nav-link {% if request.path == '/plugins' %}active{% endif %}">
                        <i class="bi bi-puzzle"></i>
                        <span>插件管理</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/files" class="nav-link {% if request.path == '/files' %}active{% endif %}">
                        <i class="bi bi-folder"></i>
                        <span>文件管理</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/contacts" class="nav-link {% if request.path == '/contacts' %}active{% endif %}">
                        <i class="bi bi-people"></i>
                        <span>联系人</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/friend_circle" class="nav-link {% if request.path == '/friend_circle' %}active{% endif %}">
                        <i class="bi bi-image"></i>
                        <span>朋友圈</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/ai-platforms" class="nav-link {% if request.path == '/ai-platforms' %}active{% endif %}">
                        <i class="bi bi-robot"></i>
                        <span>AI平台管理</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/accounts" class="nav-link {% if request.path == '/accounts' %}active{% endif %}">
                        <i class="bi bi-person-badge"></i>
                        <span>账号管理</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/system" class="nav-link {% if request.path == '/system' %}active{% endif %}">
                        <i class="bi bi-gear"></i>
                        <span>系统状态</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/notification" class="nav-link {% if request.path == '/notification' %}active{% endif %}">
                        <i class="bi bi-bell"></i>
                        <span>通知设置</span>
                    </a>
                </li>
                <li class="nav-item mt-4">
                    <a href="/qrcode" class="nav-link special-link">
                        <i class="bi bi-qr-code"></i>
                        <span>登录二维码</span>
                    </a>
                </li>
            </ul>
        </div>

        <div class="sidebar-footer">
            <span>© 2025 老夏的金库</span>
        </div>
    </nav>

    <!-- 主内容区域 -->
    <div class="main-content">
        <!-- 顶部导航栏 -->
        <nav class="navbar navbar-expand navbar-light topbar mb-4 shadow-sm rounded animate__animated animate__fadeInDown">
            <div class="container-fluid">
                <div class="d-flex align-items-center">
                    <h1 class="h4 mb-0 text-gray-800">{% block page_title %}控制面板{% endblock %}</h1>
                    <div class="ms-3">{% block page_actions %}{% endblock %}</div>
                </div>

                <div class="ms-auto d-flex align-items-center">
                    <div class="version-info me-3" id="version-info">
                        <div class="d-flex align-items-center">
                            <div class="d-flex align-items-center me-2" id="version-display">
                                <span class="badge bg-primary shadow-sm">
                                    <i class="bi bi-code-slash me-1"></i>
                                    <span id="current-version" style="font-size: 0.9rem;">{{ version }}</span>
                                </span>
                                <button id="check-update-btn" class="btn btn-outline-primary btn-sm ms-1 shadow-sm" onclick="manualCheckUpdate()" data-bs-toggle="tooltip" title="检查是否有新版本">
                                    <i class="bi bi-arrow-repeat"></i>
                                </button>
                            </div>
                            <div id="update-container" class="align-items-center" style="display: none !important;">
                                <span class="badge bg-success shadow-sm" data-bs-toggle="tooltip" title="{{ update_description }}" style="font-size: 0.9rem;" id="update-badge">
                                    <i class="bi bi-arrow-up-circle-fill me-1"></i>新版本: <span id="latest-version">{{ latest_version }}</span>
                                </span>
                                <button id="update-now-btn" class="btn btn-outline-success btn-sm ms-1 shadow-sm" onclick="updateNow()" data-bs-toggle="tooltip" title="立即更新到最新版本" style="padding: 0.25rem 0.5rem;">
                                    <i class="bi bi-cloud-download"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </nav>

        <!-- 主要内容区 -->
        <div class="content-wrapper" data-aos="fade-up">
            {% block content %}{% endblock %}
        </div>

        <!-- 页脚 -->
        <footer class="footer mt-5 py-3 text-center animate__animated animate__fadeInUp">
            <div class="container">
                <span class="text-muted">xxxbot-pad © 2025 - 技术支持 <a href="https://github.com/NanSsye/xxxbot-pad/" class="text-decoration-none" target="_blank">xxxTeam</a></span>
                <div class="mt-2">
                    <a href="https://github.com/NanSsye/xxxbot-pad/" class="btn btn-sm btn-outline-secondary me-1">
                        <i class="bi bi-github"></i>
                    </a>
                    <a href="#" class="btn btn-sm btn-outline-secondary me-1">
                        <i class="bi bi-discord"></i>
                    </a>
                    <a href="#" class="btn btn-sm btn-outline-secondary">
                        <i class="bi bi-question-circle"></i> 帮助
                    </a>
                </div>
            </div>
        </footer>
    </div>

    <!-- 移动端底部导航栏 -->
    <nav class="mobile-nav d-lg-none">
        <a href="/" class="mobile-nav-item {% if request.path == '/' %}active{% endif %}">
            <i class="bi bi-house-door"></i>
            <span>首页</span>
        </a>
        <a href="/plugins" class="mobile-nav-item {% if request.path == '/plugins' %}active{% endif %}">
            <i class="bi bi-puzzle"></i>
            <span>插件</span>
        </a>
        <a href="/files" class="mobile-nav-item {% if request.path == '/files' %}active{% endif %}">
            <i class="bi bi-folder"></i>
            <span>文件</span>
        </a>
        <a href="/contacts" class="mobile-nav-item {% if request.path == '/contacts' %}active{% endif %}">
            <i class="bi bi-people"></i>
            <span>联系人</span>
        </a>
        <a href="/friend_circle" class="mobile-nav-item {% if request.path == '/friend_circle' %}active{% endif %}">
            <i class="bi bi-image"></i>
            <span>朋友圈</span>
        </a>
        <a href="/system" class="mobile-nav-item {% if request.path == '/system' %}active{% endif %}">
            <i class="bi bi-gear"></i>
            <span>系统</span>
        </a>
        <a href="/notification" class="mobile-nav-item {% if request.path == '/notification' %}active{% endif %}">
            <i class="bi bi-bell"></i>
            <span>通知</span>
        </a>
    </nav>

    <!-- Toast通知组件 -->
    <div class="toast-container position-fixed top-0 end-0 p-3">
        <!-- 通知会动态添加到这里 -->
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- 加载AOS滚动动画库 -->
    <script src="https://cdn.jsdelivr.net/npm/aos@2.3.4/dist/aos.js"></script>

    <!-- 加载Chart.js图表库 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.0.1/dist/chart.umd.min.js"></script>

    <!-- 自定义JS -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化AOS动画
            AOS.init({
                duration: 800,
                easing: 'ease-in-out',
                once: true,
                offset: 50
            });

            // 初始化所有提示框
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });

            // 显示通知函数
            window.showToast = function(title, message, type = 'info') {
                const toastContainer = document.querySelector('.toast-container');

                const toastEl = document.createElement('div');
                toastEl.className = `toast animate__animated animate__fadeInRight bg-${type} text-white`;
                toastEl.setAttribute('role', 'alert');
                toastEl.setAttribute('aria-live', 'assertive');
                toastEl.setAttribute('aria-atomic', 'true');

                const toastHeader = document.createElement('div');
                toastHeader.className = 'toast-header bg-transparent text-white';

                const strongEl = document.createElement('strong');
                strongEl.className = 'me-auto';
                strongEl.textContent = title;

                const timeEl = document.createElement('small');
                timeEl.textContent = '刚刚';

                const closeButton = document.createElement('button');
                closeButton.type = 'button';
                closeButton.className = 'btn-close btn-close-white';
                closeButton.setAttribute('data-bs-dismiss', 'toast');
                closeButton.setAttribute('aria-label', '关闭');

                toastHeader.appendChild(strongEl);
                toastHeader.appendChild(timeEl);
                toastHeader.appendChild(closeButton);

                const toastBody = document.createElement('div');
                toastBody.className = 'toast-body';
                toastBody.textContent = message;

                toastEl.appendChild(toastHeader);
                toastEl.appendChild(toastBody);

                toastContainer.appendChild(toastEl);

                const toast = new bootstrap.Toast(toastEl);
                toast.show();

                toastEl.addEventListener('hidden.bs.toast', function() {
                    toastEl.remove();
                });
            };

            // 重启容器函数
            window.restartContainer = function() {
                console.log('重启容器按钮被点击 (内联函数)');

                if (confirm('确定要重启容器吗？这将导致服务短暂中断。')) {
                    console.log('用户确认重启');

                    // 显示加载状态
                    const restartBtn = document.getElementById('restart-container-btn');
                    if (restartBtn) {
                        restartBtn.disabled = true;
                        restartBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>重启中...';
                    }

                    // 使用当前主机名和端口构建正确的URL
                    const apiUrl = window.location.origin + '/api/system/restart';
                    console.log('发送重启请求到:', apiUrl);

                    // 调用重启API
                    fetch(apiUrl, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        credentials: 'include',  // 确保发送认证Cookie
                        body: JSON.stringify({})
                    })
                    .then(response => {
                        console.log('收到重启API响应:', response);
                        if (!response.ok) {
                            throw new Error('API响应状态码: ' + response.status);
                        }
                        return response.json();
                    })
                    .then(data => {
                        console.log('重启API响应数据:', data);

                        if (data.success) {
                            // 显示成功消息
                            showToast('重启已开始', data.message || '容器正在重启，页面将在几秒后自动刷新...', 'success');

                            // 5秒后刷新页面
                            setTimeout(() => {
                                window.location.reload();
                            }, 5000);
                        } else {
                            // 显示错误
                            showToast('重启失败', data.error || '重启请求失败', 'error');
                            // 恢复按钮状态
                            if (restartBtn) {
                                restartBtn.disabled = false;
                                restartBtn.innerHTML = '<i class="bi bi-arrow-clockwise me-1"></i>重启容器';
                            }
                        }
                    })
                    .catch(error => {
                        console.error('重启请求失败:', error);
                        showToast('重启失败', '请求发送失败: ' + error.message, 'error');
                        // 恢复按钮状态
                        const restartBtn = document.getElementById('restart-container-btn');
                        if (restartBtn) {
                            restartBtn.disabled = false;
                            restartBtn.innerHTML = '<i class="bi bi-arrow-clockwise me-1"></i>重启容器';
                        }
                    });
                }
            };
        });
    </script>

    <!-- 通用模态窗口管理 -->
    <script src="/admin/static/js/custom.js?v={{ version }}"></script>

    <!-- 版本检查脚本 -->
    <script>
        // 手动检查更新函数
        function manualCheckUpdate() {
            // 显示加载状态
            const checkBtn = document.getElementById('check-update-btn');
            const originalHTML = checkBtn.innerHTML;
            checkBtn.innerHTML = '<i class="bi bi-arrow-repeat animate__animated animate__rotateIn"></i>';
            checkBtn.disabled = true;

            // 默认隐藏更新容器
            const updateContainer = document.getElementById('update-container');
            if (updateContainer) {
                updateContainer.removeAttribute('style');
                updateContainer.setAttribute('style', 'display: none !important;');
                updateContainer.classList.remove('d-flex');
            }

            // 隐藏可能存在的tooltip
            const tooltip = bootstrap.Tooltip.getInstance(checkBtn);
            if (tooltip) {
                tooltip.hide();
            }

            // 调用检查函数
            checkForUpdates(true)
                .then(hasUpdate => {
                    // 恢复按钮状态
                    setTimeout(() => {
                        checkBtn.innerHTML = originalHTML;
                        checkBtn.disabled = false;

                        // 如果没有更新，不需要显示提示，因为已经在checkForUpdates函数中显示了

                        // 清除所有tooltip
                        document.querySelectorAll('[data-bs-toggle="tooltip"]').forEach(el => {
                            const tooltipInstance = bootstrap.Tooltip.getInstance(el);
                            if (tooltipInstance) {
                                tooltipInstance.hide();
                            }
                        });
                    }, 1000);
                })
                .catch(error => {
                    console.error('检查更新失败:', error);
                    // 恢复按钮状态
                    setTimeout(() => {
                        checkBtn.innerHTML = originalHTML;
                        checkBtn.disabled = false;
                        alert('检查更新失败，请稍后再试。');

                        // 清除所有tooltip
                        document.querySelectorAll('[data-bs-toggle="tooltip"]').forEach(el => {
                            const tooltipInstance = bootstrap.Tooltip.getInstance(el);
                            if (tooltipInstance) {
                                tooltipInstance.hide();
                            }
                        });
                    }, 1000);
                });
        }

        // 立即更新函数
        function updateNow() {
            if (!confirm('确定要立即更新到最新版本吗？更新过程中可能会导致服务短暂中断。')) {
                return;
            }

            // 显示加载状态
            const updateBtn = document.getElementById('update-now-btn');
            const originalHTML = updateBtn.innerHTML;
            updateBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 更新中...';
            updateBtn.disabled = true;

            // 发送更新请求
            fetch('/api/version/update', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    current_version: document.getElementById('current-version').innerText
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 显示成功消息
                    showToast('更新已开始', data.message || '系统正在更新并重启，请稍候...', 'success');

                    // 显示加载中的提示
                    const loadingDiv = document.createElement('div');
                    loadingDiv.className = 'position-fixed top-0 start-0 w-100 h-100 d-flex justify-content-center align-items-center bg-dark bg-opacity-75';
                    loadingDiv.style.zIndex = '9999';
                    loadingDiv.innerHTML = `
                        <div class="card shadow p-4 text-center">
                            <div class="spinner-border text-primary mb-3" role="status" style="width: 3rem; height: 3rem;">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                            <h5 class="mb-2">系统正在更新并重启</h5>
                            <p class="text-muted mb-2">请稍候，这可能需要几秒钟时间...</p>
                            <div class="progress mb-3">
                                <div id="restart-progress" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
                            </div>
                            <p class="small text-muted">更新完成后将自动跳转到登录页面</p>
                        </div>
                    `;
                    document.body.appendChild(loadingDiv);

                    // 进度条动画
                    const progressBar = document.getElementById('restart-progress');
                    let progress = 0;
                    const progressInterval = setInterval(() => {
                        progress += 2;
                        if (progress > 100) {
                            clearInterval(progressInterval);
                            // 重启完成，跳转到登录页面
                            window.location.href = '/qrcode';
                        } else {
                            progressBar.style.width = `${progress}%`;
                        }
                    }, 200); // 20秒完成进度条
                } else {
                    // 显示错误
                    showToast('更新失败', data.error || '更新请求失败', 'danger');
                    // 恢复按钮状态
                    updateBtn.disabled = false;
                    updateBtn.innerHTML = originalHTML;
                }
            })
            .catch(error => {
                console.error('更新请求失败:', error);
                showToast('更新失败', '请求发送失败: ' + error.message, 'danger');
                // 恢复按钮状态
                updateBtn.disabled = false;
                updateBtn.innerHTML = originalHTML;
            });
        }

        // 版本检查函数
        function checkForUpdates(isManualCheck = false) {
            // 获取当前版本
            const currentVersion = document.getElementById('current-version').innerText;

            // 返回一个Promise
            return new Promise((resolve, reject) => {
                // 发送版本检查请求
                fetch('/api/version/check', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        current_version: currentVersion
                    })
                })
                .then(response => response.json())
                .then(data => {
                    // 获取当前版本
                    const currentVersion = document.getElementById('current-version').innerText;

                    // 检查是否有更新且版本号不同
                    if (data.success && data.update_available && data.latest_version !== currentVersion) {
                        // 更新版本信息显示
                        const updateContainer = document.getElementById('update-container');
                        const latestVersionSpan = document.getElementById('latest-version');
                        const updateBadge = document.getElementById('update-badge');

                        // 更新版本号和描述
                        latestVersionSpan.textContent = data.latest_version;
                        updateBadge.setAttribute('title', data.update_description || '有新版本可用');
                        updateBadge.setAttribute('href', data.update_url || '#');

                        // 显示更新容器
                        updateContainer.removeAttribute('style');
                        updateContainer.setAttribute('style', 'display: flex !important;');
                        updateContainer.classList.add('d-flex');

                        // 显示更新内容弹窗
                        showUpdateContentModal(data.latest_version, data.update_description);

                        // 如果是手动检查，清除弹窗显示状态，以便下次自动显示
                        if (isManualCheck) {
                            localStorage.removeItem('updateModalShown');
                            showToast('发现新版本', `有新版本可用: ${data.latest_version}`, 'success');
                        }

                        // 初始化工具提示
                        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
                        tooltipTriggerList.map(function (tooltipTriggerEl) {
                            return new bootstrap.Tooltip(tooltipTriggerEl);
                        });

                        // 保存更新信息到本地存储
                        localStorage.setItem('updateInfo', JSON.stringify({
                            latest_version: data.latest_version,
                            update_url: data.update_url,
                            update_description: data.update_description
                        }));

                        // 返回有更新
                        resolve(true);
                    } else {
                        // 如果版本相同，隐藏更新容器
                        if (data.success && data.latest_version === currentVersion) {
                            // 隐藏更新容器
                            const updateContainer = document.getElementById('update-container');
                            if (updateContainer) {
                                updateContainer.removeAttribute('style');
                                updateContainer.setAttribute('style', 'display: none !important;');
                                updateContainer.classList.remove('d-flex');
                            }
                            // 清除本地存储的更新信息
                            localStorage.removeItem('updateInfo');
                        }

                        // 显示消息（如果是手动检查）
                        if (isManualCheck) {
                            showToast('检查完成', '当前已经是最新版本', 'info');
                        }
                        // 返回没有更新
                        resolve(false);
                    }
            })
            .catch(error => {
                console.error('版本检查失败:', error);
                reject(error);
            });
            });
        }

        // 立即更新函数已在上面定义

        // 显示更新内容弹窗函数
        function showUpdateContentModal(latestVersion, updateDescription) {
            // 更新模态框中的版本信息
            document.getElementById('modal-current-version').textContent = document.getElementById('current-version').innerText;
            document.getElementById('modal-latest-version').textContent = latestVersion;

            // 处理更新内容
            const updateContentElement = document.getElementById('update-description-content');

            // 将更新描述转换为HTML
            if (updateDescription) {
                // 简化处理，避免复杂的正则表达式
                let htmlContent = '';

                // 将更新描述按行分割
                const lines = updateDescription.split('\n');
                let inList = false;
                let inSection = false;

                for (let i = 0; i < lines.length; i++) {
                    const line = lines[i].trim();

                    // 空行处理
                    if (line === '') {
                        if (inList) {
                            htmlContent += '</ul>';
                            inList = false;
                        }
                        if (inSection) {
                            // 不添加额外的空行，保持段落结构
                        } else {
                            htmlContent += '<div class="my-3"></div>'; // 添加间距而不是空行
                            inSection = true;
                        }
                        continue;
                    } else {
                        inSection = false;
                    }

                    // 标题处理
                    if (line.startsWith('# ')) {
                        if (inList) {
                            htmlContent += '</ul>';
                            inList = false;
                        }
                        htmlContent += `<h1 class="mt-4">${line.substring(2)}</h1>`;
                    } else if (line.startsWith('## ')) {
                        if (inList) {
                            htmlContent += '</ul>';
                            inList = false;
                        }
                        htmlContent += `<h2 class="mt-4">${line.substring(3)}</h2>`;
                    } else if (line.startsWith('### ')) {
                        if (inList) {
                            htmlContent += '</ul>';
                            inList = false;
                        }
                        htmlContent += `<h3 class="mt-3">${line.substring(4)}</h3>`;
                    }
                    // 列表项处理
                    else if (line.startsWith('- ')) {
                        if (!inList) {
                            htmlContent += '<ul class="update-list">';
                            inList = true;
                        }
                        htmlContent += `<li>${line.substring(2)}</li>`;
                    }
                    // 数字列表处理
                    else if (/^\d+\.\s/.test(line)) {
                        const textContent = line.replace(/^\d+\.\s/, '');
                        if (!inList) {
                            htmlContent += '<ul class="update-list">';
                            inList = true;
                        }
                        htmlContent += `<li>${textContent}</li>`;
                    }
                    // 普通文本
                    else {
                        if (inList) {
                            htmlContent += '</ul>';
                            inList = false;
                        }
                        htmlContent += `<p>${line}</p>`;
                    }
                }

                // 确保列表正确关闭
                if (inList) {
                    htmlContent += '</ul>';
                }

                // 添加额外的样式类
                htmlContent = `<div class="update-content">${htmlContent}</div>`;

                updateContentElement.innerHTML = htmlContent;
            } else {
                updateContentElement.innerHTML = '<div class="text-center py-4"><i class="bi bi-info-circle text-muted fs-1 mb-3"></i><p class="text-muted">暂无详细的更新内容描述</p></div>';
            }

            // 显示模态框
            const modal = new bootstrap.Modal(document.getElementById('update-content-modal'));
            modal.show();
        }

        // 获取Cookie函数
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    // 判断这个cookie的名称是否是我们想要的
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化工具提示
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });

            // 隐藏更新容器（默认状态）
            const updateContainer = document.getElementById('update-container');
            if (updateContainer) {
                updateContainer.removeAttribute('style');
                updateContainer.setAttribute('style', 'display: none !important;');
                updateContainer.classList.remove('d-flex');
            }

            // 检查是否有存储的更新信息
            const storedUpdateInfo = localStorage.getItem('updateInfo');
            if (storedUpdateInfo) {
                try {
                    const updateInfo = JSON.parse(storedUpdateInfo);
                    const currentVersion = document.getElementById('current-version').innerText;

                    // 只有当存储的版本与当前版本不同时才显示更新标记
                    if (updateInfo.latest_version !== currentVersion) {
                        // 更新版本信息显示
                        const latestVersionSpan = document.getElementById('latest-version');
                        const updateBadge = document.getElementById('update-badge');

                        // 更新版本号和描述
                        latestVersionSpan.textContent = updateInfo.latest_version;
                        updateBadge.setAttribute('title', updateInfo.update_description || '有新版本可用');
                        updateBadge.setAttribute('href', updateInfo.update_url || '#');

                        // 显示更新容器
                        updateContainer.removeAttribute('style');
                        updateContainer.setAttribute('style', 'display: flex !important;');
                        updateContainer.classList.add('d-flex');

                        // 自动显示更新弹窗已禁用
                        // if (!localStorage.getItem('updateModalShown')) {
                        //     setTimeout(() => {
                        //         // 检查必要的元素是否存在
                        //         const modalElement = document.getElementById('update-content-modal');
                        //         const currentVersionElement = document.getElementById('current-version');
                        //
                        //         if (modalElement && currentVersionElement) {
                        //             showUpdateContentModal(updateInfo.latest_version, updateInfo.update_description);
                        //             // 记录已经显示过弹窗
                        //             localStorage.setItem('updateModalShown', 'true');
                        //         }
                        //     }, 3000);
                        // }
                    } else {
                        // 如果版本相同，清除存储的更新信息
                        localStorage.removeItem('updateInfo');
                    }
                } catch (e) {
                    console.error('解析存储的更新信息失败:', e);
                    localStorage.removeItem('updateInfo');
                }
            }

            // 自动更新检查已禁用
            // checkForUpdates().catch(error => console.error('自动检查更新失败:', error));
            // setInterval(() => {
            //     checkForUpdates().catch(error => console.error('自动检查更新失败:', error));
            // }, 3600000); // 3600000毫秒 = 1小时

            // 添加更新按钮的事件监听器
            document.getElementById('modal-update-now-btn').addEventListener('click', updateNow);
        });
    </script>

    <!-- 页面特定的JS -->
    {% block extra_js %}{% endblock %}

    <!-- 模态框容器 -->
    {% block modals %}{% endblock %}

    <!-- 更新内容弹窗 -->
    <div class="modal fade" id="update-content-modal" tabindex="-1" aria-labelledby="updateContentModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-lg" style="max-width: 650px; margin: 0 auto; transform: translateY(-5vh);">
            <div class="modal-content shadow-lg border-0">
                <div class="modal-header bg-light text-dark">
                    <h5 class="modal-title" id="updateContentModalLabel">
                        <i class="bi bi-arrow-up-circle-fill me-2 text-primary"></i>系统更新提示
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
                </div>
                <div class="modal-body p-4" style="max-height: 60vh; overflow-y: auto;">
                    <div class="d-flex align-items-center justify-content-center mb-4">
                        <div class="me-4">
                            <span class="badge bg-secondary fs-6 p-2 px-3 rounded-pill">
                                <i class="bi bi-code-slash me-1"></i>当前版本: <span id="modal-current-version" class="fw-bold">{{ version }}</span>
                            </span>
                        </div>
                        <div class="d-flex align-items-center">
                            <div class="arrow-right me-2">
                                <i class="bi bi-arrow-right fs-4 text-muted"></i>
                            </div>
                            <span class="badge bg-primary fs-6 p-2 px-3 rounded-pill">
                                <i class="bi bi-arrow-up-circle-fill me-1"></i>新版本: <span id="modal-latest-version" class="fw-bold">{{ latest_version }}</span>
                            </span>
                        </div>
                    </div>

                    <div class="card mb-3 border-0 shadow-sm">
                        <div class="card-header bg-light">
                            <h5 class="mb-0 d-flex align-items-center">
                                <i class="bi bi-list-check me-2 text-primary"></i>更新内容
                            </h5>
                        </div>
                        <div class="card-body">
                            <div id="update-description-content" class="markdown-body">
                                <!-- 更新内容将通过JavaScript动态加载 -->
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer justify-content-end border-0 pt-0 pb-3">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal" style="padding: 0.375rem 0.75rem;">
                        <i class="bi bi-x-circle me-1"></i>关闭
                    </button>
                    <button type="button" class="btn btn-outline-primary" id="modal-update-now-btn" style="padding: 0.375rem 0.75rem;">
                        <i class="bi bi-cloud-download me-1"></i>立即更新
                    </button>
                </div>
            </div>
        </div>
    </div>
</body>
</html>