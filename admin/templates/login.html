<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - XXXBot管理后台</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">

    <!-- 添加动画库 -->
    <link href="https://cdn.jsdelivr.net/npm/animate.css@4.1.1/animate.min.css" rel="stylesheet">

    <!-- 自定义样式 -->
    <link href="/static/css/admin.css" rel="stylesheet">

    <style>
        body {
            min-height: 100vh;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1rem;
        }

        .login-container {
            width: 100%;
            max-width: 420px;
            margin: auto;
        }

        .login-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            transform: translateY(0);
            transition: all 0.3s ease;
        }

        .login-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 48px rgba(0, 0, 0, 0.15);
        }

        .login-header {
            text-align: center;
            padding: 2rem 1rem;
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            color: white;
            position: relative;
            overflow: hidden;
        }

        .login-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, transparent 100%);
            z-index: 1;
        }

        .login-header .logo-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            position: relative;
            z-index: 2;
        }

        .login-header h1 {
            font-size: 1.5rem;
            margin: 0;
            font-weight: 600;
            position: relative;
            z-index: 2;
        }

        .login-body {
            padding: 2rem;
        }

        .form-floating {
            margin-bottom: 1rem;
        }

        .form-floating > .form-control {
            padding-left: 2.5rem;
        }

        .form-floating > label {
            padding-left: 2.5rem;
        }

        .input-icon {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--neutral-500);
            z-index: 3;
        }

        .btn-login {
            width: 100%;
            padding: 0.8rem;
            font-weight: 500;
            margin-top: 1rem;
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            border: none;
            position: relative;
            overflow: hidden;
        }

        .btn-login:hover {
            background: linear-gradient(45deg, var(--primary-dark), var(--secondary-dark));
            transform: translateY(-1px);
        }

        .btn-login::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, transparent 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .btn-login:hover::after {
            opacity: 1;
        }

        .login-footer {
            text-align: center;
            padding: 1rem;
            color: var(--neutral-600);
            font-size: 0.875rem;
        }

        .alert {
            border: none;
            border-radius: var(--border-radius-md);
            margin-bottom: 1rem;
        }

        .form-check-label {
            color: var(--neutral-700);
        }

        .form-check-input:checked {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        @media (max-width: 576px) {
            .login-body {
                padding: 1.5rem;
            }

            .login-header {
                padding: 1.5rem 1rem;
            }

            .login-header .logo-icon {
                font-size: 2.5rem;
            }

            .login-header h1 {
                font-size: 1.25rem;
            }
        }
    </style>
</head>
<body class="animate__animated animate__fadeIn">
    <div class="login-container">
        <div class="login-card animate__animated animate__fadeInUp">
            <div class="login-header">
                <i class="bi bi-robot logo-icon"></i>
                <h1>XXXBot 管理后台</h1>
            </div>

            <div class="login-body">
                <div id="login-alert" class="alert alert-danger animate__animated animate__shakeX" style="display: none;"></div>

                <form id="login-form" class="user">
                    <div class="form-floating mb-3">
                        <i class="bi bi-person input-icon"></i>
                        <input type="text" class="form-control" id="username" name="username" placeholder="用户名">
                        <label for="username">用户名</label>
                    </div>

                    <div class="form-floating mb-3">
                        <i class="bi bi-key input-icon"></i>
                        <input type="password" class="form-control" id="password" name="password" placeholder="密码">
                        <label for="password">密码</label>
                    </div>

                    <div class="form-check mb-3">
                        <input type="checkbox" class="form-check-input" id="remember" name="remember">
                        <label class="form-check-label" for="remember">记住我</label>
                    </div>

                    <button type="submit" class="btn btn-primary btn-login">
                        <i class="bi bi-box-arrow-in-right me-2"></i>登录
                    </button>
                </form>
            </div>

            <div class="login-footer">
                <div class="d-flex justify-content-between align-items-center">
                    <p class="mb-0">老夏的金库 © 2025</p>
                    <span class="badge bg-primary" id="version-badge">v{{ version }}</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const loginForm = document.getElementById('login-form');
            const alertBox = document.getElementById('login-alert');

            // 检查是否从其他页面重定向过来
            const urlParams = new URLSearchParams(window.location.search);
            const nextPage = urlParams.get('next') || '/';

            // 处理表单提交
            loginForm.addEventListener('submit', function(e) {
                e.preventDefault();

                const username = document.getElementById('username').value.trim();
                const password = document.getElementById('password').value;
                const remember = document.getElementById('remember').checked;

                if (!username || !password) {
                    showAlert('请输入用户名和密码');
                    return;
                }

                // 登录请求
                fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password,
                        remember: remember
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // 登录成功动画
                        document.querySelector('.login-card').classList.add('animate__fadeOutUp');
                        setTimeout(() => {
                            window.location.href = nextPage;
                        }, 500);
                    } else {
                        // 登录失败，显示错误
                        showAlert(data.error || '登录失败，请检查用户名和密码');
                    }
                })
                .catch(error => {
                    console.error('登录请求出错:', error);
                    showAlert('登录请求出错，请稍后重试');
                });
            });

            // 显示警告信息
            function showAlert(message) {
                alertBox.textContent = message;
                alertBox.style.display = 'block';
                alertBox.classList.remove('animate__shakeX');
                void alertBox.offsetWidth; // 触发重绘
                alertBox.classList.add('animate__shakeX');

                // 自动隐藏
                setTimeout(() => {
                    alertBox.classList.add('animate__fadeOut');
                    setTimeout(() => {
                        alertBox.style.display = 'none';
                        alertBox.classList.remove('animate__fadeOut');
                    }, 500);
                }, 3000);
            }

            // 页面加载完成后自动聚焦用户名输入框
            document.getElementById('username').focus();
        });
    </script>
</body>
</html>