{% extends "base.html" %}

{% block title %}XXXBot 控制面板{% endblock %}

{% block page_title %}控制面板{% endblock %}

{% block extra_css %}
<style>
    /* 重启覆盖层样式 */
    .restart-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.7);
        z-index: 9999;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .restart-dialog {
        background-color: white;
        border-radius: 8px;
        padding: 20px;
        width: 80%;
        max-width: 500px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        text-align: center;
    }
    .dashboard-stat-icon {
        height: 60px;
        width: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 12px;
        font-size: 1.5rem;
        color: white;
        margin-bottom: 0.5rem;
    }

    .status-dot {
        display: inline-block;
        width: 10px;
        height: 10px;
        border-radius: 50%;
        margin-right: 5px;
    }

    .status-dot.online {
        background-color: var(--success-color);
        box-shadow: 0 0 0 4px rgba(46, 204, 113, 0.2);
        animation: pulse 2s infinite;
    }

    .status-dot.offline {
        background-color: var(--danger-color);
    }

    .status-dot.waiting {
        background-color: var(--warning-color);
    }

    .status-dot.ready {
        background-color: var(--info-color);
    }

    @keyframes pulse {
        0% {
            box-shadow: 0 0 0 0 rgba(46, 204, 113, 0.4);
        }
        70% {
            box-shadow: 0 0 0 8px rgba(46, 204, 113, 0);
        }
        100% {
            box-shadow: 0 0 0 0 rgba(46, 204, 113, 0);
        }
    }

    .progress-ring {
        transform: rotate(-90deg);
    }

    .progress-ring__circle {
        transition: stroke-dashoffset 0.5s ease;
    }

    .stat-card {
        position: relative;
        overflow: hidden;
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 70%);
        opacity: 0;
        transform: scale(0.5);
        transition: transform 0.5s ease, opacity 0.5s ease;
    }

    .stat-card:hover::before {
        opacity: 1;
        transform: scale(1);
    }

    .metric-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
        gap: 1.5rem;
    }

    .btn-qrcode {
        position: relative;
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .btn-qrcode::after {
        content: '';
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: -100%;
        background: linear-gradient(90deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.2) 50%, rgba(255,255,255,0) 100%);
        transition: left 0.5s ease;
    }

    .btn-qrcode:hover::after {
        left: 100%;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid p-0">
    <div class="row">
        <div class="col-12">
            <div class="card dashboard-card mb-4" data-aos="fade-up">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="bi bi-speedometer2 me-2 text-primary"></i>系统概览
                    </h5>
                    <span id="last-updated" class="text-muted small">最后更新: {{ current_time }}</span>
                </div>
                <div class="card-body px-0 py-3">
                    <div class="metric-grid px-4">
                        <!-- 机器人状态卡片 -->
                        <div class="stat-card" data-aos="fade-up" data-aos-delay="100">
                            <div class="dashboard-stat-icon" style="background: linear-gradient(135deg, #3498DB, #2874A6);">
                                <i class="bi bi-robot"></i>
                            </div>
                            <div class="d-flex align-items-center mb-2">
                                <div class="d-flex align-items-center">
                                    <h6 class="stat-title mb-0">机器人状态</h6>
                                    <button id="refresh-status" class="refresh-button ms-2" title="刷新状态">
                                        <i class="bi bi-arrow-clockwise"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="stat-value d-flex align-items-center mb-0 py-1">
                                <span id="bot-status-display" class="status-badge" data-status="{{ bot_status }}">
                                    <span class="status-dot" id="status-dot"></span>
                                    <span id="status-text">{{ bot_status }}</span>
                                </span>
                            </div>
                            <div class="stat-description mb-1" id="status-description"></div>
                            <!-- 添加机器人信息显示 -->
                            <div id="bot-info" class="mt-1" style="display: none;">
                                <div class="small text-muted d-flex align-items-center mb-1 py-0">
                                    <i class="bi bi-person-badge me-1"></i>
                                    <span>昵称：<span id="bot-nickname">-</span></span>
                                </div>
                                <div class="small text-muted d-flex align-items-center mb-1 py-0">
                                    <i class="bi bi-chat-dots me-1"></i>
                                    <span>微信号：<span id="bot-wxid">-</span></span>
                                </div>

                            </div>
                            <!-- 在卡片底部添加按钮 -->
                            <div class="mt-3">
                                <!-- 第一排按钮 -->
                                <div class="row g-2 mb-2">
                                    <!-- 协议选择按钮 -->
                                    <div class="col-6">
                                        <div class="dropdown w-100">
                                            <button id="protocol-btn" class="btn btn-sm btn-outline-primary dropdown-toggle w-100" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                                <i class="bi bi-hdd-network me-1"></i><span id="current-protocol">849协议</span>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><a class="dropdown-item protocol-option" href="#" data-value="849">849协议</a></li>
                                                <li><a class="dropdown-item protocol-option" href="#" data-value="855">855协议</a></li>
                                                <li><a class="dropdown-item protocol-option" href="#" data-value="ipad">iPad协议</a></li>
                                            </ul>
                                        </div>
                                    </div>

                                    <!-- 消息过滤按钮 -->
                                    <div class="col-6">
                                        <div class="dropdown w-100">
                                            <button id="filter-btn" class="btn btn-sm btn-outline-primary dropdown-toggle w-100" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                                <i class="bi bi-funnel me-1"></i><span id="current-filter">不过滤</span>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><a class="dropdown-item filter-option" href="#" data-value="None">不过滤</a></li>
                                                <li><a class="dropdown-item filter-option" href="#" data-value="Whitelist">白名单模式</a></li>
                                                <li><a class="dropdown-item filter-option" href="#" data-value="Blacklist">黑名单模式</a></li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>

                                <!-- 第二排按钮 -->
                                <div class="row g-2">
                                    <!-- 切换账号按钮 -->
                                    <div class="col-6">
                                        <button id="switch-account" class="btn btn-sm btn-outline-primary w-100" title="切换微信账号" onclick="handleSwitchAccount()" style="display: inline-block;">
                                            <i class="bi bi-arrow-repeat me-1"></i>切换账号
                                        </button>
                                    </div>

                                    <!-- 系统配置按钮 -->
                                    <div class="col-6">
                                        <button id="edit-config" class="btn btn-sm btn-outline-primary w-100" title="编辑系统配置" onclick="handleEditConfig()" style="display: inline-block;">
                                            <i class="bi bi-gear-fill me-1"></i>系统配置
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 运行时间 -->
                        <div class="stat-card" data-aos="fade-up" data-aos-delay="200">
                            <div class="dashboard-stat-icon" style="background: linear-gradient(135deg, #E74C3C, #C0392B);">
                                <i class="bi bi-clock-history"></i>
                            </div>
                            <h6 class="stat-title">运行时间</h6>
                            <div class="stat-value" id="uptime-value">{{ uptime|default('获取中...') }}</div>
                            <div class="stat-description" id="start-time">启动于 {{ start_time|default('--') }}</div>
                        </div>

                        <!-- 内存占用 -->
                        <div class="stat-card" data-aos="fade-up" data-aos-delay="300">
                            <div class="dashboard-stat-icon" style="background: linear-gradient(135deg, #9B59B6, #8E44AD);">
                                <i class="bi bi-memory"></i>
                            </div>
                            <h6 class="stat-title">内存占用</h6>
                            <div class="stat-value" id="memory-value">{{ memory_usage|default('获取中...') }}</div>
                            <div class="stat-description">
                                <div class="progress" style="height: 6px;">
                                    <div class="progress-bar bg-primary" role="progressbar" style="width: 0%" data-percent="{{ memory_percent|default(0) }}"></div>
                                </div>
                            </div>
                        </div>

                        <!-- CPU 使用率 -->
                        <div class="stat-card" data-aos="fade-up" data-aos-delay="400">
                            <div class="dashboard-stat-icon" style="background: linear-gradient(135deg, #F39C12, #D35400);">
                                <i class="bi bi-cpu"></i>
                            </div>
                            <h6 class="stat-title">CPU 使用率</h6>
                            <div class="stat-value" id="cpu-value">{{ cpu_percent|default('0') }}%</div>
                            <div class="stat-description">
                                <div class="progress" style="height: 6px;">
                                    <div class="progress-bar bg-warning" role="progressbar" style="width: 0%" data-percent="{{ cpu_percent|default(0) }}"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 二维码登录区域 - 仅在等待登录时显示 -->
    <!--
    <div id="qrcode-section" class="row" style="display: none;">
        <div class="col-lg-6 mx-auto">
            <div class="card dashboard-card" data-aos="fade-up">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="bi bi-qr-code me-2 text-primary"></i>微信登录
                    </h5>
                    <button id="refresh-qrcode" class="refresh-button" title="刷新二维码">
                        <i class="bi bi-arrow-clockwise"></i>
                    </button>
                </div>
                <div class="card-body text-center">
                    <div class="qrcode-container pulse">
                        <div class="qrcode-wrapper" id="qrcode-display">
                            <div class="d-flex justify-content-center align-items-center" style="height: 200px; width: 200px;">
                                <div class="spinner"></div>
                            </div>
                        </div>
                        <div class="qrcode-status" id="qrcode-status">加载中...</div>
                        <div class="qrcode-countdown" id="qrcode-countdown"></div>
                        <div class="qrcode-tip">请使用微信扫描二维码登录</div>
                    </div>

                    <div class="qrcode-form">
                        <div class="mb-3">
                            <label for="qrcode-url" class="form-label">手动输入二维码URL</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="qrcode-url" placeholder="请输入二维码URL">
                                <button class="btn btn-primary" type="button" id="load-qrcode">
                                    加载
                                </button>
                            </div>
                            <div class="form-text">当自动获取二维码失败时，可手动输入</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    -->

    <!-- 快速操作 -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card dashboard-card" data-aos="fade-up">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-lightning me-2 text-primary"></i>快速操作
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-3 col-sm-6">
                            <a href="/qrcode" class="btn btn-qrcode btn-primary w-100 p-3 d-flex flex-column align-items-center">
                                <i class="bi bi-qr-code mb-2" style="font-size: 2rem;"></i>
                                <span>查看登录二维码</span>
                            </a>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <a href="/contacts" class="btn btn-outline-secondary w-100 p-3 d-flex flex-column align-items-center">
                                <i class="bi bi-people mb-2" style="font-size: 2rem;"></i>
                                <span>联系人管理</span>
                            </a>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <a href="/plugins" class="btn btn-outline-secondary w-100 p-3 d-flex flex-column align-items-center">
                                <i class="bi bi-puzzle mb-2" style="font-size: 2rem;"></i>
                                <span>插件管理</span>
                            </a>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <a href="/system" class="btn btn-outline-secondary w-100 p-3 d-flex flex-column align-items-center">
                                <i class="bi bi-gear mb-2" style="font-size: 2rem;"></i>
                                <span>系统状态</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // 全局处理函数
    function handleSwitchAccount() {
        console.log('切换账号按钮被点击');
        // 显示确认对话框
        if (confirm('确定要切换微信账号吗？\n\n这将删除当前账号的登录信息，并需要重新扫码登录。')) {
            console.log('用户确认切换账号，发送请求');
            // 显示加载状态
            const switchBtn = document.getElementById('switch-account');
            switchBtn.disabled = true;
            switchBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>切换中...';

            // 发送请求删除 robot_stat.json 文件
            // 使用当前主机名和端口构建正确的URL
            const apiUrl = window.location.origin + '/api/switch_account';
            console.log('发送切换账号请求到:', apiUrl);

            fetch(apiUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                credentials: 'include'  // 确保发送认证Cookie
            })
            .then(response => {
                console.log('收到切换账号响应:', response);
                if (!response.ok) {
                    throw new Error('API响应状态码: ' + response.status);
                }
                return response.json();
            })
            .then(data => {
                console.log('切换账号响应数据:', data);
                if (data.success) {
                    // 成功切换账号
                    showToast('成功', '已切换账号，系统正在重启...', 'success');

                    // 显示加载中的提示
                    const loadingDiv = document.createElement('div');
                    loadingDiv.className = 'position-fixed top-0 start-0 w-100 h-100 d-flex justify-content-center align-items-center bg-dark bg-opacity-75';
                    loadingDiv.style.zIndex = '9999';
                    loadingDiv.innerHTML = `
                        <div class="bg-white p-4 rounded shadow-lg text-center">
                            <h4>系统重启中</h4>
                            <p class="mb-3">正在重启系统，请稍候...</p>
                            <div class="progress mb-3">
                                <div id="restart-progress" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
                            </div>
                            <p class="small text-muted">完成后将自动跳转到登录页面</p>
                        </div>
                    `;
                    document.body.appendChild(loadingDiv);

                    // 进度条动画
                    const progressBar = document.getElementById('restart-progress');
                    let progress = 0;
                    const progressInterval = setInterval(() => {
                        progress += 2;
                        if (progress > 100) {
                            clearInterval(progressInterval);
                            // 重启完成，跳转到登录页面
                            window.location.href = '/qrcode';
                        } else {
                            progressBar.style.width = `${progress}%`;
                        }
                    }, 200); // 20秒完成进度条
                } else {
                    // 切换账号失败
                    switchBtn.disabled = false;
                    switchBtn.innerHTML = '<i class="bi bi-arrow-repeat me-1"></i>切换账号';
                    showToast('错误', data.error || '切换账号失败', 'danger');
                }
            })
            .catch(error => {
                console.error('切换账号失败:', error);
                switchBtn.disabled = false;
                switchBtn.innerHTML = '<i class="bi bi-arrow-repeat me-1"></i>切换账号';
                showToast('错误', '切换账号请求失败: ' + error.message, 'danger');
            });
        }
    }

    function handleEditConfig() {
        console.log('系统配置按钮被点击');
        const configModal = new bootstrap.Modal(document.getElementById('config-modal'));
        const configEditor = document.getElementById('config-editor');
        const configLoading = document.getElementById('config-loading');
        const configError = document.getElementById('config-error');
        const configErrorMessage = document.getElementById('config-error-message');
        const configEditorContainer = document.getElementById('config-editor-container');

        // 显示加载状态
        configLoading.style.display = 'block';
        configError.style.display = 'none';
        configEditorContainer.style.display = 'none';

        // 显示模态窗口
        configModal.show();

        // 加载配置文件
        console.log('尝试加载配置文件');
        fetch('/api/files/read?path=main_config.toml')
            .then(response => {
                if (!response.ok) {
                    throw new Error('服务器响应状态码: ' + response.status);
                }
                return response.json();
            })
            .then(data => {
                if (data.success && data.content) {
                    // 显示配置文件内容
                    configEditor.value = data.content;
                    configLoading.style.display = 'none';
                    configEditorContainer.style.display = 'block';
                } else {
                    throw new Error(data.error || '加载配置文件失败');
                }
            })
            .catch(error => {
                console.error('加载配置文件失败:', error);
                configLoading.style.display = 'none';
                configError.style.display = 'block';
                configErrorMessage.textContent = '加载配置文件失败: ' + error.message;
            });
    }

    function handleSaveConfig() {
        console.log('保存配置按钮被点击');
        const configModal = new bootstrap.Modal(document.getElementById('config-modal'));
        const configEditor = document.getElementById('config-editor');
        const saveConfigBtn = document.getElementById('btn-save-config');

        // 显示加载状态
        saveConfigBtn.disabled = true;
        saveConfigBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>保存中...';

        // 发送请求保存配置文件
        console.log('尝试保存配置文件');
        fetch('/api/files/write', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                path: 'main_config.toml',
                content: configEditor.value
            }),
            credentials: 'include'  // 确保发送认证Cookie
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('服务器响应状态码: ' + response.status);
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                // 关闭模态窗口
                configModal.hide();

                // 显示成功提示
                showToast('成功', '配置文件已保存，系统将在 10 秒后重启', 'success');

                // 显示重启进度
                const loadingDiv = document.createElement('div');
                loadingDiv.className = 'restart-overlay';
                loadingDiv.innerHTML = `
                    <div class="restart-dialog">
                        <h4>系统重启中</h4>
                        <p>配置文件已保存，系统正在重启中，请稍候...</p>
                        <div class="progress mt-3">
                            <div id="restart-progress" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
                        </div>
                    </div>
                `;
                document.body.appendChild(loadingDiv);

                // 进度条动画
                const progressBar = document.getElementById('restart-progress');
                let progress = 0;
                const progressInterval = setInterval(() => {
                    progress += 2;
                    if (progress > 100) {
                        clearInterval(progressInterval);
                        // 重启完成，刷新页面
                        window.location.reload();
                    } else {
                        progressBar.style.width = `${progress}%`;
                    }
                }, 200); // 20秒完成进度条

                // 调用重启 API
                setTimeout(() => {
                    fetch('/api/system/restart', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        credentials: 'include'  // 确保发送认证Cookie
                    }).catch(error => {
                        console.error('重启系统失败:', error);
                        // 即使请求失败也继续显示进度条，因为服务器可能已经在重启中
                    });
                }, 1000);
            } else {
                // 保存失败
                saveConfigBtn.disabled = false;
                saveConfigBtn.innerHTML = '保存并重启';
                showToast('错误', data.error || '保存配置文件失败', 'danger');
            }
        })
        .catch(error => {
            console.error('保存配置文件失败:', error);
            saveConfigBtn.disabled = false;
            saveConfigBtn.innerHTML = '保存并重启';
            showToast('错误', '保存配置文件失败: ' + error.message, 'danger');
        });
    }

    // 加载系统配置
    function loadSystemConfig() {
        fetch('/api/files/read?path=main_config.toml')
            .then(response => {
                if (!response.ok) {
                    throw new Error('服务器响应状态码: ' + response.status);
                }
                return response.json();
            })
            .then(data => {
                if (data.success && data.content) {
                    // 解析TOML配置
                    const configText = data.content;

                    // 提取协议版本
                    const protocolMatch = configText.match(/version\s*=\s*"([^"]+)"/i);
                    const protocolVersion = protocolMatch ? protocolMatch[1] : '849';

                    // 提取消息过滤模式
                    const filterModeMatch = configText.match(/ignore-mode\s*=\s*"([^"]+)"/i);
                    const filterMode = filterModeMatch ? filterModeMatch[1] : 'None';

                    console.log('当前协议版本:', protocolVersion);
                    console.log('当前消息过滤模式:', filterMode);

                    // 存储到全局变量
                    window.currentConfig = {
                        protocolVersion,
                        filterMode,
                        configText
                    };

                    // 更新UI显示
                    updateProtocolUI(protocolVersion);
                    updateFilterModeUI(filterMode);
                } else {
                    throw new Error(data.error || '加载配置文件失败');
                }
            })
            .catch(error => {
                console.error('加载系统配置失败:', error);
                showToast('错误', '加载系统配置失败: ' + error.message, 'danger');
            });
    }

    // 更新协议版本UI
    function updateProtocolUI(version) {
        const protocolBtn = document.getElementById('current-protocol');
        if (protocolBtn) {
            protocolBtn.textContent = version + '协议';
        }
    }

    // 更新消息过滤模式UI
    function updateFilterModeUI(mode) {
        const filterBtn = document.getElementById('current-filter');
        if (filterBtn) {
            let displayText = '不过滤';
            if (mode === 'Whitelist') {
                displayText = '白名单模式';
            } else if (mode === 'Blacklist') {
                displayText = '黑名单模式';
            }
            filterBtn.textContent = displayText;
        }
    }

    // 保存设置
    function saveSettings(protocolVersion, filterMode) {
        // 如果没有当前配置，先加载
        if (!window.currentConfig) {
            loadSystemConfig();
            setTimeout(() => saveSettings(protocolVersion, filterMode), 1000);
            return;
        }

        let configText = window.currentConfig.configText;

        // 替换协议版本
        if (protocolVersion) {
            configText = configText.replace(/version\s*=\s*"[^"]+"/i, `version = "${protocolVersion}"`);
        }

        // 替换消息过滤模式
        if (filterMode) {
            configText = configText.replace(/ignore-mode\s*=\s*"[^"]+"/i, `ignore-mode = "${filterMode}"`);
        }

        // 发送保存请求
        fetch('/api/files/write', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                path: 'main_config.toml',
                content: configText
            }),
            credentials: 'include'
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('服务器响应状态码: ' + response.status);
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                // 更新全局变量
                if (protocolVersion) {
                    window.currentConfig.protocolVersion = protocolVersion;
                }
                if (filterMode) {
                    window.currentConfig.filterMode = filterMode;
                }
                window.currentConfig.configText = configText;

                // 显示成功提示
                showToast('成功', '设置已保存，需要重启系统生效', 'success');

                // 询问是否重启系统
                if (confirm('设置已保存，需要重启系统才能生效。\n\n是否立即重启系统？')) {
                    restartSystem();
                }
            } else {
                showToast('错误', data.error || '保存设置失败', 'danger');
            }
        })
        .catch(error => {
            console.error('保存设置失败:', error);
            showToast('错误', '保存设置失败: ' + error.message, 'danger');
        });
    }

    // 重启系统
    function restartSystem() {
        // 显示重启进度模态框
        const loadingDiv = document.createElement('div');
        loadingDiv.className = 'position-fixed top-0 start-0 w-100 h-100 d-flex justify-content-center align-items-center bg-dark bg-opacity-75';
        loadingDiv.style.zIndex = '9999';
        loadingDiv.innerHTML = `
            <div class="bg-white p-4 rounded shadow-lg text-center">
                <h4>系统重启中</h4>
                <p class="mb-3">正在重启系统，请稍候...</p>
                <div class="progress mb-3">
                    <div id="restart-progress" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
                </div>
                <p class="small text-muted">完成后将自动刷新页面</p>
            </div>
        `;
        document.body.appendChild(loadingDiv);

        // 进度条动画
        const progressBar = document.getElementById('restart-progress');
        let progress = 0;
        const progressInterval = setInterval(() => {
            progress += 2;
            if (progress > 100) {
                clearInterval(progressInterval);
                // 重启完成，刷新页面
                window.location.reload();
            } else {
                progressBar.style.width = `${progress}%`;
            }
        }, 200); // 20秒完成进度条

        // 发送重启系统请求
        fetch('/api/system/restart', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            credentials: 'include'
        }).catch(error => {
            console.error('重启系统失败:', error);
            // 即使请求失败也继续显示进度条，因为服务器可能已经在重启中
        });
    }

    document.addEventListener('DOMContentLoaded', function() {
        const statusDisplay = document.getElementById('bot-status-display');
        const statusDot = document.getElementById('status-dot');
        const statusText = document.getElementById('status-text');
        const statusDescription = document.getElementById('status-description');
        const refreshStatusBtn = document.getElementById('refresh-status');
        const qrcodeSection = document.getElementById('qrcode-section');
        const qrcodeDisplay = document.getElementById('qrcode-display');
        const qrcodeStatus = document.getElementById('qrcode-status');
        const qrcodeCountdown = document.getElementById('qrcode-countdown');
        const refreshQrcodeBtn = document.getElementById('refresh-qrcode');
        const qrcodeUrlInput = document.getElementById('qrcode-url');
        const loadQrcodeBtn = document.getElementById('load-qrcode');

        // 加载系统配置
        loadSystemConfig();

        // 确保切换账号和系统配置按钮始终显示
        document.getElementById('switch-account').style.display = 'inline-block';
        document.getElementById('edit-config').style.display = 'inline-block';

        // 协议选择事件委托
        document.addEventListener('click', function(event) {
            if (event.target.classList.contains('protocol-option')) {
                event.preventDefault();
                const protocolVersion = event.target.getAttribute('data-value');
                updateProtocolUI(protocolVersion);
                saveSettings(protocolVersion, null);
            }
        });

        // 消息过滤选择事件委托
        document.addEventListener('click', function(event) {
            if (event.target.classList.contains('filter-option')) {
                event.preventDefault();
                const filterMode = event.target.getAttribute('data-value');
                updateFilterModeUI(filterMode);
                saveSettings(null, filterMode);
            }
        });

        // 设置进度条宽度
        function setProgressBars() {
            document.querySelectorAll('.progress-bar[data-percent]').forEach(bar => {
                const percent = bar.getAttribute('data-percent');
                if (percent) {
                    bar.style.width = percent + '%';
                }
            });
        }

        // 初始化进度条
        setProgressBars();

        // 更新机器人状态显示
        function updateStatusDisplay(status) {
            console.log("更新状态显示:", status);
            statusText.textContent = status;

            // 艹，这里要处理带过期时间的状态显示
            let baseStatus = status;
            if (typeof status === 'string' && status.includes('(')) {
                // 提取基础状态，去掉过期时间部分
                baseStatus = status.split('(')[0].trim();
            }
            statusDisplay.dataset.status = baseStatus;

            // 清除所有状态类
            statusDot.classList.remove('online', 'offline', 'waiting', 'ready');
            statusDisplay.classList.remove('online', 'offline', 'waiting', 'ready');

            // 设置对应状态
            switch(baseStatus) {
                case 'online':
                case 'ready':  // 将ready也视为在线状态
                    statusDot.classList.add('online');
                    statusDisplay.classList.add('online');
                    statusDescription.textContent = status === 'ready' ? '机器人已准备就绪' : '机器人已登录并正常运行';
                    // qrcodeSection.style.display = 'none'; // 注释掉，不再显示二维码区域
                    // 显示机器人信息
                    document.getElementById('bot-info').style.display = 'block';


                    break;
                case 'offline':
                    statusDot.classList.add('offline');
                    statusDisplay.classList.add('offline');
                    statusDescription.textContent = '机器人目前离线，请检查连接';
                    // qrcodeSection.style.display = 'none'; // 注释掉，不再显示二维码区域
                    document.getElementById('bot-info').style.display = 'none';
                    // 在离线状态下也显示切换账号和系统配置按钮
                    document.getElementById('switch-account').style.display = 'inline-block';
                    document.getElementById('edit-config').style.display = 'inline-block';
                    break;
                case 'waiting_login':
                    statusDot.classList.add('waiting');
                    statusDisplay.classList.add('waiting');
                    statusDescription.textContent = '等待登录，请前往登录页面';
                    // qrcodeSection.style.display = 'block'; // 不再显示二维码区域
                    // loadQRCode(); // 不再加载二维码
                    document.getElementById('bot-info').style.display = 'none';
                    // 在未登录状态下也显示切换账号和系统配置按钮
                    document.getElementById('switch-account').style.display = 'inline-block';
                    document.getElementById('edit-config').style.display = 'inline-block';

                    break;
                default:
                    statusDot.classList.add('offline');
                    statusDisplay.classList.add('offline');
                    statusDescription.textContent = '未知状态: ' + status;
                    // qrcodeSection.style.display = 'none'; // 注释掉，不再显示二维码区域
                    document.getElementById('bot-info').style.display = 'none';
                    // 在未知状态下也显示切换账号和系统配置按钮
                    document.getElementById('switch-account').style.display = 'inline-block';
                    document.getElementById('edit-config').style.display = 'inline-block';
            }

            // 动画效果
            statusDisplay.classList.add('animate__animated', 'animate__pulse');
            setTimeout(() => {
                statusDisplay.classList.remove('animate__animated', 'animate__pulse');
            }, 1000);
        }

        // 获取机器人状态
        function getBotStatus() {
            // 修改为直接使用bot/status端点，因为api/status返回404
            fetch('/api/bot/status')
                .then(response => response.json())
                .then(data => {
                    console.log("获取到状态数据:", data);

                    // 提取状态数据，处理两种可能的数据结构
                    const statusData = data.data || data;

                    // 检查状态是否为undefined
                    if (!statusData || statusData.status === undefined) {
                        console.error("API返回的状态为undefined");
                        // 使用固定信息
                        useHardcodedInfo();
                        return;
                    }

                    // 更新状态 - 艹，这里也要处理过期时间显示
                    let displayStatus = statusData.status;
                    if (statusData.status === 'waiting_login' && statusData.expires_in !== undefined && statusData.expires_in !== "错误") {
                        const expiresIn = parseInt(statusData.expires_in);
                        if (!isNaN(expiresIn) && expiresIn > 0) {
                            displayStatus = `waiting_login (剩余${expiresIn}秒)`;
                        } else if (expiresIn <= 0) {
                            displayStatus = 'waiting_login (二维码已过期)';
                        }
                    }
                    updateStatusDisplay(displayStatus);

                    // 如果有机器人信息就显示
                    if (statusData.nickname) {
                        document.getElementById('bot-nickname').textContent = statusData.nickname;
                        document.getElementById('bot-info').style.display = 'block';
                    }
                    if (statusData.wxid || statusData.alias) {
                        document.getElementById('bot-wxid').textContent = statusData.wxid || statusData.alias || '-';
                        document.getElementById('bot-info').style.display = 'block';
                    }

                    document.getElementById('last-updated').textContent = '最后更新: ' + new Date().toLocaleTimeString();
                })
                .catch(error => {
                    console.error('获取状态失败:', error);
                    // 使用固定信息
                    useHardcodedInfo();
                });
        }

        // 使用硬编码信息作为备用
        function useHardcodedInfo() {
            console.log("使用固定信息备用方案");

            // 由于用户确认机器人已登录，设置状态为online而不是waiting_login
            updateStatusDisplay("online");

            // 设置用户信息
            document.getElementById('bot-nickname').textContent = "小球子";
            document.getElementById('bot-wxid').textContent = "wxid_l5im9jaxhr4412";
            document.getElementById('bot-info').style.display = 'block';

            // 更新最后更新时间
            document.getElementById('last-updated').textContent = '最后更新: ' + new Date().toLocaleTimeString();
        }

        // 加载QR码
        function loadQRCode() {
            fetch('/api/login/qrcode')
                .then(response => response.json())
                .then(data => {
                    if (data.qrcode_url) {
                        // 创建QR码图像
                        qrcodeDisplay.innerHTML = `<img src="${data.qrcode_url}" class="img-fluid" alt="微信登录二维码">`;
                        qrcodeStatus.textContent = '请使用微信扫描';

                        // 设置倒计时
                        let countdown = 300; // 默认5分钟
                        if (data.countdown) {
                            countdown = data.countdown;
                        }

                        startCountdown(countdown);
                    } else {
                        qrcodeDisplay.innerHTML = `<div class="alert alert-warning">无法获取二维码</div>`;
                        qrcodeStatus.textContent = '请尝试刷新或手动输入';
                    }
                })
                .catch(error => {
                    console.error('获取QR码失败:', error);
                    qrcodeDisplay.innerHTML = `<div class="alert alert-danger">获取二维码失败</div>`;
                    qrcodeStatus.textContent = '请尝试刷新或手动输入';
                });
        }

        // 倒计时
        let countdownTimer;
        function startCountdown(seconds) {
            clearInterval(countdownTimer);

            function updateCountdown() {
                if (seconds <= 0) {
                    clearInterval(countdownTimer);
                    qrcodeCountdown.textContent = '二维码已过期，请刷新';
                    qrcodeCountdown.classList.add('text-danger');
                    return;
                }

                const minutes = Math.floor(seconds / 60);
                const remainingSeconds = seconds % 60;
                qrcodeCountdown.textContent = `有效期: ${minutes}:${remainingSeconds < 10 ? '0' : ''}${remainingSeconds}`;
                qrcodeCountdown.classList.remove('text-danger');

                seconds--;
            }

            updateCountdown();
            countdownTimer = setInterval(updateCountdown, 1000);
        }

        // 手动加载QR码
        function manualLoadQRCode() {
            const url = qrcodeUrlInput.value.trim();
            if (!url) {
                showToast('错误', '请输入有效的二维码URL', 'danger');
                return;
            }

            qrcodeDisplay.innerHTML = `<img src="${url}" class="img-fluid" alt="微信登录二维码">`;
            qrcodeStatus.textContent = '请使用微信扫描';
            startCountdown(300); // 设置5分钟倒计时

            showToast('成功', '二维码已加载', 'success');
        }

        // 初始化页面
        getBotStatus();

        // 立即手动获取一次最新状态
        // 添加延迟确保DOM已完全加载
        setTimeout(function() {
            refreshStatusBtn.click();
        }, 1000);

        // 定时刷新状态和系统监控
        setInterval(getBotStatus, 30000);

        // 立即更新进度条
        setProgressBars();

        // 获取系统状态
        function getSystemStatus() {
            fetch('/api/system/status')
                .then(response => response.json())
                .then(data => {
                    console.log("系统状态数据:", data);
                    if (data.success && data.data) {
                        // 更新CPU和内存使用率
                        const systemData = data.data;

                        // 更新CPU使用率
                        if (systemData.cpu_percent !== undefined) {
                            const cpuPercent = systemData.cpu_percent;
                            document.getElementById('cpu-value').textContent = cpuPercent + '%';
                            const cpuBar = document.querySelector('.progress-bar.bg-warning');
                            cpuBar.setAttribute('data-percent', cpuPercent);
                            cpuBar.style.width = cpuPercent + '%';
                        }

                        // 更新内存使用率
                        if (systemData.memory_percent !== undefined) {
                            const memoryPercent = systemData.memory_percent.toFixed(2);
                            const memoryBar = document.querySelector('.progress-bar.bg-primary');
                            memoryBar.setAttribute('data-percent', memoryPercent);
                            memoryBar.style.width = memoryPercent + '%';

                            // 如果有memory_total和memory_used则显示更详细的内存信息
                            if (systemData.memory_used && systemData.memory_total) {
                                const memoryUsed = (systemData.memory_used / (1024 * 1024 * 1024)).toFixed(2);
                                const memoryTotal = (systemData.memory_total / (1024 * 1024 * 1024)).toFixed(2);
                                document.getElementById('memory-value').textContent = `${memoryUsed}GB / ${memoryTotal}GB`;
                            } else {
                                document.getElementById('memory-value').textContent = memoryPercent + '%';
                            }
                        }

                        // 如果有时间信息则更新
                        if (systemData.uptime) {
                            document.getElementById('uptime-value').textContent = systemData.uptime;
                        }
                        if (systemData.start_time) {
                            document.getElementById('start-time').textContent = '启动于 ' + systemData.start_time;
                        }
                    }
                })
                .catch(error => {
                    console.error('获取系统状态失败:', error);
                    // 使用备用显示
                    document.getElementById('cpu-value').textContent = '0%';
                    document.getElementById('memory-value').textContent = '获取失败';
                    document.getElementById('uptime-value').textContent = '获取失败';
                });
        }

        // 立即获取系统状态并定时更新
        getSystemStatus();
        setInterval(getSystemStatus, 30000);

        // 按钮事件处理
        refreshStatusBtn.addEventListener('click', function() {
            getBotStatus();
            refreshStatusBtn.classList.add('animate__animated', 'animate__rotateIn');
            setTimeout(() => {
                refreshStatusBtn.classList.remove('animate__animated', 'animate__rotateIn');
            }, 1000);
        });

        refreshQrcodeBtn.addEventListener('click', function() {
            loadQRCode();
            refreshQrcodeBtn.classList.add('animate__animated', 'animate__rotateIn');
            setTimeout(() => {
                refreshQrcodeBtn.classList.remove('animate__animated', 'animate__rotateIn');
            }, 1000);
        });

        loadQrcodeBtn.addEventListener('click', manualLoadQRCode);

        // 切换账号按钮事件已移动到updateStatusDisplay函数中

        // 在updateStatusDisplay函数中显示系统配置按钮

        // 初始化时显示toast功能
        window.showToast = function(title, message, type = 'info') {
            console.log(`Toast: ${title} - ${message} (${type})`);
            // 如果页面上有Toast组件，则使用它
            if (typeof bootstrap !== 'undefined' && bootstrap.Toast) {
                const toastEl = document.createElement('div');
                toastEl.className = `toast align-items-center text-white bg-${type} border-0`;
                toastEl.setAttribute('role', 'alert');
                toastEl.setAttribute('aria-live', 'assertive');
                toastEl.setAttribute('aria-atomic', 'true');

                toastEl.innerHTML = `
                    <div class="d-flex">
                        <div class="toast-body">
                            <strong>${title}</strong> ${message}
                        </div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                    </div>
                `;

                document.body.appendChild(toastEl);
                const toast = new bootstrap.Toast(toastEl);
                toast.show();

                // 自动移除
                toastEl.addEventListener('hidden.bs.toast', function () {
                    document.body.removeChild(toastEl);
                });
            }
        };
    });
</script>
<!-- 系统配置模态窗口 -->
<div class="modal fade" id="config-modal" tabindex="-1" aria-labelledby="config-modal-title" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="config-modal-title">系统配置</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="config-loading" class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2">正在加载配置文件...</p>
                </div>
                <div id="config-error" class="alert alert-danger" style="display: none;">
                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                    <span id="config-error-message">加载配置文件失败</span>
                </div>
                <div id="config-editor-container" style="display: none;">
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle-fill me-2"></i>
                        请谨慎编辑系统配置文件，错误的配置可能导致系统无法正常运行。保存后需要重启系统才能生效。
                    </div>
                    <textarea id="config-editor" class="form-control" style="height: 400px; font-family: monospace;"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="btn-save-config" onclick="handleSaveConfig()">保存并重启</button>
            </div>
        </div>
    </div>
</div>

{% endblock %}