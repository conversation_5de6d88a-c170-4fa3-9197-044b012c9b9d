{"version": "v1.4.4", "last_check": "2025-04-14T19:12:52.575429", "update_available": true, "latest_version": "v1.5.0", "update_url": "https://github.com/NanSsye/xxxbot-pad/", "update_description": "# 更新说明\n\n## 主要更新内容\n\n### 1. 修复账号切换后联系人数据问题\n- 修复了切换新账号后，联系人列表仍显示上一个账号数据的问题\n- 在账号切换时会自动清除联系人缓存，确保数据正确刷新\n- 切换账号后会自动从微信API获取最新联系人数据\n\n### 2. 修复联系人获取限制\n- 修复了之前只能获取100个联系人的限制\n- 现在可以获取全部联系人，无论数量多少\n- 对于好友数量较多的用户，获取过程可能需要较长时间，请耐心等待\n- 联系人加载过程中会显示进度条，方便了解加载状态\n\n### 3. 增强消息过滤功能\n- 增加了过滤公众号消息的功能，避免机器人回复公众号推送\n- 增加了过滤微信团队消息的功能，避免对系统消息进行回复\n- 消息过滤设置可在主配置文件(main_config.toml)中进行调整\n\n### 4. 联系人管理优化\n- 优化了联系人数据库存储和读取逻辑\n- 改进了联系人详情获取的并行处理机制，提高加载速度\n- 联系人分类现在能正确显示所有类型的联系人数量\n\n### 5. 性能优化\n- 优化了大量联系人情况下的前端渲染性能\n- 改进了联系人搜索功能，支持更快速的搜索结果\n- 减少了不必要的API请求，提高系统响应速度\n\n## 注意事项\n- 首次加载联系人时可能需要较长时间，特别是联系人数量较多的用户\n- 联系人详情获取是分批进行的，会在后台继续加载\n- 如遇到联系人数据不正确的情况，可点击刷新按钮强制更新\n\n## 后续计划\n- 进一步优化联系人管理界面\n- 增加更多消息过滤选项\n- 改进多账号切换体验\n\n感谢您的使用和支持！如有任何问题或建议，请随时反馈。"}