[DifyConversationManager]
# 基础设置
enable = true                                    # 是否启用插件
api-key = "app-9hiqqQIi8bZfvmaFc56WPyYH"       # Dify API密钥
base-url = "http://************:8080/v1"        # Dify API基础URL
http-proxy = ""                                 # HTTP代理设置（可选）

# 分页设置
default-page-size = 20                         # 默认每页显示的条数

# 命令设置
command-prefix = "/dify"
command-tip = """-----XYBot-----
📝 Dify对话管理助手

支持的命令：
/dify              # 显示此帮助菜单
/dify 列表         # 查看所有对话
/dify 历史 <ID>    # 查看指定对话历史
/dify 删除 <ID>    # 删除指定对话
/dify 删除对话     # ⚠️删除对话记录
                   # - 群聊中使用：删除本群所有对话
                   # - 私聊中使用：删除您的所有对话
/dify 重命名 <ID> <新名称>  # 重命名对话
"""

# 权限设置
price = 0
admin_ignore = true
whitelist_ignore = true

# 分页设置
max-page-size = 100                           # 最大每页显示的条数

# 显示设置
show-time = true                          # 是否显示时间戳
show-message-id = false                   # 是否显示消息ID
max-message-length = 500                  # 单条消息最大显示长度

# 消息格式设置
date-format = "%Y-%m-%d %H:%M"                # 时间显示格式
message-separator = "---------------"          # 消息分隔符

# 调试设置
debug = false                                 # 是否启用调试模式
log-level = "INFO"                           # 日志级别：DEBUG, INFO, WARNING, ERROR
