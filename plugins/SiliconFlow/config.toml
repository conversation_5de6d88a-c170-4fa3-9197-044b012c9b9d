[SiliconFlow]
enable = true                           # 是否启用此功能
handle_all_messages = false               # 是否处理所有消息（不需要命令前缀）
cleanup_days = 3       # 临时文件清理时间，单位 天


# 文本模型配置
[TextGeneration]
enable = true                           # 是否启用此功能
api-key = ""                            # 硅基流动API密钥，必填
base-url = "https://api.siliconflow.cn/v1"  # 硅基流动API地址
default-model = "deepseek-ai/DeepSeek-R1-Distill-Qwen-7B"          # 默认使用的模型
commands = ["硅基", "sf", "SiliconFlow"]    # 触发插件的命令
# 高级设置
max_tokens = 4096                       # 最大token数
temperature = 0.7                       # 温度参数
top_p = 0.7                             # Top-p采样
top_k = 50                              # Top-k采样
frequency_penalty = 0.5                 # 频率惩罚


# 图片生成配置
[ImageGeneration]
enable = true                           # 是否启用此功能
api-key = ""                            # 硅基流动API密钥，必填
base-url = "https://api.siliconflow.cn/v1"  # 硅基流动API地址
image-model = "Kwai-Kolors/Kolors"
image-size = "1024x1024"
image-steps = 20
image-guidance-scale = 7.5
image-commands = ["画图", "绘图", "生成图片"]
image-batch-size = 4


# 视觉模型配置
[VisionRecognition]
enable = true                           # 是否启用此功能
api-key = ""                            # 硅基流动API密钥，必填
base-url = "https://api.siliconflow.cn/v1"  # 硅基流动API地址
vision-model = "Qwen/Qwen2.5-VL-72B-Instruct"
auto_analyze_images = true
vision_prompt = "请详细描述这张图片的内容"
