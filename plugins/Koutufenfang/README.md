# Koutufenfang 插件

## 简介

Koutufenfang（口吐芬芳）是一个有趣的群聊互动插件，可以在群聊中通过@群员并使用特定关键词来触发"口吐芬芳"功能。插件支持两种火力等级：轻度和重度，每个群聊的配置独立管理。

## 功能特性

- 🎯 **精准触发**：通过@群员 + 触发关键词来激活功能
- 🔥 **火力等级**：支持轻度（min）和重度（max）两种等级
- 👥 **群聊独立**：每个群聊的配置完全独立
- 🛡️ **权限控制**：只有管理员和白名单用户可以操作火力全开
- 📊 **数据库驱动**：从maren.db数据库随机选择内容
- 🚫 **私聊忽略**：私聊中自动忽略相关指令（除插件开启禁用外）

## 使用方法

### 基本功能

在群聊中发送以下格式的消息来触发口吐芬芳，支持多种灵活的格式：

#### 📝 支持的消息格式

**格式1：关键词在前**
```
骂他 @群员
骂她 @群员
```

**格式2：关键词在后**
```
@群员 骂他
@群员 骂她
```

**格式3：无空格间隔**
```
骂他@群员
骂她@群员
```

**格式4：多个@用户**
```
骂他@群员1@群员2
骂她 @群员1@群员2
@群员1@群员2 骂他
```

#### ✅ 有效格式示例

**单个用户**：
- `骂他 @张三` ✅
- `骂她 @李四` ✅
- `骂他@王五` ✅ (无空格)
- `骂他   @赵六` ✅ (多个空格)
- `@张三 骂他` ✅ (传统格式)
- `@李四  骂她` ✅ (传统格式+多空格)

**多个用户**：
- `骂他@张三@李四` ✅ (无空格多用户)
- `骂她 @王五@赵六` ✅ (有空格多用户)
- `@张三@李四 骂他` ✅ (传统格式多用户)

#### ❌ 无效格式示例

- `骂他吧 @张三` ❌ (关键词不完全匹配)
- `快骂他 @张三` ❌ (关键词不在开头)
- `@张三 快骂他` ❌ (关键词不在结尾)
- `骂他` ❌ (缺少@用户)
- `@张三` ❌ (缺少关键词)

机器人会随机从数据库中选择一条对应等级的内容，并@被@的群员进行回复。支持同时@多个用户。

### 火力全开控制

**开启火力全开（重度模式）：**
```
火力全开
开启重度
```

**关闭火力全开（轻度模式）：**
```
关闭火力全开
收敛点
开启轻度
```

### 白名单管理

管理员可以通过以下指令添加用户到白名单：
```
@群员 火力全开加白
```

## 权限说明

- **管理员**：可以操作火力全开指令和管理白名单
- **白名单用户**：可以操作火力全开指令
- **普通用户**：只能使用基本的口吐芬芳功能

## 配置说明

配置文件 `config.toml` 包含以下设置：

```toml
[Koutufenfang]
# 插件开关（全局）
enable = true

# 触发关键词
trigger_keywords = ["骂他", "骂她"]

# 火力全开相关指令
fire_power_commands = {
    "enable" = ["火力全开", "开启重度"],
    "disable" = ["关闭火力全开", "收敛点", "开启轻度"]
}

# 白名单管理指令
whitelist_command = "火力全开加白"

# 数据库配置
[Koutufenfang.database]
db_path = "database/maren.db"
table_name = "main"

# 默认配置
[Koutufenfang.defaults]
fire_power_enabled = false
```

## 数据库结构

插件使用 `database/maren.db` 数据库中的 `main` 表：

| 字段 | 类型 | 说明 |
|------|------|------|
| id | integer | 主键，序号 |
| text | varchar(255) | 正文内容 |
| level | varchar(255) | 火力等级（min=轻度，max=重度） |

## 工作流程

1. **收到消息** → 检查是否为群聊消息
2. **触发功能关键词和@群员** → 验证消息格式
3. **是否火力全开** → 确定使用的等级（min/max）
4. **是否启用插件** → 检查插件开关状态
5. **随机选取对应等级的正文数据** → 从数据库获取内容
6. **回复消息** → @被@的人发送内容

## 注意事项

- 插件仅在群聊中生效，私聊会自动忽略相关指令
- 每个群聊的火力全开状态和白名单独立管理
- 默认情况下火力全开为关闭状态（轻度模式）
- 管理员列表从主配置文件 `main_config.toml` 中读取

## 安装方法

1. 将插件文件夹复制到 `plugins/Koutufenfang/` 目录
2. 确保 `database/maren.db` 文件存在且包含正确的数据结构
3. 根据需要修改 `config.toml` 配置文件
4. 重启机器人或重新加载插件

## 版本信息

- **版本**：1.0.0
- **作者**：AI Assistant
- **依赖**：KeyvalDB, SQLite3

## 故障排除

### 插件没有响应

1. **检查插件是否启用**：
   - 确认 `config.toml` 中 `enable = true`
   - 检查插件是否在禁用列表中

2. **检查消息格式**：
   - 支持多种格式：`骂他 @群员`、`@群员 骂他`、`骂他@群员` 等
   - 关键词必须完全匹配：`骂他` 或 `骂她`
   - 确保在群聊中使用，不是私聊
   - 支持同时@多个用户

3. **检查日志**：
   - 查看机器人日志中是否有 `Koutufenfang收到群聊消息` 的调试信息
   - 查看是否有 `触发口吐芬芳功能` 的日志

4. **检查数据库**：
   - 确认 `database/maren.db` 文件存在
   - 确认数据库中有 `main` 表且包含数据

### 权限问题

- 火力全开指令只有管理员和白名单用户可以使用
- 白名单管理只有管理员可以操作
- 管理员列表在 `main_config.toml` 中配置

## 免责声明

本插件仅供娱乐和学习交流使用，请文明使用，遵守相关法律法规和社区规范。使用者需对使用本插件产生的后果承担责任。
