import tomllib
import sqlite3
import random
import re
import os
from loguru import logger

from WechatAPI import WechatAPIClient
from utils.decorators import *
from utils.plugin_base import PluginBase
from database.keyvalDB import KeyvalDB


class Koutufenfang(PluginBase):
    description = "口吐芬芳插件"
    author = "AI Assistant"
    version = "1.0.0"

    def __init__(self):
        super().__init__()

        # 加载配置文件
        config_path = os.path.join(os.path.dirname(__file__), "config.toml")
        try:
            with open(config_path, "rb") as f:
                plugin_config = tomllib.load(f)

            config = plugin_config["Koutufenfang"]

            self.enable = config["enable"]
            self.trigger_keywords = config["trigger_keywords"]
            self.fire_power_commands = config["fire_power_commands"]
            self.whitelist_command = config["whitelist_command"]

            # 数据库配置
            db_config = config["database"]
            self.db_path = db_config["db_path"]
            self.table_name = db_config["table_name"]

            # 默认配置
            defaults = config["defaults"]
            self.default_fire_power = defaults["fire_power_enabled"]

            logger.success("Koutufenfang插件配置加载成功")

        except Exception as e:
            logger.error(f"加载Koutufenfang配置文件失败: {str(e)}")
            self.enable = False

    async def async_init(self):
        """异步初始化"""
        # 初始化KeyvalDB
        self.keyval_db = KeyvalDB()
        await self.keyval_db.initialize()
        logger.info("Koutufenfang插件异步初始化完成")

    def _get_group_config_key(self, group_wxid: str, config_type: str) -> str:
        """获取群聊配置的键名"""
        return f"koutufenfang:{group_wxid}:{config_type}"

    async def _get_group_fire_power_status(self, group_wxid: str) -> bool:
        """获取群聊火力全开状态"""
        key = self._get_group_config_key(group_wxid, "fire_power")
        result = await self.keyval_db.get(key)
        # 确保返回布尔值，处理字符串形式的存储
        if result is None:
            return self.default_fire_power
        elif isinstance(result, bool):
            return result
        elif isinstance(result, str):
            return result.lower() in ('true', '1', 'yes', 'on')
        else:
            return bool(result)

    async def _set_group_fire_power_status(self, group_wxid: str, enabled: bool):
        """设置群聊火力全开状态"""
        key = self._get_group_config_key(group_wxid, "fire_power")
        # 将布尔值转换为字符串存储，避免Pydantic验证错误
        await self.keyval_db.set(key, str(enabled))

    async def _get_group_whitelist(self, group_wxid: str) -> list:
        """获取群聊白名单"""
        key = self._get_group_config_key(group_wxid, "whitelist")
        result = await self.keyval_db.get(key)
        # 处理不同类型的存储格式
        if result is None:
            return []
        elif isinstance(result, list):
            return result
        elif isinstance(result, str):
            # 如果是字符串，尝试解析为列表
            try:
                import json
                return json.loads(result)
            except:
                return [result] if result else []
        else:
            return []

    async def _add_to_whitelist(self, group_wxid: str, user_wxid: str):
        """添加用户到白名单"""
        whitelist = await self._get_group_whitelist(group_wxid)
        if user_wxid not in whitelist:
            whitelist.append(user_wxid)
            key = self._get_group_config_key(group_wxid, "whitelist")
            # 将列表转换为JSON字符串存储，避免Pydantic验证错误
            import json
            await self.keyval_db.set(key, json.dumps(whitelist))

    def _get_random_text_from_db(self, level: str) -> str:
        """从数据库随机获取指定等级的文本"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 查询指定等级的所有记录
            cursor.execute(f"SELECT text FROM {self.table_name} WHERE level = ?", (level,))
            results = cursor.fetchall()

            conn.close()

            if results:
                # 随机选择一条记录
                random_text = random.choice(results)[0]
                return random_text
            else:
                logger.warning(f"数据库中没有找到level为{level}的记录")
                return "暂无相关内容"

        except Exception as e:
            logger.error(f"从数据库获取文本失败: {str(e)}")
            return "获取内容失败"

    def _extract_at_users(self, message: dict) -> list:
        """提取消息中@的用户列表"""
        at_users = []

        # 输出原始消息内容中的相关字段
        logger.debug(f"原始消息AtUserList: {message.get('AtUserList', 'None')}, Ats: {message.get('Ats', 'None')}")

        # 从消息对象中提取被@用户，参考MemeGen插件的实现
        if "AtUserList" in message and isinstance(message["AtUserList"], list):
            at_users = message["AtUserList"]
            logger.debug(f"从AtUserList获取到的@用户: {at_users}")
        elif "Ats" in message and isinstance(message["Ats"], list):
            at_users = message["Ats"]
            logger.debug(f"从Ats获取到的@用户: {at_users}")

        # 检查at_users是否为空
        if not at_users:
            logger.debug("未能从消息中提取到@用户")

        return at_users

    def _is_admin_or_whitelist(self, user_wxid: str, group_wxid: str, admins: list, whitelist: list) -> bool:
        """检查用户是否为管理员或在白名单中"""
        return user_wxid in admins or user_wxid in whitelist

    @on_text_message(priority=60)
    async def handle_text_message(self, bot: WechatAPIClient, message: dict):
        """处理文本消息"""
        try:
            if not self.enable:
                return True

            # 只处理群聊消息
            if not message.get("IsGroup", False):
                return True

            content = str(message.get("Content", "")).strip()
            from_wxid = message.get("FromWxid", "")
            sender_wxid = message.get("SenderWxid", "")

            # 添加调试日志
            logger.info(f"Koutufenfang收到群聊消息: 群={from_wxid}, 发送者={sender_wxid}, 内容='{content}'")

            # 获取管理员列表
            with open("main_config.toml", "rb") as f:
                main_config = tomllib.load(f)
            admins = main_config["XYBot"]["admins"]

            # 处理火力全开相关指令
            if await self._handle_fire_power_commands(bot, message, content, from_wxid, sender_wxid, admins):
                return False

            # 处理白名单添加指令
            if await self._handle_whitelist_command(bot, message, content, from_wxid, sender_wxid, admins):
                return False

            # 处理口吐芬芳功能
            if await self._handle_koutufenfang(bot, message, content, from_wxid, sender_wxid):
                return False

            return True

        except Exception as e:
            logger.error(f"Koutufenfang插件处理消息时出错: {str(e)}")
            return True  # 出错时允许后续插件处理

    async def _handle_fire_power_commands(self, bot: WechatAPIClient, message: dict, content: str,
                                        from_wxid: str, sender_wxid: str, admins: list) -> bool:
        """处理火力全开相关指令"""
        # 检查是否为火力全开指令
        is_enable_command = any(cmd in content for cmd in self.fire_power_commands["enable"])
        is_disable_command = any(cmd in content for cmd in self.fire_power_commands["disable"])

        if not (is_enable_command or is_disable_command):
            return False

        # 检查权限
        whitelist = await self._get_group_whitelist(from_wxid)
        if not self._is_admin_or_whitelist(sender_wxid, from_wxid, admins, whitelist):
            await bot.send_at_message(from_wxid, "❌ 只有管理员和白名单用户才能操作火力全开指令", [sender_wxid])
            return True

        # 执行指令
        if is_enable_command:
            await self._set_group_fire_power_status(from_wxid, True)
            await bot.send_at_message(from_wxid, "🔥 火力全开已启用！", [sender_wxid])
        else:
            await self._set_group_fire_power_status(from_wxid, False)
            await bot.send_at_message(from_wxid, "😌 火力全开已关闭，切换为轻度模式", [sender_wxid])

        return True

    async def _handle_whitelist_command(self, bot: WechatAPIClient, message: dict, content: str,
                                      from_wxid: str, sender_wxid: str, admins: list) -> bool:
        """处理白名单添加指令"""
        if self.whitelist_command not in content:
            return False

        # 只有管理员可以添加白名单
        if sender_wxid not in admins:
            await bot.send_at_message(from_wxid, "❌ 只有管理员才能添加白名单", [sender_wxid])
            return True

        # 提取@的用户
        at_users = self._extract_at_users(message)
        if not at_users:
            await bot.send_at_message(from_wxid, "❌ 请@要添加到白名单的用户", [sender_wxid])
            return True

        # 添加到白名单
        for user_wxid in at_users:
            await self._add_to_whitelist(from_wxid, user_wxid)

        await bot.send_at_message(from_wxid, f"✅ 已将 {len(at_users)} 个用户添加到火力全开白名单", [sender_wxid])
        return True

    def _parse_message_format(self, content: str, at_users: list) -> tuple:
        """解析消息格式，支持多种格式

        支持的格式：
        1. "@群员 骂他" - @在前，关键词在后
        2. "骂他 @群员" - 关键词在前，@在后
        3. "骂他@群员" - 关键词和@无空格
        4. "骂他@群员1@群员2" - 关键词和多个@无空格
        5. "骂他 @群员1@群员2" - 关键词和多个@有空格

        Returns:
            tuple: (是否匹配, 匹配的关键词)
        """
        if not at_users:
            return False, None

        content = content.strip()

        # 方法1: 检查关键词是否在消息开头（支持"骂他 @群员"、"骂他@群员"等格式）
        for keyword in self.trigger_keywords:
            if content.startswith(keyword):
                # 检查关键词后面是否紧跟@或空格+@
                after_keyword = content[len(keyword):]
                if after_keyword.startswith('@') or after_keyword.startswith(' @') or re.match(r'^\s+@', after_keyword):
                    logger.debug(f"匹配格式1: 关键词'{keyword}'在开头")
                    return True, keyword

        # 方法2: 检查关键词是否在消息结尾（支持"@群员 骂他"格式）
        content_parts = content.split()
        if len(content_parts) >= 2:
            last_word = content_parts[-1]
            if last_word in self.trigger_keywords:
                logger.debug(f"匹配格式2: 关键词'{last_word}'在结尾")
                return True, last_word

        return False, None

    async def _handle_koutufenfang(self, bot: WechatAPIClient, message: dict, content: str,
                                 from_wxid: str, sender_wxid: str) -> bool:
        """处理口吐芬芳功能"""
        # 检查是否@了人
        at_users = self._extract_at_users(message)
        if not at_users:
            return False

        # 解析消息格式
        is_match, keyword = self._parse_message_format(content, at_users)
        if not is_match:
            return False

        logger.info(f"触发口吐芬芳功能: 群聊={from_wxid}, 发送者={sender_wxid}, 关键词={keyword}, @用户={at_users}")

        # 获取火力全开状态
        fire_power_enabled = await self._get_group_fire_power_status(from_wxid)
        level = "max" if fire_power_enabled else "min"

        logger.info(f"当前火力等级: {level} (火力全开: {fire_power_enabled})")

        # 从数据库获取随机文本
        random_text = self._get_random_text_from_db(level)

        # 发送消息，@被@的人
        await bot.send_at_message(from_wxid, random_text, at_users)

        return True
