# GuessIdioms2 插件测试指南

## 🧪 功能测试清单

### 1. 基础游戏功能测试

#### 1.1 关键词触发测试
在群聊中发送以下指令测试游戏启动：
- `测试猜成语`
- `测试成语游戏` 
- `测试开始猜成语`

**预期结果**：
- 机器人发送游戏开始消息
- 显示答题时间、奖励积分等信息
- 发送成语图片

#### 1.2 抢答功能测试
游戏开始后：
- 发送正确答案测试抢答成功
- 发送错误答案测试忽略机制

**预期结果**：
- 正确答案：立即结束游戏，显示详细答案信息和排行榜
- 错误答案：被忽略，游戏继续

#### 1.3 详细答案信息测试
游戏结束后检查是否显示：
- ✅ 正确答案
- 📖 拼音
- 💡 解释
- 📚 出处
- 🌰 例子

### 2. 管理功能测试（需要管理员权限）

#### 2.1 白名单管理测试

**添加白名单**：
```
添加猜成语白名单
```

**删除白名单**：
```
删除猜成语白名单
```

**预期结果**：
- 管理员：操作成功，显示确认消息
- 非管理员：显示权限不足提示

#### 2.2 手动测试定时功能

**测试指令**：
```
测试定时猜成语
```

**预期结果**：
- 管理员：显示测试报告，包含触发群聊数量统计
- 非管理员：显示权限不足提示
- 根据配置向所有允许的群聊发送游戏

### 3. 定时功能测试

#### 3.1 配置验证
检查 `config.toml` 中的设置：
```toml
enable_timer_trigger = true
timer_type = "daily"
daily_hours = [9, 15, 20]
apply_to_all_groups = true
```

#### 3.2 定时触发测试
- 等待配置的时间点（如9点、15点、20点）
- 或使用"测试定时猜成语"手动触发

**预期结果**：
- 在指定时间自动向所有群聊发送游戏
- 日志中记录触发信息

### 4. 权限控制测试

#### 4.1 管理员权限测试
使用管理员账号测试：
- ✅ 添加猜成语白名单
- ✅ 删除猜成语白名单  
- ✅ 测试定时猜成语

#### 4.2 普通用户权限测试
使用普通用户账号测试：
- ❌ 添加猜成语白名单（应被拒绝）
- ❌ 删除猜成语白名单（应被拒绝）
- ❌ 测试定时猜成语（应被拒绝）
- ✅ 参与游戏（应正常）

### 5. 配置模式测试

#### 5.1 全群聊模式测试
```toml
apply_to_all_groups = true
```
- 定时触发应覆盖所有群聊
- 手动测试应显示"所有群聊"模式

#### 5.2 白名单模式测试
```toml
apply_to_all_groups = false
```
- 定时触发仅覆盖白名单群聊
- 手动测试应显示"白名单群聊"模式

## 🔍 故障排除

### 常见问题

1. **定时功能不工作**
   - 检查 `enable_timer_trigger = true`
   - 检查时间配置是否正确
   - 查看日志中的定时触发记录

2. **权限控制不生效**
   - 检查 `main_config.toml` 中的管理员列表
   - 确认使用的是正确的wxid

3. **API接口错误**
   - 检查网络连接
   - 验证AppSecret是否正确
   - 查看错误日志

### 日志检查
关注以下日志信息：
- 定时触发记录
- API请求结果
- 权限验证结果
- 游戏状态变化

## 📊 测试报告模板

### 基础功能
- [ ] 关键词触发游戏
- [ ] 抢答功能正常
- [ ] 详细答案信息显示
- [ ] 排行榜功能

### 管理功能
- [ ] 添加白名单（管理员）
- [ ] 删除白名单（管理员）
- [ ] 手动测试定时功能（管理员）
- [ ] 权限控制正常

### 定时功能
- [ ] 自动定时触发
- [ ] 全群聊模式
- [ ] 白名单模式
- [ ] 触发统计准确

### API功能
- [ ] 成语数据获取正常
- [ ] 图片下载正常
- [ ] 详细信息完整

---

**测试完成时间**: ___________  
**测试人员**: ___________  
**版本**: v2.2.0  
**测试结果**: ___________
