# 猜成语2.0插件

通过显示成语相关的图片，让群成员猜测对应的成语。

## 功能特性

- ✅ **抢答模式**：第一个答对的人立刻获胜并结束游戏
- ✅ 支持关键词触发和定时触发两种方式
- ✅ 每次答题有时间限制（默认3分钟）
- ✅ 答题结束后根据答对题目数量排名并发送排行榜
- ✅ 群员答错忽略不响应，答对奖励积分
- ✅ 每个群聊的排名不互通，区分开
- ✅ 支持群聊白名单管理
- ✅ 可自定义触发方式、时间范围、奖励积分等

## 配置说明

### 基本配置
- `enable`: 是否启用插件
- `enable_keyword_trigger`: 是否开启关键词指令触发
- `enable_timer_trigger`: 是否开启定时触发

### 触发设置
- `keyword_commands`: 关键词触发指令列表
- `timer_type`: 定时触发类型（daily/monthly/specific）
- `daily_hours`: 每日触发的小时数
- `monthly_days`: 每月触发的日期
- `specific_times`: 具体时间列表

### 游戏设置
- `answer_time_limit`: 答题时间限制（分钟）
- `reward_points`: 答对奖励积分数
- `leaderboard_top_count`: 排行榜显示前几名

### 群聊管理
- `apply_to_all_groups`: 是否应用到所有群聊
- 如果为false，则只在白名单群聊中生效

## 使用方法

### 群聊指令
- `猜成语` / `成语游戏` / `开始猜成语` - 开始游戏

### 管理指令（仅管理员）
- `添加猜成语白名单` - 将当前群聊加入白名单
- `删除猜成语白名单` - 将当前群聊从白名单移除
- `测试定时猜成语` - 手动触发定时功能（测试用）

### 游戏流程（抢答模式）
1. 发送触发指令或定时触发
2. 机器人发送成语图片
3. 群成员抢答猜测成语
4. **第一个答对的用户立刻获胜并结束游戏**
5. 获胜者获得积分奖励
6. 立刻显示详细答案信息（包含拼音、解释、出处、例子）和排行榜

### 排行榜格式
```
【猜成语-最新排名Top10】
排名 昵称 答对次数
1👑.群成员1 34
2🥈.群成员2 19
3🥉.群成员3 11
4😄.虚以待位 0
5😃.虚以待位 0
6😁.虚以待位 0
7😆.虚以待位 0
8😊.虚以待位 0
9😍.虚以待位 0
10😋.虚以待位 0
```

## 接口说明
- 请求URL：https://api.dudunas.top/api/chengyu
- 返回格式：JSON
- 请求方式：GET
- 请求参数：AppSecret（必填）
- 返回示例：
```json
{
  "code": 200,
  "msg": "Success",
  "result": {
    "chengyu": "天下为公",
    "pingyin": "tiān xià wéi gōng",
    "jieshi": "原意是天下是公众的，天子之位，传贤而不传子，后成为一种美好社会的政治理想。",
    "chuchu": "《礼记·礼运》大道之行也，天下为公。",
    "lizi": "万古千秋业，～器。★陈毅《湖海诗社开征引》诗",
    "imglink": "https://imgstore.dudunas.top/vipbucket/image/2024/12/13/675b7f33b3b94.jpg"
  }
}
```

## 文件结构
```
plugins/GuessIdioms2.0/
├── __init__.py          # 初始化文件
├── main.py              # 主插件文件
├── config.toml          # 配置文件
├── whitelist.txt        # 群聊白名单
├── data/                # 数据存储目录
│   └── leaderboard_*.json  # 各群聊排行榜数据
└── README.md            # 说明文档
```

## 注意事项
1. 插件只在群聊中生效
2. 需要将群聊添加到白名单才能使用（除非设置apply_to_all_groups=true）
3. 答案必须完全匹配，不支持模糊匹配
4. 每个用户在同一轮游戏中只能答对一次
5. 排行榜数据按群聊分别存储，互不影响

## 版本信息
- 版本：2.0.0
- 作者：Assistant
- 更新日期：2025-07-09
