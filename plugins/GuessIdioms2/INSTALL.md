# 猜成语2插件安装说明

## 安装完成 ✅

插件已成功创建并配置完成！

## 文件结构

```
plugins/GuessIdioms2/
├── __init__.py                    # 插件初始化文件
├── main.py                        # 主插件代码
├── config.toml                    # 配置文件
├── whitelist.txt                  # 群聊白名单
├── README.md                      # 详细说明文档
├── USAGE.md                       # 使用指南
├── INSTALL.md                     # 安装说明（本文件）
└── data/                          # 数据存储目录
    └── leaderboard_*.json         # 各群聊排行榜数据
```

## 功能特性 ✅

- ✅ 关键词触发游戏（猜成语、成语游戏、开始猜成语）
- ✅ 定时触发游戏（支持每日、每月、特定时间）
- ✅ 答题时间限制（默认3分钟，可配置）
- ✅ 积分奖励系统（答对奖励积分）
- ✅ 排行榜功能（显示前10名，按群聊分别统计，不足时显示"虚以待位"）
- ✅ 群聊白名单管理
- ✅ 精确答案匹配
- ✅ 防重复答题（每轮游戏每人只能答对一次）
- ✅ 图片自动下载和发送
- ✅ 完整的错误处理

## 快速启用

### 1. 启用插件
编辑 `config.toml` 文件：
```toml
enable = true
```

### 2. 添加群聊到白名单
在目标群聊中发送：
```
添加猜成语白名单
```

### 3. 开始游戏
在群聊中发送任一指令：
```
猜成语
成语游戏
开始猜成语
```

## 配置说明

### 基本配置
- `enable`: 是否启用插件
- `enable_keyword_trigger`: 是否启用关键词触发
- `enable_timer_trigger`: 是否启用定时触发

### 游戏设置
- `answer_time_limit`: 答题时间限制（分钟）
- `reward_points`: 答对奖励积分数
- `keyword_commands`: 触发指令列表

### 定时设置
- `timer_type`: 定时类型（daily/monthly/specific）
- `daily_hours`: 每日触发小时数
- `monthly_days`: 每月触发日期

## 接口信息

### 新版API接口
- **请求URL**: https://api.dudunas.top/api/chengyu
- **请求方式**: GET
- **返回格式**: JSON
- **请求参数**: AppSecret（必填，固定值：6c3b0ca62dd045e9dcf15e3dcbe4c03a）

### 返回数据格式
```json
{
  "code": 200,
  "msg": "Success",
  "result": {
    "chengyu": "成语答案",
    "pingyin": "拼音",
    "jieshi": "解释",
    "chuchu": "出处",
    "lizi": "例子",
    "imglink": "题目图片链接"
  }
}
```

### 功能增强
- ✅ 游戏结束后显示成语详细信息（拼音、解释、出处、例子）
- ✅ 使用新的JSON格式API接口
- ✅ 更稳定的数据获取和解析

- **API地址**: http://api.xingchenfu.xyz/API/LookIdiom.php
- **请求方式**: GET
- **返回格式**: TEXT
- **示例返回**: ±img=https://api.xingchenfu.xyz/API/data/LookIdiom/img/001.jpg± 答案：一刀两断

## 测试状态

- ✅ 代码语法检查通过
- ✅ 配置文件格式正确
- ✅ API接口测试成功
- ✅ 数据解析功能正常

## 注意事项

1. 插件只在群聊中生效
2. 需要将群聊添加到白名单
3. 答案必须完全匹配
4. 每个群聊的排行榜独立
5. 需要网络连接以获取成语图片

## 版本信息

- **插件名称**: 猜成语2
- **版本号**: 2.0.0
- **作者**: Assistant
- **创建日期**: 2025-07-09
- **兼容性**: XYBot框架

## 支持

如有问题，请查看：
1. README.md - 详细功能说明
2. USAGE.md - 使用指南
3. 日志文件 - 错误信息
4. 配置文件 - 参数设置

插件安装完成，可以开始使用！🎉
