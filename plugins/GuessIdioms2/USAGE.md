# 猜成语2.0插件使用指南

## 快速开始

### 1. 启用插件
编辑 `config.toml` 文件，将 `enable = true`

### 2. 添加群聊白名单
在群聊中发送：`添加猜成语白名单`

### 3. 开始游戏
在群聊中发送：`猜成语` 或 `成语游戏` 或 `开始猜成语`

## 游戏流程（抢答模式）

1. **触发游戏**
   - 群成员发送触发指令
   - 或者定时自动触发（需要配置）

2. **显示题目**
   - 机器人发送游戏开始消息
   - 发送成语相关图片

3. **抢答阶段**
   - 群成员在限定时间内（默认3分钟）抢答猜测成语
   - **第一个答对的用户立刻获胜并结束游戏**
   - 获胜者立即获得积分奖励

4. **游戏结束**
   - 抢答成功或时间到后显示正确答案
   - 立刻发送排行榜（显示前10名）

## 配置选项详解

### 基本设置
```toml
enable = true                    # 启用插件
enable_keyword_trigger = true    # 启用关键词触发
enable_timer_trigger = false     # 启用定时触发
```

### 触发指令
```toml
keyword_commands = ["猜成语", "成语游戏", "开始猜成语"]
```

### 定时触发
```toml
timer_type = "daily"             # 触发类型：daily/monthly/specific
daily_hours = [10, 15, 20]       # 每日触发时间（小时）
monthly_days = [1, 15]           # 每月触发日期
specific_times = []              # 具体时间列表
```

### 游戏设置
```toml
answer_time_limit = 3            # 答题时间限制（分钟）
reward_points = 10               # 答对奖励积分
leaderboard_top_count = 10       # 排行榜显示人数
```

### 群聊管理
```toml
apply_to_all_groups = false      # 是否应用到所有群聊
```

## 排行榜示例

```
【猜成语-最新排名Top10】
排名 昵称 答对次数 
1👑.群成员1 34 
2🥈.群成员2 19 
3🥉.群成员3 11 
4😄.群成员4 9 
5😃.群成员5 7 
6😁.群成员6 7 
7😆.群成员7 4 
8😊.群成员8 4 
9😍.群成员9 4 
10😋.群成员10 3 
```

## 注意事项

1. **群聊限制**：插件只在群聊中生效，私聊无效
2. **白名单管理**：需要将群聊添加到白名单才能使用
3. **精确匹配**：答案必须完全匹配，不支持模糊匹配
4. **重复答题**：每个用户在同一轮游戏中只能答对一次
5. **数据隔离**：每个群聊的排行榜数据独立存储

## 故障排除

### 插件无响应
- 检查插件是否启用（`enable = true`）
- 检查群聊是否在白名单中
- 查看日志文件确认错误信息

### 图片发送失败
- 检查网络连接
- 确认API接口可访问
- 查看错误日志

### 积分不生效
- 确认积分系统正常工作
- 检查用户是否已答对过
- 查看数据库连接状态

## 技术支持

如遇问题，请查看日志文件或联系管理员。
插件版本：2.0.0
更新日期：2025-07-09
