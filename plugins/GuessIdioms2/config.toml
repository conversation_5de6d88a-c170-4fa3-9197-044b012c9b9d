[GuessIdioms2]
# 是否启用插件
enable = true

# 触发方式设置
enable_keyword_trigger = true  # 是否开启关键词指令触发
enable_timer_trigger = true   # 是否开启定时触发

# 关键词触发指令
keyword_commands = ["测试猜成语", "测试成语游戏", "测试开始猜成语"]

# 定时触发设置
# 支持的时间格式：
# - 每日：daily_hours = [8, 14, 20] 表示每天8点、14点、20点触发
# - 每月：monthly_days = [1, 15] 表示每月1号和15号触发
# - 具体时间：specific_times = ["2024-12-25 10:00", "2024-12-31 23:59"]
timer_type = "daily"  # 可选：daily, monthly, specific
daily_hours = [9, 15, 20]  # 每日触发的小时数
monthly_days = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31]  # 每月触发的日期
specific_times = []  # 具体时间列表

# 答题设置
answer_time_limit = 3  # 答题时间限制（分钟）
reward_points = 50     # 答对奖励积分数

# 群聊白名单设置
apply_to_all_groups = false  # 是否应用到所有群聊
# 如果为false，则只在白名单群聊中生效
# 可在群聊中发送"添加猜成语白名单"指令添加当前群聊

# 排行榜设置
leaderboard_top_count = 10  # 排行榜显示前几名

# 指令格式提示
command_format = """-----猜成语2-----
🎮 游戏指令：
• 猜成语 - 开始游戏
• 成语游戏 - 开始游戏
• 开始猜成语 - 开始游戏

🔧 管理指令（仅管理员）：
• 添加猜成语白名单 - 将当前群聊加入白名单
• 删除猜成语白名单 - 将当前群聊从白名单移除
• 测试定时猜成语 - 手动触发定时功能（测试用）

🎯 游戏规则（抢答模式）：
• 答题时间限制：3分钟
• 第一个答对的人获胜并立刻结束游戏
• 答对奖励：50积分
• 答错不扣分，忽略不响应
• 游戏结束后显示详细答案信息和排行榜

⏰ 定时功能：
• 每天9点、15点、20点自动触发
• 应用到所有群聊"""
