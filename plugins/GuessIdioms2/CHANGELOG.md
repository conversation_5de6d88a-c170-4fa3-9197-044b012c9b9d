# 猜成语2插件更新日志

## 版本 2.2.0 (2025-07-11) - 定时功能完善与管理功能增强

### 🚀 重大更新
- **定时功能完善**: 修复定时触发无法发送到所有群聊的问题
- **管理员权限控制**: 添加和删除白名单操作现在需要管理员权限
- **删除白名单功能**: 新增"删除猜成语白名单"指令
- **手动测试定时功能**: 新增"测试定时猜成语"指令，方便管理员测试定时推送

### 🔧 技术改进
- 优化定时触发逻辑，支持真正的全群聊推送
- 从数据库获取群聊列表，解决无法获取所有群聊的问题
- 增加管理员权限验证机制
- 完善错误处理和日志记录

### 🛡️ 安全增强
- 白名单管理操作现在需要管理员权限
- 定时测试功能仅限管理员使用
- 防止非管理员用户滥用管理功能

### 📊 功能增强
- 手动测试定时功能提供详细的执行报告
- 显示触发成功和跳过的群聊数量
- 区分白名单模式和全群聊模式的统计信息

## 版本 2.1.0 (2025-07-11) - API升级与详细信息展示

### 🚀 重大更新
- **API接口升级**: 更新为新的JSON格式API接口 `https://api.dudunas.top/api/chengyu`
- **详细答案信息**: 游戏结束后显示成语的拼音、解释、出处、例子等详细信息
- **数据格式优化**: 从文本解析改为JSON格式，提高数据获取的稳定性和准确性

### 🔧 技术改进
- 新增AppSecret参数支持（固定值：6c3b0ca62dd045e9dcf15e3dcbe4c03a）
- 优化数据解析逻辑，支持完整的成语信息展示
- 改进错误处理机制，提高接口调用的可靠性
- 更新游戏状态存储，包含成语的详细信息

### 📝 文档更新
- 更新README.md中的接口说明和游戏流程
- 更新INSTALL.md中的接口信息和功能说明
- 完善配置文件说明和使用指南

### 🎮 用户体验提升
- 游戏结束后不仅显示答案，还包含成语的完整学习信息
- 提供拼音、解释、出处、例子等教育价值内容
- 增强游戏的知识性和趣味性

## 版本 2.0.1 (2025-07-09) - 抢答模式

### 🚀 重大更新：抢答模式
- ✅ **核心逻辑修改**：改为抢答模式，第一个答对的人立刻获胜并结束游戏
- ✅ **游戏流程优化**：答对后立刻显示答案和排行榜，不再等待时间结束
- ✅ **竞技性增强**：真正的抢答体验，增加游戏的紧张感和竞技性

### 🎯 抢答模式特点
- **即时结束**：第一个答对的人立刻结束游戏
- **唯一获胜者**：每轮只有一个获胜者
- **快节奏**：不需要等待完整的答题时间
- **公平竞争**：先答对先获胜，体现反应速度

### 🔧 技术改进
- 修改 `_handle_answer` 方法，答对后立刻调用 `_end_game`
- 优化 `_end_game` 方法，支持不同的结束原因提示
- 更新游戏状态管理，确保游戏结束后不再处理答案

## 版本 2.0.0 (2025-07-09)

### 🎯 主要功能
- ✅ 创建完整的猜成语游戏插件
- ✅ 支持关键词触发和定时触发
- ✅ 集成积分奖励系统
- ✅ 实现群聊排行榜功能
- ✅ 支持群聊白名单管理

### 🔧 核心特性
- **游戏流程**: 获取成语图片 → 群员抢答 → 第一个答对者获胜 → 立刻显示排行榜
- **抢答机制**: 第一个答对的人立刻结束游戏并获得积分
- **时间控制**: 默认3分钟答题时间上限，但通常会提前结束
- **精确匹配**: 答案必须完全匹配，防止误判
- **数据隔离**: 每个群聊的排行榜独立存储

### 🎮 触发方式
- **关键词触发**: `猜成语`、`成语游戏`、`开始猜成语`
- **定时触发**: 支持每日、每月、特定时间
- **白名单管理**: `添加猜成语白名单`

### 📊 排行榜功能
- 显示前10名用户
- 按答对次数排序
- 不足10名时显示"虚以待位"
- 使用表情符号美化显示

### 🛠️ 技术实现
- **API接口**: http://api.xingchenfu.xyz/API/LookIdiom.php
- **图片处理**: 自动下载并转换为base64发送
- **数据存储**: JSON文件存储排行榜数据
- **配置管理**: TOML格式配置文件
- **错误处理**: 完整的异常处理和日志记录

### 📁 文件结构
```
plugins/GuessIdioms2/
├── main.py              # 主插件代码
├── config.toml          # 配置文件
├── whitelist.txt        # 群聊白名单
├── __init__.py          # 初始化文件
├── README.md            # 详细说明
├── USAGE.md             # 使用指南
├── INSTALL.md           # 安装说明
├── CHANGELOG.md         # 更新日志（本文件）
└── data/                # 数据存储目录
    └── leaderboard_*.json  # 排行榜数据
```

### 🎯 问题修复
- ✅ **游戏流程**: 确保群员答对后不会立刻结束游戏，而是等待时间结束
- ✅ **排行榜显示**: 当排名不足10名时，空位显示"虚以待位"
- ✅ **插件命名**: 统一使用"GuessIdioms2"作为插件名称

### 📋 配置选项
- `enable`: 是否启用插件
- `enable_keyword_trigger`: 是否启用关键词触发
- `enable_timer_trigger`: 是否启用定时触发
- `keyword_commands`: 触发指令列表
- `answer_time_limit`: 答题时间限制（分钟）
- `reward_points`: 答对奖励积分数
- `apply_to_all_groups`: 是否应用到所有群聊
- `leaderboard_top_count`: 排行榜显示人数

### 🔍 测试状态
- ✅ 代码语法检查通过
- ✅ 配置文件格式正确
- ✅ API接口测试成功
- ✅ 排行榜功能正常
- ✅ 数据文件读写正常

### 📝 使用说明
1. 启用插件：设置 `enable = true`
2. 添加白名单：在群聊发送 `添加猜成语白名单`
3. 开始游戏：发送触发指令
4. 答题：直接发送成语答案
5. 查看排行榜：游戏结束后自动显示

### 🎉 排行榜示例
```
【猜成语-最新排名Top10】
排名 昵称 答对次数 
1👑.群成员1 34 
2🥈.群成员2 19 
3🥉.群成员3 11 
4😄.虚以待位 0
5😃.虚以待位 0
6😁.虚以待位 0
7😆.虚以待位 0
8😊.虚以待位 0
9😍.虚以待位 0
10😋.虚以待位 0
```

### 📞 技术支持
如有问题，请查看：
- README.md - 详细功能说明
- USAGE.md - 使用指南
- 日志文件 - 错误信息
- 配置文件 - 参数设置

---
**版本**: 2.0.0  
**作者**: Assistant  
**创建日期**: 2025-07-09  
**兼容性**: XYBot框架
