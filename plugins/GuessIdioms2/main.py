import asyncio
import json
import os
import re
import tomllib
from datetime import datetime, timedelta
from typing import Dict, List, Optional

import aiohttp
from loguru import logger

from WechatAPI import WechatAPIClient
from database.XYBotDB import XYBotDB
from utils.decorators import *
from utils.plugin_base import PluginBase


class GuessIdioms2(PluginBase):
    description = "猜成语2 - 通过显示成语相关的图片，让群成员抢答对应的成语"
    author = "Assistant"
    version = "2.2.0"

    def __init__(self):
        super().__init__()
        
        # 获取配置文件路径
        config_path = os.path.join(os.path.dirname(__file__), "config.toml")
        
        try:
            with open(config_path, "rb") as f:
                config = tomllib.load(f)

            # 读取配置
            plugin_config = config.get("GuessIdioms2", {})
            self.enable = plugin_config.get("enable", False)

            # 触发方式设置
            self.enable_keyword_trigger = plugin_config.get("enable_keyword_trigger", True)
            self.enable_timer_trigger = plugin_config.get("enable_timer_trigger", False)
            self.keyword_commands = plugin_config.get("keyword_commands", ["猜成语"])

            # 定时触发设置
            self.timer_type = plugin_config.get("timer_type", "daily")
            self.daily_hours = plugin_config.get("daily_hours", [10, 15, 20])
            self.monthly_days = plugin_config.get("monthly_days", [1, 15])
            self.specific_times = plugin_config.get("specific_times", [])

            # 答题设置
            self.answer_time_limit = plugin_config.get("answer_time_limit", 3)  # 分钟
            self.reward_points = plugin_config.get("reward_points", 10)

            # 群聊白名单设置
            self.apply_to_all_groups = plugin_config.get("apply_to_all_groups", False)
            self.leaderboard_top_count = plugin_config.get("leaderboard_top_count", 10)
            self.command_format = plugin_config.get("command_format", "")

            # 读取主配置文件获取管理员列表
            main_config_path = "main_config.toml"
            with open(main_config_path, "rb") as f:
                main_config = tomllib.load(f)
            self.admins = main_config.get("XYBot", {}).get("admins", [])
            
        except Exception as e:
            logger.error(f"加载GuessIdioms2.0配置文件失败: {str(e)}")
            self.enable = False
            self.admins = []
            
        # 初始化数据库和文件路径
        self.db = XYBotDB()
        self.plugin_dir = os.path.dirname(__file__)
        self.whitelist_file = os.path.join(self.plugin_dir, "whitelist.txt")
        self.data_dir = os.path.join(self.plugin_dir, "data")
        os.makedirs(self.data_dir, exist_ok=True)
        
        # 游戏状态管理
        self.active_games: Dict[str, Dict] = {}  # 群聊ID -> 游戏状态
        
        # API接口
        self.api_url = "https://api.dudunas.top/api/chengyu"
        self.app_secret = "6c3b0ca62dd045e9dcf15e3dcbe4c03a"

    async def async_init(self):
        """异步初始化"""
        return

    def _load_whitelist(self) -> List[str]:
        """加载群聊白名单"""
        try:
            if os.path.exists(self.whitelist_file):
                with open(self.whitelist_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                whitelist = []
                for line in lines:
                    line = line.strip()
                    if line and not line.startswith('#'):
                        whitelist.append(line)
                return whitelist
            return []
        except Exception as e:
            logger.error(f"加载白名单文件失败: {e}")
            return []

    def _save_whitelist(self, whitelist: List[str]):
        """保存群聊白名单"""
        try:
            with open(self.whitelist_file, 'w', encoding='utf-8') as f:
                f.write("# 猜成语2.0插件群聊白名单\n")
                f.write("# 每行一个群聊ID，以@chatroom结尾\n")
                for group_id in whitelist:
                    f.write(f"{group_id}\n")
        except Exception as e:
            logger.error(f"保存白名单文件失败: {e}")

    def _is_group_allowed(self, group_id: str) -> bool:
        """检查群聊是否允许使用插件"""
        if self.apply_to_all_groups:
            return True
        whitelist = self._load_whitelist()
        return group_id in whitelist

    def _is_admin(self, wxid: str) -> bool:
        """检查用户是否为管理员"""
        return wxid in self.admins

    async def _get_all_groups(self, bot: WechatAPIClient) -> List[str]:
        """获取所有群聊列表"""
        try:
            # 从数据库获取所有群聊
            all_groups = self.db.get_chatroom_list()
            return [group for group in all_groups if group.endswith("@chatroom")]
        except Exception as e:
            logger.error(f"获取群聊列表失败: {e}")
            return []

    def _load_leaderboard(self, group_id: str) -> Dict:
        """加载群聊排行榜数据"""
        leaderboard_file = os.path.join(self.data_dir, f"leaderboard_{group_id}.json")
        try:
            if os.path.exists(leaderboard_file):
                with open(leaderboard_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return {}
        except Exception as e:
            logger.error(f"加载排行榜数据失败: {e}")
            return {}

    def _save_leaderboard(self, group_id: str, leaderboard: Dict):
        """保存群聊排行榜数据"""
        leaderboard_file = os.path.join(self.data_dir, f"leaderboard_{group_id}.json")
        try:
            with open(leaderboard_file, 'w', encoding='utf-8') as f:
                json.dump(leaderboard, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存排行榜数据失败: {e}")

    async def _fetch_idiom_data(self) -> Optional[Dict]:
        """请求成语接口获取图片和答案"""
        try:
            # 构建请求参数
            params = {
                'AppSecret': self.app_secret
            }

            async with aiohttp.ClientSession() as session:
                async with session.get(self.api_url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()

                        # 检查返回状态
                        if data.get('code') == 200 and 'result' in data:
                            result = data['result']
                            return {
                                'image_url': result.get('imglink', ''),
                                'answer': result.get('chengyu', ''),
                                'pinyin': result.get('pingyin', ''),
                                'explanation': result.get('jieshi', ''),
                                'source': result.get('chuchu', ''),
                                'example': result.get('lizi', '')
                            }
                        else:
                            logger.error(f"API返回错误: {data}")
                            return None
                    else:
                        logger.error(f"请求成语接口失败，状态码: {response.status}")
                        return None
        except Exception as e:
            logger.error(f"请求成语接口异常: {e}")
            return None

    async def _download_image(self, image_url: str) -> Optional[bytes]:
        """下载图片并返回二进制数据"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(image_url) as response:
                    if response.status == 200:
                        return await response.read()
                    else:
                        logger.error(f"下载图片失败，状态码: {response.status}")
                        return None
        except Exception as e:
            logger.error(f"下载图片异常: {e}")
            return None

    async def _start_game(self, bot: WechatAPIClient, group_id: str):
        """开始猜成语游戏"""
        if group_id in self.active_games:
            await bot.send_text_message(group_id, "-----猜成语2.0-----\n🎮 游戏正在进行中，请等待当前游戏结束！")
            return

        # 获取成语数据
        idiom_data = await self._fetch_idiom_data()
        if not idiom_data:
            await bot.send_text_message(group_id, "-----猜成语2.0-----\n❌ 获取成语数据失败，请稍后再试！")
            return

        # 初始化游戏状态
        game_state = {
            'image_url': idiom_data['image_url'],
            'answer': idiom_data['answer'],
            'pinyin': idiom_data['pinyin'],
            'explanation': idiom_data['explanation'],
            'source': idiom_data['source'],
            'example': idiom_data['example'],
            'start_time': datetime.now(),
            'participants': {},  # wxid -> {'nickname': str, 'correct_count': int}
            'answered_correctly': set()  # 已答对的用户wxid
        }

        self.active_games[group_id] = game_state

        # 发送游戏开始消息
        start_message = (
            f"-----猜成语2.0-----\n"
            f"🎮 猜成语游戏开始！\n"
            f"⏰ 答题时间：{self.answer_time_limit}分钟\n"
            f"🎯 答对奖励：{self.reward_points}积分\n"
            f"💡 请根据图片猜出对应的成语！"
        )

        await bot.send_text_message(group_id, start_message)

        # 下载并发送图片
        try:
            image_data = await self._download_image(idiom_data['image_url'])
            if image_data:
                await bot.send_image_message(group_id, image_data)
            else:
                await bot.send_text_message(group_id, f"图片下载失败，请访问：{idiom_data['image_url']}")
        except Exception as e:
            logger.error(f"发送图片失败: {e}")
            await bot.send_text_message(group_id, f"图片发送失败，请访问：{idiom_data['image_url']}")

        # 设置定时器，时间到后结束游戏
        asyncio.create_task(self._game_timer(bot, group_id))

    async def _game_timer(self, bot: WechatAPIClient, group_id: str):
        """游戏计时器"""
        await asyncio.sleep(self.answer_time_limit * 60)  # 转换为秒

        if group_id in self.active_games:
            await self._end_game(bot, group_id, "timeout")

    async def _end_game(self, bot: WechatAPIClient, group_id: str, reason: str = "timeout"):
        """结束游戏并发送排行榜"""
        if group_id not in self.active_games:
            return

        game_state = self.active_games[group_id]
        answer = game_state['answer']
        pinyin = game_state.get('pinyin', '')
        explanation = game_state.get('explanation', '')
        source = game_state.get('source', '')
        example = game_state.get('example', '')

        # 构建详细的答案信息
        answer_details = f"✅ 正确答案：{answer}"
        if pinyin:
            answer_details += f"\n📖 拼音：{pinyin}"
        if explanation:
            answer_details += f"\n💡 解释：{explanation}"
        if source:
            answer_details += f"\n📚 出处：{source}"
        if example:
            answer_details += f"\n🌰 例子：{example}"

        # 根据结束原因发送不同的提示
        if reason == "correct_answer":
            # 抢答成功结束
            await bot.send_text_message(group_id,
                f"-----猜成语2-----\n"
                f"🎉 游戏结束！有人抢答成功！\n"
                f"{answer_details}")
        else:
            # 时间到结束
            await bot.send_text_message(group_id,
                f"-----猜成语2-----\n"
                f"⏰ 时间到！游戏结束！\n"
                f"{answer_details}")

        # 更新排行榜数据
        leaderboard = self._load_leaderboard(group_id)
        for wxid, user_data in game_state['participants'].items():
            if wxid not in leaderboard:
                leaderboard[wxid] = {
                    'nickname': user_data['nickname'],
                    'total_correct': 0
                }
            leaderboard[wxid]['total_correct'] += user_data['correct_count']
            leaderboard[wxid]['nickname'] = user_data['nickname']  # 更新昵称

        self._save_leaderboard(group_id, leaderboard)

        # 生成并发送排行榜
        await self._send_leaderboard(bot, group_id, leaderboard)

        # 清除游戏状态
        del self.active_games[group_id]

    async def _send_leaderboard(self, bot: WechatAPIClient, group_id: str, leaderboard: Dict):
        """发送排行榜"""
        # 按答对次数排序
        sorted_users = sorted(leaderboard.items(),
                            key=lambda x: x[1]['total_correct'],
                            reverse=True) if leaderboard else []

        # 生成排行榜消息
        message = "【猜成语-最新排名Top10】\n排名 昵称 答对次数\n"

        rank_emojis = ["👑", "🥈", "🥉", "😄", "😃", "😁", "😆", "😊", "😍", "😋"]

        # 显示实际排名
        for i, (wxid, user_data) in enumerate(sorted_users[:self.leaderboard_top_count]):
            rank = i + 1
            emoji = rank_emojis[i] if i < len(rank_emojis) else "😎"
            nickname = user_data.get('nickname', '未知用户')
            correct_count = user_data.get('total_correct', 0)

            message += f"{rank}{emoji}.{nickname} {correct_count}\n"

        # 如果排名不足10名，补充"虚以待位"
        actual_count = len(sorted_users)
        if actual_count < self.leaderboard_top_count:
            for i in range(actual_count, self.leaderboard_top_count):
                rank = i + 1
                emoji = rank_emojis[i] if i < len(rank_emojis) else "😎"
                message += f"{rank}{emoji}.虚以待位 0\n"

        await bot.send_text_message(group_id, message)

    @on_text_message(priority=50)
    async def handle_text(self, bot: WechatAPIClient, message: dict):
        """处理文本消息"""
        if not self.enable:
            return
            
        content = str(message["Content"]).strip()
        group_id = message["FromWxid"]
        sender_wxid = message["SenderWxid"]
        
        # 只处理群聊消息
        if not group_id.endswith("@chatroom"):
            return
            
        # 处理白名单管理指令（需要管理员权限）
        if content in ["添加猜成语白名单", "删除猜成语白名单"]:
            if not self._is_admin(sender_wxid):
                await bot.send_text_message(group_id,
                    "-----猜成语2.0-----\n❌ 只有管理员才能操作白名单！")
                return

            whitelist = self._load_whitelist()

            if content == "添加猜成语白名单":
                if group_id not in whitelist:
                    whitelist.append(group_id)
                    self._save_whitelist(whitelist)
                    await bot.send_text_message(group_id,
                        "-----猜成语2.0-----\n✅ 已将当前群聊添加到猜成语白名单！")
                else:
                    await bot.send_text_message(group_id,
                        "-----猜成语2.0-----\n💡 当前群聊已在猜成语白名单中！")

            elif content == "删除猜成语白名单":
                if group_id in whitelist:
                    whitelist.remove(group_id)
                    self._save_whitelist(whitelist)
                    await bot.send_text_message(group_id,
                        "-----猜成语2.0-----\n✅ 已将当前群聊从猜成语白名单中移除！")
                else:
                    await bot.send_text_message(group_id,
                        "-----猜成语2.0-----\n💡 当前群聊不在猜成语白名单中！")
            return

        # 处理手动测试定时功能（需要管理员权限）
        if content == "测试定时猜成语":
            if not self._is_admin(sender_wxid):
                await bot.send_text_message(group_id,
                    "-----猜成语2.0-----\n❌ 只有管理员才能测试定时功能！")
                return

            await self._manual_timer_trigger(bot, sender_wxid)
            return

        # 检查群聊是否在白名单中
        if not self._is_group_allowed(group_id):
            return
        
        # 处理关键词触发
        if self.enable_keyword_trigger and content in self.keyword_commands:
            await self._start_game(bot, group_id)
            return
        
        # 处理游戏中的答案
        if group_id in self.active_games:
            await self._handle_answer(bot, message)

    async def _handle_answer(self, bot: WechatAPIClient, message: dict):
        """处理游戏中的答案（抢答模式）"""
        content = str(message["Content"]).strip()
        group_id = message["FromWxid"]
        sender_wxid = message["SenderWxid"]

        game_state = self.active_games[group_id]
        correct_answer = game_state['answer']

        # 检查答案是否正确（精确匹配）
        if content == correct_answer:
            # 抢答成功！获取用户昵称
            try:
                nickname = await bot.get_nickname(sender_wxid)
                if not nickname:
                    nickname = "未知用户"
            except:
                nickname = "未知用户"

            # 更新参与者数据（本轮的获胜者）
            game_state['participants'][sender_wxid] = {
                'nickname': nickname,
                'correct_count': 1  # 本轮答对
            }
            game_state['answered_correctly'].add(sender_wxid)

            # 奖励积分
            self.db.add_points(sender_wxid, self.reward_points)

            # 发送抢答成功提示
            await bot.send_at_message(group_id,
                f"\n🎉 抢答成功！奖励{self.reward_points}积分！",
                [sender_wxid])

            # 立刻结束游戏并显示排行榜
            await self._end_game(bot, group_id, "correct_answer")

    # 定时触发装饰器 - 每小时检查一次是否需要触发游戏
    @schedule('cron', minute=0)
    async def timer_trigger_check(self, bot: WechatAPIClient):
        """定时触发检查"""
        if not self.enable or not self.enable_timer_trigger:
            return
            
        now = datetime.now()
        should_trigger = False
        
        if self.timer_type == "daily":
            if now.hour in self.daily_hours:
                should_trigger = True
        elif self.timer_type == "monthly":
            if now.day in self.monthly_days and now.hour in [10]:  # 默认10点触发
                should_trigger = True
        elif self.timer_type == "specific":
            current_time_str = now.strftime("%Y-%m-%d %H:%M")
            if current_time_str in self.specific_times:
                should_trigger = True
        
        if should_trigger:
            # 获取所有允许的群聊
            target_groups = []
            if self.apply_to_all_groups:
                # 获取所有群聊列表
                target_groups = await self._get_all_groups(bot)
                logger.info(f"定时触发：获取到 {len(target_groups)} 个群聊")
            else:
                target_groups = self._load_whitelist()
                logger.info(f"定时触发：白名单模式，共 {len(target_groups)} 个群聊")

            # 向所有目标群聊发送游戏
            triggered_count = 0
            for group_id in target_groups:
                if group_id not in self.active_games:  # 避免重复开始游戏
                    await self._start_game(bot, group_id)
                    triggered_count += 1
                    await asyncio.sleep(1)  # 避免频繁发送

            logger.info(f"定时触发完成：成功触发 {triggered_count} 个群聊的猜成语游戏")

    async def _manual_timer_trigger(self, bot: WechatAPIClient, admin_wxid: str):
        """手动触发定时功能（测试用）"""
        try:
            # 获取所有允许的群聊
            target_groups = []
            if self.apply_to_all_groups:
                target_groups = await self._get_all_groups(bot)
                await bot.send_text_message(admin_wxid,
                    f"-----猜成语2.0-----\n🔧 手动测试定时功能\n📊 模式：所有群聊\n🎯 目标群聊数：{len(target_groups)}")
            else:
                target_groups = self._load_whitelist()
                await bot.send_text_message(admin_wxid,
                    f"-----猜成语2.0-----\n🔧 手动测试定时功能\n📊 模式：白名单群聊\n🎯 目标群聊数：{len(target_groups)}")

            if not target_groups:
                await bot.send_text_message(admin_wxid,
                    "-----猜成语2.0-----\n❌ 没有找到可用的群聊！")
                return

            # 向所有目标群聊发送游戏
            triggered_count = 0
            skipped_count = 0

            for group_id in target_groups:
                if group_id not in self.active_games:  # 避免重复开始游戏
                    await self._start_game(bot, group_id)
                    triggered_count += 1
                    await asyncio.sleep(1)  # 避免频繁发送
                else:
                    skipped_count += 1

            # 发送结果报告
            result_message = (
                f"-----猜成语2.0-----\n"
                f"✅ 手动测试定时功能完成！\n"
                f"🎮 成功触发：{triggered_count} 个群聊\n"
                f"⏭️ 跳过（游戏中）：{skipped_count} 个群聊\n"
                f"📊 总计群聊：{len(target_groups)} 个"
            )
            await bot.send_text_message(admin_wxid, result_message)

            logger.info(f"手动测试定时功能完成：成功触发 {triggered_count} 个群聊，跳过 {skipped_count} 个群聊")

        except Exception as e:
            logger.error(f"手动测试定时功能失败: {e}")
            await bot.send_text_message(admin_wxid,
                f"-----猜成语2.0-----\n❌ 手动测试定时功能失败：{str(e)}")
