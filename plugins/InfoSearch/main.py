import os
import tomllib
from typing import List, Dict, Any, Tu<PERSON>, Optional

from loguru import logger
from sqlalchemy import create_engine, text, MetaData, Table, Column, String, Integer, select, Index
from sqlalchemy.exc import SQLAlchemyError
import pymysql  # 添加MySQL驱动依赖

from WechatAPI import WechatAPIClient
from utils.decorators import *
from utils.plugin_base import PluginBase


class InfoSearch(PluginBase):
    description = "数据库信息查询插件"
    author = "Augment Agent"
    version = "1.0.0"

    def __init__(self):
        super().__init__()

        # 获取配置文件路径
        config_path = os.path.join(os.path.dirname(__file__), "config.toml")

        try:
            with open(config_path, "rb") as f:
                config = tomllib.load(f)

            # 读取基本配置
            basic_config = config.get("basic", {})
            self.enable = basic_config.get("enable", False)
            self.command = basic_config.get("command", ["查询", "搜索", "查找"])

            # 读取数据库配置
            db_config = config.get("database", {})
            self.connection_string = db_config.get("connection_string", "sqlite:///database/infosearch.db")
            self.gdstudent_table = db_config.get("gdstudent_table", "gdstudent")
            self.suishenma_table = db_config.get("suishenma_table", "suishenma")
            self.max_results = db_config.get("max_results", 10)

            # 初始化数据库引擎，添加连接超时设置
            self.engine = create_engine(
                self.connection_string,
                pool_recycle=1800,  # 连接回收时间，防止连接过期，降低为30分钟
                pool_timeout=30,    # 从连接池获取连接的超时时间
                pool_pre_ping=True, # 在使用前测试连接是否有效
                max_overflow=5,      # 连接池溢出时允许创建的最大连接数
                pool_size=5,         # 连接池大小
                connect_args={
                    'connect_timeout': 10,  # 连接超时时间（秒）
                    'read_timeout': 60,     # 读取超时时间（秒），增加到60秒
                    'write_timeout': 60     # 写入超时时间（秒），增加到60秒
                }
            )

            # 检查数据库连接和表是否存在
            self._check_and_create_tables()

            logger.info(f"InfoSearch插件初始化成功")

        except Exception as e:
            logger.error(f"加载InfoSearch配置文件失败: {str(e)}")
            self.enable = False  # 如果加载失败，禁用插件

    def _check_and_create_tables(self):
        """检查数据库连接和表是否存在"""
        try:
            logger.info(f"InfoSearch: 开始检查数据库连接和表结构")
            logger.info(f"InfoSearch: 数据库连接字符串: {self.connection_string.replace('abc.123', '****')}")

            # 创建元数据对象
            metadata = MetaData()

            # 尝试连接数据库
            try:
                with self.engine.connect() as conn:
                    # 检查是否可以连接
                    logger.info(f"InfoSearch: 测试数据库连接...")
                    result = conn.execute(text("SELECT 1"))
                    logger.info(f"InfoSearch: 成功连接到MySQL数据库")

                    # 检查数据库中的表
                    logger.info(f"InfoSearch: 检查数据库中的表...")

                    # 获取所有表名
                    tables_result = conn.execute(text("SHOW TABLES"))
                    tables = [row[0] for row in tables_result]
                    logger.info(f"InfoSearch: 数据库中的表: {', '.join(tables)}")

                    # 检查特定表是否存在
                    gd_exists = self.gdstudent_table in tables
                    sm_exists = self.suishenma_table in tables

                    if not gd_exists:
                        logger.warning(f"InfoSearch: 表 {self.gdstudent_table} 不存在")
                    else:
                        logger.info(f"InfoSearch: 表 {self.gdstudent_table} 存在")
                        # 检查表结构
                        try:
                            struct_result = conn.execute(text(f"DESCRIBE {self.gdstudent_table}"))
                            columns = [row[0] for row in struct_result]
                            logger.info(f"InfoSearch: {self.gdstudent_table} 表的字段: {', '.join(columns)}")

                            # 检查必要字段
                            required_fields = ['name', 'idcard', 'phone']
                            missing_fields = [field for field in required_fields if field not in columns]
                            if missing_fields:
                                logger.warning(f"InfoSearch: {self.gdstudent_table} 表缺少必要字段: {', '.join(missing_fields)}")
                        except Exception as e:
                            logger.error(f"InfoSearch: 检查 {self.gdstudent_table} 表结构失败: {str(e)}")

                    if not sm_exists:
                        logger.warning(f"InfoSearch: 表 {self.suishenma_table} 不存在")
                    else:
                        logger.info(f"InfoSearch: 表 {self.suishenma_table} 存在")
                        # 检查表结构
                        try:
                            struct_result = conn.execute(text(f"DESCRIBE {self.suishenma_table}"))
                            columns = [row[0] for row in struct_result]
                            logger.info(f"InfoSearch: {self.suishenma_table} 表的字段: {', '.join(columns)}")

                            # 检查必要字段
                            required_fields = ['name', 'idcard', 'phone']
                            missing_fields = [field for field in required_fields if field not in columns]
                            if missing_fields:
                                logger.warning(f"InfoSearch: {self.suishenma_table} 表缺少必要字段: {', '.join(missing_fields)}")
                        except Exception as e:
                            logger.error(f"InfoSearch: 检查 {self.suishenma_table} 表结构失败: {str(e)}")

                    # 如果两个表都不存在，返回错误
                    if not gd_exists and not sm_exists:
                        logger.error(f"InfoSearch: 两个表 {self.gdstudent_table} 和 {self.suishenma_table} 都不存在，插件无法正常工作")
                        raise ValueError(f"两个表 {self.gdstudent_table} 和 {self.suishenma_table} 都不存在")

            except Exception as e:
                logger.error(f"InfoSearch: 连接数据库或检查表失败: {str(e)}")
                raise

        except SQLAlchemyError as e:
            logger.error(f"InfoSearch: 检查数据库表失败: {str(e)}")
            raise

    async def search_by_name(self, name: str) -> List[Dict[str, Any]]:
        """根据姓名查询记录"""
        try:
            logger.info(f"InfoSearch: 开始根据姓名查询: {name}")

            # 使用索引优化的SQL查询，适用于MySQL
            # 使用简化的查询，先分别查询两个表，然后在Python中合并结果
            # 只使用精确查询，提高效率和稳定性
            gdstudent_query = text(f"""
                SELECT 'gdstudent' as source, name, idcard, phone
                FROM {self.gdstudent_table}
                WHERE name = :exact_name
                LIMIT :limit
            """)

            # 模糊查询已移除，只保留精确查询
            # gdstudent_fuzzy_query = text(f"""
            #     SELECT 'gdstudent' as source, name, idcard, phone
            #     FROM {self.gdstudent_table}
            #     WHERE name LIKE :name
            #     LIMIT :limit
            # """)

            suishenma_query = text(f"""
                SELECT 'suishenma' as source, name, idcard, phone
                FROM {self.suishenma_table}
                WHERE name = :exact_name
                LIMIT :limit
            """)

            # 模糊查询已移除，只保留精确查询
            # suishenma_fuzzy_query = text(f"""
            #     SELECT 'suishenma' as source, name, idcard, phone
            #     FROM {self.suishenma_table}
            #     WHERE name LIKE :name
            #     LIMIT :limit
            # """)

            results = []

            # 分别查询两个表
            with self.engine.connect() as conn:
                # 先查询gdstudent表
                logger.info(f"InfoSearch: 查询 {self.gdstudent_table} 表 (精确查询)")
                try:
                    # 先进行精确查询
                    gd_result = conn.execute(gdstudent_query, {"exact_name": name, "limit": self.max_results})
                    gd_rows = [dict(row) for row in gd_result]
                    logger.info(f"InfoSearch: 从 {self.gdstudent_table} 表精确查询找到 {len(gd_rows)} 条记录")

                    # 去掉模糊查询，只保留精确查询，提高效率和稳定性
                    # 注释掉模糊查询代码
                    # if len(gd_rows) == 0:
                    #     logger.info(f"InfoSearch: 对 {self.gdstudent_table} 表进行模糊查询")
                    #     # 模糊查询代码已移除

                    results.extend(gd_rows)
                except Exception as e:
                    logger.error(f"InfoSearch: 查询 {self.gdstudent_table} 表失败: {str(e)}")

                # 然后查询suishenma表
                logger.info(f"InfoSearch: 查询 {self.suishenma_table} 表 (精确查询)")
                try:
                    # 先进行精确查询
                    sm_result = conn.execute(suishenma_query, {"exact_name": name, "limit": self.max_results})
                    # 增加错误处理和调试日志
                    try:
                        # 直接处理元组结果，而不尝试转换为字典
                        sm_rows = []
                        for row in sm_result:
                            # 检查是否是元组或列表
                            if isinstance(row, (tuple, list)) and len(row) >= 4:
                                # 创建字典对象
                                record = {
                                    "source": row[0] if row[0] else "suishenma",
                                    "name": row[1] if row[1] else "未知",
                                    "idcard": row[2] if row[2] else "未知",
                                    "phone": row[3] if row[3] else "未知"
                                }
                                sm_rows.append(record)
                            elif hasattr(row, "_fields") and len(row) >= 4:  # 处理命名元组
                                # 如果是命名元组，尝试使用字段名
                                record = {
                                    "source": getattr(row, row._fields[0], "suishenma"),
                                    "name": getattr(row, row._fields[1], "未知"),
                                    "idcard": getattr(row, row._fields[2], "未知"),
                                    "phone": getattr(row, row._fields[3], "未知")
                                }
                                sm_rows.append(record)
                            elif hasattr(row, "_mapping"):  # 处理SQLAlchemy结果代理对象
                                try:
                                    record = dict(row)
                                    sm_rows.append(record)
                                except Exception as e:
                                    logger.error(f"InfoSearch: 无法将行转换为字典: {str(e)}")
                            else:
                                # 如果是其他类型，记录错误
                                logger.warning(f"InfoSearch: 未知的行类型: {type(row)}, 值: {row}")

                        logger.info(f"InfoSearch: 从 {self.suishenma_table} 表精确查询找到 {len(sm_rows)} 条记录")
                    except Exception as e:
                        logger.error(f"InfoSearch: 处理 {self.suishenma_table} 表查询结果时出错: {str(e)}")
                        # 输出第一行结果以进行调试
                        try:
                            first_row = next(sm_result)
                            logger.info(f"InfoSearch: 第一行结果: {first_row}")
                            # 如果第一行是元组，直接创建一个记录
                            if isinstance(first_row, (tuple, list)) and len(first_row) >= 4:
                                record = {
                                    "source": first_row[0] if first_row[0] else "suishenma",
                                    "name": first_row[1] if first_row[1] else "未知",
                                    "idcard": first_row[2] if first_row[2] else "未知",
                                    "phone": first_row[3] if first_row[3] else "未知"
                                }
                                sm_rows = [record]
                                logger.info(f"InfoSearch: 从第一行创建了一个记录")
                        except:
                            logger.info(f"InfoSearch: 无法获取第一行结果")
                            sm_rows = []

                    # 去掉模糊查询，只保留精确查询，提高效率和稳定性
                    # 注释掉模糊查询代码
                    # if len(sm_rows) == 0 and len(results) < self.max_results:
                    #     logger.info(f"InfoSearch: 对 {self.suishenma_table} 表进行模糊查询")
                    #     # 模糊查询代码已移除

                    results.extend(sm_rows)
                except Exception as e:
                    logger.error(f"InfoSearch: 查询 {self.suishenma_table} 表失败: {str(e)}")

            # 限制总结果数
            results = results[:self.max_results]
            logger.info(f"InfoSearch: 姓名查询完成，总共找到 {len(results)} 条记录")
            return results

        except SQLAlchemyError as e:
            logger.error(f"InfoSearch: 姓名查询失败: {str(e)}")
            return []
        except Exception as e:
            logger.error(f"InfoSearch: 姓名查询过程中发生未知错误: {str(e)}")
            return []

    async def search_by_idcard(self, idcard: str) -> List[Dict[str, Any]]:
        """根据身份证号码查询记录"""
        try:
            logger.info(f"InfoSearch: 开始根据身份证号码查询: {idcard}")

            # 使用索引优化的SQL查询，适用于MySQL
            # 使用简化的查询，先分别查询两个表，然后在Python中合并结果
            # 只使用精确查询，提高效率和稳定性
            gdstudent_query = text(f"""
                SELECT 'gdstudent' as source, name, idcard, phone
                FROM {self.gdstudent_table}
                WHERE idcard = :exact_idcard
                LIMIT :limit
            """)

            # 模糊查询已移除，只保留精确查询
            # gdstudent_fuzzy_query = text(f"""
            #     SELECT 'gdstudent' as source, name, idcard, phone
            #     FROM {self.gdstudent_table}
            #     WHERE idcard LIKE :idcard
            #     LIMIT :limit
            # """)

            suishenma_query = text(f"""
                SELECT 'suishenma' as source, name, idcard, phone
                FROM {self.suishenma_table}
                WHERE idcard = :exact_idcard
                LIMIT :limit
            """)

            # 模糊查询已移除，只保留精确查询
            # suishenma_fuzzy_query = text(f"""
            #     SELECT 'suishenma' as source, name, idcard, phone
            #     FROM {self.suishenma_table}
            #     WHERE idcard LIKE :idcard
            #     LIMIT :limit
            # """)

            results = []

            # 分别查询两个表
            with self.engine.connect() as conn:
                # 先查询gdstudent表
                logger.info(f"InfoSearch: 查询 {self.gdstudent_table} 表 (精确查询)")
                try:
                    # 先进行精确查询
                    gd_result = conn.execute(gdstudent_query, {"exact_idcard": idcard, "limit": self.max_results})
                    gd_rows = [dict(row) for row in gd_result]
                    logger.info(f"InfoSearch: 从 {self.gdstudent_table} 表精确查询找到 {len(gd_rows)} 条记录")

                    # 去掉模糊查询，只保留精确查询，提高效率和稳定性
                    # 注释掉模糊查询代码
                    # if len(gd_rows) == 0:
                    #     logger.info(f"InfoSearch: 对 {self.gdstudent_table} 表进行模糊查询")
                    #     # 模糊查询代码已移除

                    results.extend(gd_rows)
                except Exception as e:
                    logger.error(f"InfoSearch: 查询 {self.gdstudent_table} 表失败: {str(e)}")

                # 然后查询suishenma表
                logger.info(f"InfoSearch: 查询 {self.suishenma_table} 表 (精确查询)")
                try:
                    # 先进行精确查询
                    sm_result = conn.execute(suishenma_query, {"exact_idcard": idcard, "limit": self.max_results})
                    # 增加错误处理和调试日志
                    try:
                        # 直接处理元组结果，而不尝试转换为字典
                        sm_rows = []
                        for row in sm_result:
                            # 检查是否是元组或列表
                            if isinstance(row, (tuple, list)) and len(row) >= 4:
                                # 创建字典对象
                                record = {
                                    "source": row[0] if row[0] else "suishenma",
                                    "name": row[1] if row[1] else "未知",
                                    "idcard": row[2] if row[2] else "未知",
                                    "phone": row[3] if row[3] else "未知"
                                }
                                sm_rows.append(record)
                            elif hasattr(row, "_fields") and len(row) >= 4:  # 处理命名元组
                                # 如果是命名元组，尝试使用字段名
                                record = {
                                    "source": getattr(row, row._fields[0], "suishenma"),
                                    "name": getattr(row, row._fields[1], "未知"),
                                    "idcard": getattr(row, row._fields[2], "未知"),
                                    "phone": getattr(row, row._fields[3], "未知")
                                }
                                sm_rows.append(record)
                            elif hasattr(row, "_mapping"):  # 处理SQLAlchemy结果代理对象
                                try:
                                    record = dict(row)
                                    sm_rows.append(record)
                                except Exception as e:
                                    logger.error(f"InfoSearch: 无法将行转换为字典: {str(e)}")
                            else:
                                # 如果是其他类型，记录错误
                                logger.warning(f"InfoSearch: 未知的行类型: {type(row)}, 值: {row}")

                        logger.info(f"InfoSearch: 从 {self.suishenma_table} 表精确查询找到 {len(sm_rows)} 条记录")
                    except Exception as e:
                        logger.error(f"InfoSearch: 处理 {self.suishenma_table} 表查询结果时出错: {str(e)}")
                        # 输出第一行结果以进行调试
                        try:
                            first_row = next(sm_result)
                            logger.info(f"InfoSearch: 第一行结果: {first_row}")
                            # 如果第一行是元组，直接创建一个记录
                            if isinstance(first_row, (tuple, list)) and len(first_row) >= 4:
                                record = {
                                    "source": first_row[0] if first_row[0] else "suishenma",
                                    "name": first_row[1] if first_row[1] else "未知",
                                    "idcard": first_row[2] if first_row[2] else "未知",
                                    "phone": first_row[3] if first_row[3] else "未知"
                                }
                                sm_rows = [record]
                                logger.info(f"InfoSearch: 从第一行创建了一个记录")
                        except:
                            logger.info(f"InfoSearch: 无法获取第一行结果")
                            sm_rows = []

                    # 去掉模糊查询，只保留精确查询，提高效率和稳定性
                    # 注释掉模糊查询代码
                    # if len(sm_rows) == 0 and len(results) < self.max_results:
                    #     logger.info(f"InfoSearch: 对 {self.suishenma_table} 表进行模糊查询")
                    #     # 模糊查询代码已移除

                    results.extend(sm_rows)
                except Exception as e:
                    logger.error(f"InfoSearch: 查询 {self.suishenma_table} 表失败: {str(e)}")

            # 限制总结果数
            results = results[:self.max_results]
            logger.info(f"InfoSearch: 身份证号码查询完成，总共找到 {len(results)} 条记录")
            return results

        except SQLAlchemyError as e:
            logger.error(f"InfoSearch: 身份证号码查询失败: {str(e)}")
            return []
        except Exception as e:
            logger.error(f"InfoSearch: 身份证号码查询过程中发生未知错误: {str(e)}")
            return []

    async def search_by_phone(self, phone: str) -> List[Dict[str, Any]]:
        """根据手机号码查询记录"""
        try:
            logger.info(f"InfoSearch: 开始根据手机号码查询: {phone}")

            # 使用索引优化的SQL查询，适用于MySQL
            # 使用简化的查询，先分别查询两个表，然后在Python中合并结果
            # 只使用精确查询，提高效率和稳定性
            gdstudent_query = text(f"""
                SELECT 'gdstudent' as source, name, idcard, phone
                FROM {self.gdstudent_table}
                WHERE phone = :exact_phone
                LIMIT :limit
            """)

            # 模糊查询已移除，只保留精确查询
            # gdstudent_fuzzy_query = text(f"""
            #     SELECT 'gdstudent' as source, name, idcard, phone
            #     FROM {self.gdstudent_table}
            #     WHERE phone LIKE :phone
            #     LIMIT :limit
            # """)

            suishenma_query = text(f"""
                SELECT 'suishenma' as source, name, idcard, phone
                FROM {self.suishenma_table}
                WHERE phone = :exact_phone
                LIMIT :limit
            """)

            # 模糊查询已移除，只保留精确查询
            # suishenma_fuzzy_query = text(f"""
            #     SELECT 'suishenma' as source, name, idcard, phone
            #     FROM {self.suishenma_table}
            #     WHERE phone LIKE :phone
            #     LIMIT :limit
            # """)

            results = []

            # 分别查询两个表
            with self.engine.connect() as conn:
                # 先查询gdstudent表
                logger.info(f"InfoSearch: 查询 {self.gdstudent_table} 表 (精确查询)")
                try:
                    # 先进行精确查询
                    gd_result = conn.execute(gdstudent_query, {"exact_phone": phone, "limit": self.max_results})
                    gd_rows = [dict(row) for row in gd_result]
                    logger.info(f"InfoSearch: 从 {self.gdstudent_table} 表精确查询找到 {len(gd_rows)} 条记录")

                    # 去掉模糊查询，只保留精确查询，提高效率和稳定性
                    # 注释掉模糊查询代码
                    # if len(gd_rows) == 0:
                    #     logger.info(f"InfoSearch: 对 {self.gdstudent_table} 表进行模糊查询")
                    #     # 模糊查询代码已移除

                    results.extend(gd_rows)
                except Exception as e:
                    logger.error(f"InfoSearch: 查询 {self.gdstudent_table} 表失败: {str(e)}")

                # 然后查询suishenma表
                logger.info(f"InfoSearch: 查询 {self.suishenma_table} 表 (精确查询)")
                try:
                    # 先进行精确查询
                    sm_result = conn.execute(suishenma_query, {"exact_phone": phone, "limit": self.max_results})
                    # 增加错误处理和调试日志
                    try:
                        # 直接处理元组结果，而不尝试转换为字典
                        sm_rows = []
                        for row in sm_result:
                            # 检查是否是元组或列表
                            if isinstance(row, (tuple, list)) and len(row) >= 4:
                                # 创建字典对象
                                record = {
                                    "source": row[0] if row[0] else "suishenma",
                                    "name": row[1] if row[1] else "未知",
                                    "idcard": row[2] if row[2] else "未知",
                                    "phone": row[3] if row[3] else "未知"
                                }
                                sm_rows.append(record)
                            elif hasattr(row, "_fields") and len(row) >= 4:  # 处理命名元组
                                # 如果是命名元组，尝试使用字段名
                                record = {
                                    "source": getattr(row, row._fields[0], "suishenma"),
                                    "name": getattr(row, row._fields[1], "未知"),
                                    "idcard": getattr(row, row._fields[2], "未知"),
                                    "phone": getattr(row, row._fields[3], "未知")
                                }
                                sm_rows.append(record)
                            elif hasattr(row, "_mapping"):  # 处理SQLAlchemy结果代理对象
                                try:
                                    record = dict(row)
                                    sm_rows.append(record)
                                except Exception as e:
                                    logger.error(f"InfoSearch: 无法将行转换为字典: {str(e)}")
                            else:
                                # 如果是其他类型，记录错误
                                logger.warning(f"InfoSearch: 未知的行类型: {type(row)}, 值: {row}")

                        logger.info(f"InfoSearch: 从 {self.suishenma_table} 表精确查询找到 {len(sm_rows)} 条记录")
                    except Exception as e:
                        logger.error(f"InfoSearch: 处理 {self.suishenma_table} 表查询结果时出错: {str(e)}")
                        # 输出第一行结果以进行调试
                        try:
                            first_row = next(sm_result)
                            logger.info(f"InfoSearch: 第一行结果: {first_row}")
                            # 如果第一行是元组，直接创建一个记录
                            if isinstance(first_row, (tuple, list)) and len(first_row) >= 4:
                                record = {
                                    "source": first_row[0] if first_row[0] else "suishenma",
                                    "name": first_row[1] if first_row[1] else "未知",
                                    "idcard": first_row[2] if first_row[2] else "未知",
                                    "phone": first_row[3] if first_row[3] else "未知"
                                }
                                sm_rows = [record]
                                logger.info(f"InfoSearch: 从第一行创建了一个记录")
                        except:
                            logger.info(f"InfoSearch: 无法获取第一行结果")
                            sm_rows = []

                    # 去掉模糊查询，只保留精确查询，提高效率和稳定性
                    # 注释掉模糊查询代码
                    # if len(sm_rows) == 0 and len(results) < self.max_results:
                    #     logger.info(f"InfoSearch: 对 {self.suishenma_table} 表进行模糊查询")
                    #     # 模糊查询代码已移除

                    results.extend(sm_rows)
                except Exception as e:
                    logger.error(f"InfoSearch: 查询 {self.suishenma_table} 表失败: {str(e)}")

            # 限制总结果数
            results = results[:self.max_results]
            logger.info(f"InfoSearch: 手机号码查询完成，总共找到 {len(results)} 条记录")
            return results

        except SQLAlchemyError as e:
            logger.error(f"InfoSearch: 手机号码查询失败: {str(e)}")
            return []
        except Exception as e:
            logger.error(f"InfoSearch: 手机号码查询过程中发生未知错误: {str(e)}")
            return []

    async def format_results(self, results: List[Dict[str, Any]]) -> str:
        """格式化查询结果为文本消息"""
        if not results:
            return "未找到匹配的记录。"

        output = "查询结果：\n"
        output += "------------------------\n"

        for i, record in enumerate(results, 1):
            # 增加错误处理，确保即使结果格式不符合预期，也能正确处理
            try:
                # 尝试获取字段，如果不存在则使用默认值
                if isinstance(record, dict):
                    source = record.get("source", "未知")
                    name = record.get("name", "未知")
                    idcard = record.get("idcard", "未知")
                    phone = record.get("phone", "未知")
                elif isinstance(record, (list, tuple)) and len(record) >= 4:
                    # 如果是列表或元组，尝试按顺序获取字段
                    source = record[0] if record[0] else "未知"
                    name = record[1] if record[1] else "未知"
                    idcard = record[2] if record[2] else "未知"
                    phone = record[3] if record[3] else "未知"
                else:
                    # 如果是其他类型，输出原始记录
                    logger.warning(f"InfoSearch: 记录格式不符合预期: {record}")
                    output += f"{i}. 原始记录: {str(record)}\n"
                    output += "------------------------\n"
                    continue

                output += f"{i}. 来源: {source}\n"
                output += f"   姓名: {name}\n"
                output += f"   身份证: {idcard}\n"
                output += f"   手机: {phone}\n"
                output += "------------------------\n"
            except Exception as e:
                logger.error(f"InfoSearch: 格式化结果时出错: {str(e)}")
                # 如果格式化失败，输出原始记录
                output += f"{i}. 原始记录: {str(record)}\n"
                output += "------------------------\n"

        return output

    @on_text_message(priority=50)
    async def handle_text(self, bot: WechatAPIClient, message: dict):
        """处理文本消息"""
        if not self.enable:
            return True  # 插件未启用，允许后续插件处理

        content = message["Content"].strip()
        parts = content.split()

        # 检查是否是查询命令
        if len(parts) >= 3 and parts[0] in self.command:
            logger.info(f"InfoSearch: 收到查询命令: {content}")
            query_type = parts[1]
            query_value = " ".join(parts[2:])

            # 先发送正在查询的消息
            await bot.send_text_message(
                message["FromWxid"],
                f"正在查询{query_type} '{query_value}'，请稍候..."
            )

            try:
                results = []
                logger.info(f"InfoSearch: 开始查询 {query_type}: {query_value}")

                if query_type in ["姓名", "名字", "name"]:
                    results = await self.search_by_name(query_value)
                elif query_type in ["身份证", "身份证号", "身份证号码", "idcard"]:
                    results = await self.search_by_idcard(query_value)
                elif query_type in ["手机", "手机号", "手机号码", "phone"]:
                    results = await self.search_by_phone(query_value)
                else:
                    await bot.send_text_message(
                        message["FromWxid"],
                        f"不支持的查询类型: {query_type}\n支持的类型: 姓名、身份证、手机"
                    )
                    return False  # 阻止后续插件处理

                logger.info(f"InfoSearch: 查询完成，找到 {len(results)} 条结果")

                # 格式化并发送结果
                result_text = await self.format_results(results)
                await bot.send_text_message(message["FromWxid"], result_text)

            except Exception as e:
                logger.error(f"InfoSearch: 查询过程中出错: {str(e)}")
                await bot.send_text_message(
                    message["FromWxid"],
                    f"查询过程中出错: {str(e)}"
                )

            return False  # 阻止后续插件处理

        return True  # 允许后续插件处理
