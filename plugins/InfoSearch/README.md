# InfoSearch 插件

## 简介

InfoSearch 是一个用于查询数据库信息的插件，支持对 gdstudent 和 suishenma 两个表进行查询。
可以通过姓名、身份证号码或手机号码进行查询，最多返回10条匹配记录。

## 功能

- 姓名查询：根据姓名查询相关记录
- 身份证号码查询：根据身份证号码查询相关记录
- 手机号码查询：根据手机号码查询相关记录

## 使用方法

在聊天中发送以下格式的消息：

```
查询 姓名 张三
查询 身份证 330102199001011234
查询 手机 13812345678
```

也可以使用其他触发词：

```
搜索 姓名 张三
查找 身份证 330102199001011234
```

## 配置说明

配置文件 `config.toml` 包含以下设置：

```toml
[basic]
# 是否启用插件
enable = true

# 触发命令
command = ["查询", "搜索", "查找"]

# 数据库配置
[database]
# 数据库连接字符串 (MySQL)
connection_string = "mysql+pymysql://root:abc.123@192.168.123.199:3306/infosearch"

# 表名配置
gdstudent_table = "gdstudent"
suishenma_table = "suishenma"

# 最大返回记录数
max_results = 10
```

## 数据库表结构

插件使用的数据库表结构如下：

### gdstudent 表

| 字段名 | 类型 | 说明 |
|-------|------|------|
| name  | TEXT | 姓名 |
| idcard | TEXT | 身份证号码 |
| phone | TEXT | 手机号码 |
| ... | ... | 其他字段 |

### suishenma 表

| 字段名 | 类型 | 说明 |
|-------|------|------|
| name  | TEXT | 姓名 |
| idcard | TEXT | 身份证号码 |
| phone | TEXT | 手机号码 |
| ... | ... | 其他字段 |

## 版本历史

- 1.0.0: 初始版本，支持基本查询功能
