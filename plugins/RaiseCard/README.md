# 举牌插件 (RaiseCard)

这是一个简单的举牌图片生成插件，可以生成举牌图片并发送给用户。

## 功能

- 根据用户输入的文本生成举牌图片
- 支持普通举牌和黑丝举牌两种模式
- 支持私聊和群聊
- 支持多种触发命令

## 使用方法

### 普通举牌

#### 私聊

直接发送以下命令：

```
举牌 [文本内容]
举牌子 [文本内容]
举个牌 [文本内容]
举个牌子 [文本内容]
```

例如：`举牌 XXXBot`

#### 群聊

在群聊中 @ 机器人并发送以下命令：

```
@机器人 举牌 [文本内容]
@机器人 举牌子 [文本内容]
@机器人 举个牌 [文本内容]
@机器人 举个牌子 [文本内容]
```

例如：`@机器人 举牌 XXXBot`

### 黑丝举牌

#### 私聊

直接发送以下命令：

```
黑丝举牌 [第一行]|[第二行]|[第三行]
黑丝举牌子 [第一行]|[第二行]
黑丝举个牌 [第一行]
黑丝举个牌子 [第一行]|[第二行]|[第三行]
```

例如：`黑丝举牌 XXXBot|举牌插件`

**注意：** 黑丝举牌支持多行文本，使用 `|` 分隔每行文本。

#### 群聊

在群聊中 @ 机器人并发送以下命令：

```
@机器人 黑丝举牌 [第一行]|[第二行]|[第三行]
@机器人 黑丝举牌子 [第一行]|[第二行]
```

例如：`@机器人 黑丝举牌 XXXBot|举牌插件`

## 示例

### 普通举牌

![普通举牌示例](https://api.suyanw.cn/api/zt.php?msg=XXXBot)

### 黑丝举牌

![黑丝举牌示例](https://api.suyanw.cn/api/hsjp/?rgb1=0&rgb2=0&rgb3=0&msg=XXXBot&msg1=举牌插件)

## API 来源

本插件使用以下 API 生成举牌图片：

- 普通举牌：`https://api.suyanw.cn/api/zt.php`
- 黑丝举牌：`https://api.suyanw.cn/api/hsjp/`

## 注意事项

- 请勿生成违反法律法规的内容
- API 可能会有调用限制，请合理使用
- 如果 API 无法访问，插件将无法正常工作

## 版本历史

- v1.1.0 (2025-04-20): 添加黑丝举牌功能
- v1.0.0 (2025-04-20): 初始版本
