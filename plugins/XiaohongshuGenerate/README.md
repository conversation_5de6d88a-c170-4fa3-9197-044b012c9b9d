# 小红书爆款内容生成插件

艹！这是老王我专门搞的小红书爆款内容生成插件，能根据你提供的主题快速生成高质量的小红书内容！

## 功能特色

🌟 **多种风格模板**：内置4种不同风格的小红书内容模板
- Prompt1：小红书浓人风格 - 情绪浓烈，网感十足
- Prompt2：爆款写作专家 - 专业营销文案
- Prompt3：简洁爆款风格 - 简单直接有效  
- Prompt4：营销大师风格 - 高级营销策略

🤖 **多AI平台支持**：支持硅基流动和MetaChat两大AI平台
- 硅基流动：支持DeepSeek-V3、DeepSeek-R1、QwQ-32B等模型
- MetaChat：支持GPT-3.5、GPT-4、GPT-4-Turbo等模型

⚙️ **灵活配置**：支持用户自定义切换模板、平台和模型
💰 **积分系统**：默认消耗1000积分，支持管理员和白名单豁免
🎯 **智能生成**：根据主题生成包含标题和正文的完整小红书内容

## 使用指令

### 基本指令
- `小红书 [主题]` - 生成小红书爆款内容
- `小红书帮助` - 显示帮助信息

### 设置指令
- `小红书模板 [1-4]` - 切换prompt模板
- `小红书平台 [siliconflow/metachat]` - 切换AI平台
- `小红书模型 [模型名]` - 切换AI模型
- `小红书状态` - 查看当前设置

### 使用示例
```
小红书 护肤品推荐
小红书 美食探店攻略
小红书 旅行穿搭分享
小红书模板 2
小红书平台 siliconflow
小红书模型 deepseek-ai/DeepSeek-V3
```

## 配置说明

### 基础配置
```toml
[basic]
enable = true          # 是否启用插件
price = 1000          # 积分消耗
admin_ignore = true   # 管理员豁免积分
whitelist_ignore = true # 白名单豁免积分
admins = []           # 管理员列表
```

### AI平台配置
```toml
[siliconflow]
enable = true
api_key = "your_api_key"
base_url = "https://api.siliconflow.cn/v1"
default_model = "deepseek-ai/DeepSeek-V3"

[metachat]
enable = false
api_key = "your_api_key"
base_url = "https://api.metachat.cn/v1"
default_model = "gpt-3.5-turbo"
```

### 通用设置
```toml
[settings]
default_platform = "siliconflow"  # 默认AI平台
default_prompt = 1               # 默认模板
commands = ["小红书", "xhs", "xiaohongshu"]  # 指令前缀
max_tokens = 2000               # 最大生成字数
temperature = 0.7               # 温度参数
timeout = 120                   # 请求超时时间
```

## 安装步骤

1. **配置API密钥**
   - 在 `config.toml` 中填入你的硅基流动或MetaChat API密钥
   - 至少启用一个AI平台

2. **检查Prompt文件**
   - 确保 `Prompt1.md` 到 `Prompt4.md` 文件存在
   - 这些文件包含了不同风格的system prompt

3. **重启机器人**
   - 重启微信机器人以加载插件

## 注意事项

⚠️ **API密钥安全**：请妥善保管你的API密钥，不要泄露给他人
⚠️ **积分消耗**：每次生成内容会消耗1000积分，请确保用户有足够积分
⚠️ **网络环境**：确保服务器能正常访问AI平台的API接口
⚠️ **内容审核**：生成的内容仅供参考，请遵守平台规则和法律法规

## 故障排除

### 常见问题

1. **API调用失败**
   - 检查API密钥是否正确
   - 检查网络连接是否正常
   - 查看日志中的具体错误信息

2. **模板文件不存在**
   - 确保Prompt1.md到Prompt4.md文件都存在
   - 检查文件编码是否为UTF-8

3. **积分扣除异常**
   - 检查数据库连接是否正常
   - 确认用户积分是否足够

### 日志查看
插件运行时会输出详细的日志信息，可以通过日志排查问题：
- 初始化日志：显示配置加载情况
- API调用日志：显示请求和响应详情
- 错误日志：显示具体的错误信息和堆栈跟踪

## 更新日志

### v1.0.0 (2025-01-27)
- 🎉 初始版本发布
- ✅ 支持4种小红书内容模板
- ✅ 支持硅基流动和MetaChat双平台
- ✅ 完整的积分系统集成
- ✅ 灵活的用户配置管理
- ✅ 详细的帮助和状态查询功能

## 技术支持

如果遇到问题或有功能建议，请联系老王！

---

**插件作者**：老王  
**版本**：v1.0.0  
**最后更新**：2025-01-27
