import os
import json
import tomllib
import traceback
import aiohttp
import asyncio
from typing import Dict, List, Optional
from loguru import logger

from WechatAPI import WechatAPIClient
from utils.decorators import *
from utils.plugin_base import PluginBase


class XiaohongshuGenerate(PluginBase):
    """小红书爆款内容生成插件 - 艹！老王专门搞的小红书内容生成器"""
    description = "小红书爆款内容生成插件"
    author = "老王"
    version = "1.0.0"

    def __init__(self):
        super().__init__()
        self.plugin_dir = os.path.dirname(os.path.abspath(__file__))

        # 初始化数据库连接
        from database.XYBotDB import XYBotDB
        self.db = XYBotDB()

        # 用户状态管理 - 记录用户当前使用的配置
        self.user_states = {}  # {wxid: {"platform": "siliconflow", "model": "xxx", "prompt": 1}}
        
        try:
            # 读取配置文件
            config_path = os.path.join(self.plugin_dir, "config.toml")
            with open(config_path, "rb") as f:
                config = tomllib.load(f)

            # 基础配置
            basic_config = config.get("basic", {})
            self.enable = basic_config.get("enable", False)
            self.price = basic_config.get("price", 1000)
            self.admin_ignore = basic_config.get("admin_ignore", True)
            self.whitelist_ignore = basic_config.get("whitelist_ignore", True)
            self.admins = basic_config.get("admins", [])

            # 硅基流动配置
            self.siliconflow_config = config.get("siliconflow", {})
            self.siliconflow_enable = self.siliconflow_config.get("enable", True)
            self.siliconflow_api_key = self.siliconflow_config.get("api_key", "")
            self.siliconflow_base_url = self.siliconflow_config.get("base_url", "https://api.siliconflow.cn/v1")
            self.siliconflow_default_model = self.siliconflow_config.get("default_model", "deepseek-ai/DeepSeek-V3")
            self.siliconflow_models = self.siliconflow_config.get("available_models", [])

            # MetaChat配置
            self.metachat_config = config.get("metachat", {})
            self.metachat_enable = self.metachat_config.get("enable", False)
            self.metachat_api_key = self.metachat_config.get("api_key", "")
            self.metachat_base_url = self.metachat_config.get("base_url", "https://api.metachat.cn/v1")
            self.metachat_default_model = self.metachat_config.get("default_model", "gpt-3.5-turbo")
            self.metachat_models = self.metachat_config.get("available_models", [])

            # 通用设置
            settings_config = config.get("settings", {})
            self.default_platform = settings_config.get("default_platform", "siliconflow")
            self.default_prompt = settings_config.get("default_prompt", 1)
            self.commands = settings_config.get("commands", ["小红书", "xhs", "xiaohongshu"])
            self.max_tokens = settings_config.get("max_tokens", 2000)
            self.temperature = settings_config.get("temperature", 0.7)
            self.timeout = settings_config.get("timeout", 120)

            # 加载Prompt模板
            self.prompts = {}
            for i in range(1, 5):
                prompt_file = os.path.join(self.plugin_dir, f"Prompt{i}.md")
                if os.path.exists(prompt_file):
                    with open(prompt_file, "r", encoding="utf-8") as f:
                        self.prompts[i] = f.read().strip()
                        logger.info(f"加载Prompt{i}.md成功")
                else:
                    logger.warning(f"Prompt{i}.md文件不存在")

            logger.success("XiaohongshuGenerate插件初始化成功")

        except Exception as e:
            logger.error(f"XiaohongshuGenerate插件初始化失败: {str(e)}")
            logger.error(traceback.format_exc())
            self.enable = False

    def get_user_state(self, wxid: str) -> Dict:
        """获取用户状态，如果不存在则创建默认状态"""
        if wxid not in self.user_states:
            self.user_states[wxid] = {
                "platform": self.default_platform,
                "model": self.siliconflow_default_model if self.default_platform == "siliconflow" else self.metachat_default_model,
                "prompt": self.default_prompt
            }
        return self.user_states[wxid]

    def update_user_state(self, wxid: str, **kwargs):
        """更新用户状态"""
        state = self.get_user_state(wxid)
        state.update(kwargs)
        self.user_states[wxid] = state

    async def check_points(self, bot: WechatAPIClient, message: dict) -> bool:
        """检查用户积分是否足够"""
        wxid = message["SenderWxid"]
        from_wxid = message["FromWxid"]
        is_group = from_wxid.endswith("chatroom")

        # 管理员和白名单豁免
        if (wxid in self.admins and self.admin_ignore) or (self.db.get_whitelist(wxid) and self.whitelist_ignore):
            return True

        # 检查积分
        user_points = self.db.get_points(wxid)
        if user_points < self.price:
            error_msg = f"\n😭-----老王的小红书生成器-----\n你的积分不够啦！需要 {self.price} 积分，你现在只有 {user_points} 积分"
            if is_group:
                await bot.send_at_message(from_wxid, error_msg, [wxid])
            else:
                await bot.send_text_message(from_wxid, error_msg)
            return False

        return True

    def deduct_points(self, wxid: str) -> bool:
        """扣除用户积分"""
        if (wxid in self.admins and self.admin_ignore) or (self.db.get_whitelist(wxid) and self.whitelist_ignore):
            return True
        
        return self.db.add_points(wxid, -self.price)

    async def call_siliconflow_api(self, messages: List[Dict], model: str) -> str:
        """调用硅基流动API"""
        try:
            if not self.siliconflow_api_key:
                return "艹！硅基流动API密钥未配置！"

            data = {
                "model": model,
                "messages": messages,
                "max_tokens": self.max_tokens,
                "temperature": self.temperature,
                "stream": False
            }

            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.siliconflow_api_key}"
            }

            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.siliconflow_base_url}/chat/completions",
                    headers=headers,
                    json=data,
                    timeout=aiohttp.ClientTimeout(total=self.timeout)
                ) as response:
                    if response.status != 200:
                        error = await response.text()
                        logger.error(f"硅基流动API错误[{response.status}]: {error}")
                        return "艹！硅基流动API调用失败，这个破接口！"

                    result = await response.json()
                    if not isinstance(result, dict) or "choices" not in result:
                        logger.error("硅基流动API返回格式错误")
                        return "艹！API返回的数据格式有问题！"

                    content = result["choices"][0].get("message", {}).get("content", "").strip()
                    return content if content else "艹！API没返回内容！"

        except asyncio.TimeoutError:
            logger.error("硅基流动API请求超时")
            return "艹！API请求超时了，这个破网络！"
        except Exception as e:
            logger.error(f"调用硅基流动API失败: {str(e)}")
            logger.error(traceback.format_exc())
            return f"艹！API调用出错了: {str(e)}"

    async def call_metachat_api(self, messages: List[Dict], model: str) -> str:
        """调用MetaChat API"""
        try:
            if not self.metachat_api_key:
                return "艹！MetaChat API密钥未配置！"

            data = {
                "model": model,
                "messages": messages,
                "max_tokens": self.max_tokens,
                "temperature": self.temperature,
                "stream": False
            }

            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.metachat_api_key}"
            }

            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.metachat_base_url}/chat/completions",
                    headers=headers,
                    json=data,
                    timeout=aiohttp.ClientTimeout(total=self.timeout)
                ) as response:
                    if response.status != 200:
                        error = await response.text()
                        logger.error(f"MetaChat API错误[{response.status}]: {error}")
                        return "艹！MetaChat API调用失败！"

                    result = await response.json()
                    if not isinstance(result, dict) or "choices" not in result:
                        logger.error("MetaChat API返回格式错误")
                        return "艹！API返回的数据格式有问题！"

                    content = result["choices"][0].get("message", {}).get("content", "").strip()
                    return content if content else "艹！API没返回内容！"

        except asyncio.TimeoutError:
            logger.error("MetaChat API请求超时")
            return "艹！API请求超时了！"
        except Exception as e:
            logger.error(f"调用MetaChat API失败: {str(e)}")
            logger.error(traceback.format_exc())
            return f"艹！API调用出错了: {str(e)}"

    async def generate_xiaohongshu_content(self, topic: str, wxid: str) -> str:
        """生成小红书内容"""
        try:
            user_state = self.get_user_state(wxid)
            platform = user_state["platform"]
            model = user_state["model"]
            prompt_id = user_state["prompt"]

            # 获取system prompt
            if prompt_id not in self.prompts:
                return "艹！选择的prompt模板不存在！"

            system_prompt = self.prompts[prompt_id]

            # 构建消息
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": f"请为以下素材描述信息「{topic}」创作小红书爆款内容"}
            ]

            # 根据平台调用对应API
            if platform == "siliconflow":
                if not self.siliconflow_enable:
                    return "艹！硅基流动平台未启用！"
                return await self.call_siliconflow_api(messages, model)
            elif platform == "metachat":
                if not self.metachat_enable:
                    return "艹！MetaChat平台未启用！"
                return await self.call_metachat_api(messages, model)
            else:
                return "艹！不支持的AI平台！"

        except Exception as e:
            logger.error(f"生成小红书内容失败: {str(e)}")
            logger.error(traceback.format_exc())
            return f"艹！生成内容时出错了: {str(e)}"

    def get_help_message(self) -> str:
        """获取帮助信息"""
        return """
🌟-----老王的小红书生成器-----🌟

📝 基本指令：
• 小红书 [主题] - 生成小红书爆款内容
• 小红书帮助 - 显示此帮助信息

⚙️ 设置指令：
• 小红书模板 [1-4] - 切换prompt模板
• 小红书平台 [siliconflow/metachat] - 切换AI平台
• 小红书模型 [模型名] - 切换AI模型
• 小红书状态 - 查看当前设置

📋 模板说明：
1️⃣ 小红书浓人风格 - 情绪浓烈，网感十足
2️⃣ 爆款写作专家 - 专业营销文案
3️⃣ 简洁爆款风格 - 简单直接有效
4️⃣ 营销大师风格 - 高级营销策略

💰 积分消耗：每次生成消耗1000积分
🎯 示例：小红书 护肤品推荐
        """

    def get_status_message(self, wxid: str) -> str:
        """获取用户当前状态信息"""
        user_state = self.get_user_state(wxid)
        platform = user_state["platform"]
        model = user_state["model"]
        prompt_id = user_state["prompt"]

        # 获取用户积分
        user_points = self.db.get_points(wxid)

        # 判断是否豁免积分
        is_exempt = (wxid in self.admins and self.admin_ignore) or (self.db.get_whitelist(wxid) and self.whitelist_ignore)

        status_msg = f"""
📊-----你的小红书生成器状态-----📊

🤖 当前AI平台：{platform}
🧠 当前模型：{model}
📝 当前模板：Prompt{prompt_id}
💰 你的积分：{user_points}
💸 每次消耗：{self.price}积分{'（你已豁免）' if is_exempt else ''}

🔧 可用平台：{'siliconflow' if self.siliconflow_enable else ''}{'、metachat' if self.metachat_enable else ''}
📋 可用模板：1、2、3、4
        """
        return status_msg

    @on_text_message(priority=50)
    async def handle_text_message(self, bot: WechatAPIClient, message: dict):
        """处理文本消息"""
        if not self.enable:
            return True

        content = message["Content"].strip()
        wxid = message["SenderWxid"]
        from_wxid = message["FromWxid"]

        # 检查是否是小红书相关指令
        is_xiaohongshu_cmd = False
        for cmd in self.commands:
            if content.startswith(cmd):
                is_xiaohongshu_cmd = True
                break

        if not is_xiaohongshu_cmd:
            return True

        try:
            # 解析指令 - 处理各种空格字符
            parts = content.replace('\u3000', ' ').replace('\xa0', ' ').split()
            if len(parts) < 1:
                return True

            main_cmd = parts[0]
            logger.info(f"小红书插件收到指令: {content}, 解析parts: {parts}")

            # 如果只有一个部分，可能是连在一起的，尝试按指令前缀分割
            if len(parts) == 1 and parts[0] not in self.commands:
                for cmd in self.commands:
                    if content.startswith(cmd):
                        remaining = content[len(cmd):].strip()
                        if remaining:
                            parts = [cmd, remaining]
                            logger.info(f"重新解析指令: {parts}")
                        break

            # 直接检查完整指令内容（处理没有空格分隔的情况）
            for cmd in self.commands:
                if content == f"{cmd}帮助" or content == f"{cmd}说明":
                    help_msg = self.get_help_message()
                    await bot.send_text_message(from_wxid, help_msg)
                    return False
                elif content == f"{cmd}状态" or content == f"{cmd}设置" or content == f"{cmd}配置":
                    logger.info(f"匹配到状态查询指令: {content}")
                    status_msg = self.get_status_message(wxid)
                    await bot.send_text_message(from_wxid, status_msg)
                    return False
                # 检查模板切换指令（如：小红书模板1）
                elif content.startswith(f"{cmd}模板"):
                    template_part = content[len(cmd)+2:]  # 去掉"小红书模板"部分
                    try:
                        prompt_id = int(template_part)
                        if prompt_id not in [1, 2, 3, 4]:
                            await bot.send_text_message(from_wxid, "艹！模板编号只能是1-4！")
                            return False

                        if prompt_id not in self.prompts:
                            await bot.send_text_message(from_wxid, f"艹！Prompt{prompt_id}.md文件不存在！")
                            return False

                        self.update_user_state(wxid, prompt=prompt_id)
                        await bot.send_text_message(from_wxid, f"✅ 已切换到模板{prompt_id}")
                        return False
                    except ValueError:
                        await bot.send_text_message(from_wxid, "艹！模板编号必须是数字！")
                        return False
                # 检查平台切换指令（如：小红书平台siliconflow）
                elif content.startswith(f"{cmd}平台"):
                    platform_part = content[len(cmd)+2:]  # 去掉"小红书平台"部分
                    platform = platform_part.lower()
                    if platform == "siliconflow":
                        if not self.siliconflow_enable:
                            await bot.send_text_message(from_wxid, "艹！硅基流动平台未启用！")
                            return False
                        self.update_user_state(wxid, platform="siliconflow", model=self.siliconflow_default_model)
                        await bot.send_text_message(from_wxid, "✅ 已切换到硅基流动平台")
                    elif platform == "metachat":
                        if not self.metachat_enable:
                            await bot.send_text_message(from_wxid, "艹！MetaChat平台未启用！")
                            return False
                        self.update_user_state(wxid, platform="metachat", model=self.metachat_default_model)
                        await bot.send_text_message(from_wxid, "✅ 已切换到MetaChat平台")
                    else:
                        await bot.send_text_message(from_wxid, "艹！只支持siliconflow和metachat平台！")
                    return False

            # 帮助指令（空格分隔版本）
            if len(parts) >= 2 and parts[1] in ["帮助", "说明", "help", "?", "？"]:
                help_msg = self.get_help_message()
                await bot.send_text_message(from_wxid, help_msg)
                return False

            # 状态查询指令（空格分隔版本）
            if len(parts) >= 2 and parts[1] in ["状态", "status", "设置", "配置"]:
                logger.info(f"匹配到状态查询指令: {parts[1]}")
                status_msg = self.get_status_message(wxid)
                await bot.send_text_message(from_wxid, status_msg)
                return False

            # 模板切换指令
            if len(parts) >= 3 and parts[1] in ["模板", "template", "prompt"]:
                try:
                    prompt_id = int(parts[2])
                    if prompt_id not in [1, 2, 3, 4]:
                        await bot.send_text_message(from_wxid, "艹！模板编号只能是1-4！")
                        return False

                    if prompt_id not in self.prompts:
                        await bot.send_text_message(from_wxid, f"艹！Prompt{prompt_id}.md文件不存在！")
                        return False

                    self.update_user_state(wxid, prompt=prompt_id)
                    await bot.send_text_message(from_wxid, f"✅ 已切换到模板{prompt_id}")
                    return False
                except ValueError:
                    await bot.send_text_message(from_wxid, "艹！模板编号必须是数字！")
                    return False

            # 平台切换指令
            if len(parts) >= 3 and parts[1] in ["平台", "platform", "api"]:
                platform = parts[2].lower()
                if platform == "siliconflow":
                    if not self.siliconflow_enable:
                        await bot.send_text_message(from_wxid, "艹！硅基流动平台未启用！")
                        return False
                    self.update_user_state(wxid, platform="siliconflow", model=self.siliconflow_default_model)
                    await bot.send_text_message(from_wxid, "✅ 已切换到硅基流动平台")
                elif platform == "metachat":
                    if not self.metachat_enable:
                        await bot.send_text_message(from_wxid, "艹！MetaChat平台未启用！")
                        return False
                    self.update_user_state(wxid, platform="metachat", model=self.metachat_default_model)
                    await bot.send_text_message(from_wxid, "✅ 已切换到MetaChat平台")
                else:
                    await bot.send_text_message(from_wxid, "艹！只支持siliconflow和metachat平台！")
                return False

            # 模型切换指令
            if len(parts) >= 3 and parts[1] in ["模型", "model"]:
                model_name = " ".join(parts[2:])
                user_state = self.get_user_state(wxid)
                platform = user_state["platform"]

                # 检查模型是否在可用列表中
                if platform == "siliconflow":
                    if model_name not in self.siliconflow_models:
                        available = "、".join(self.siliconflow_models)
                        await bot.send_text_message(from_wxid, f"艹！硅基流动平台不支持该模型！\n可用模型：{available}")
                        return False
                elif platform == "metachat":
                    if model_name not in self.metachat_models:
                        available = "、".join(self.metachat_models)
                        await bot.send_text_message(from_wxid, f"艹！MetaChat平台不支持该模型！\n可用模型：{available}")
                        return False

                self.update_user_state(wxid, model=model_name)
                await bot.send_text_message(from_wxid, f"✅ 已切换到模型：{model_name}")
                return False

            # 生成内容指令
            if len(parts) >= 2:
                # 提取主题内容
                topic = " ".join(parts[1:])
                if not topic or topic in ["帮助", "说明", "help", "?", "？"]:
                    help_msg = self.get_help_message()
                    await bot.send_text_message(from_wxid, help_msg)
                    return False

                # 检查积分
                if not await self.check_points(bot, message):
                    return False

                # 发送生成中提示
                await bot.send_text_message(from_wxid, "🤖 老王正在为你生成小红书爆款内容，稍等片刻...")

                # 生成内容
                result = await self.generate_xiaohongshu_content(topic, wxid)

                # 扣除积分
                if not self.deduct_points(wxid):
                    logger.error(f"扣除用户{wxid}积分失败")

                # 发送结果
                final_msg = f"🌟----小红书爆款内容----🌟\n\n{result}"
                await bot.send_text_message(from_wxid, final_msg)
                return False

            # 如果只输入了指令前缀，显示帮助
            help_msg = self.get_help_message()
            await bot.send_text_message(from_wxid, help_msg)
            return False

        except Exception as e:
            logger.error(f"处理小红书指令失败: {str(e)}")
            logger.error(traceback.format_exc())
            await bot.send_text_message(from_wxid, f"艹！处理指令时出错了: {str(e)}")
            return False
