[basic]
# 是否启用插件
enable = true
# 积分消耗（默认1000）
price = 1000
# 管理员豁免积分消耗
admin_ignore = true
# 白名单豁免积分消耗
whitelist_ignore = true
# 管理员列表
admins = ['wxid_fjgby1dzvif021']

[siliconflow]
# 硅基流动API配置
enable = true
api_key = "sk-xzhxqbpwqesphwrnysvczwlauhcswsqtdapnahgbgkyyywyh"
base_url = "https://api.siliconflow.cn/v1"
# 默认模型
default_model = "deepseek-ai/DeepSeek-V3"
# 可用模型列表
available_models = [
    "deepseek-ai/DeepSeek-V3",
    "deepseek-ai/DeepSeek-R1",
    "Qwen/QwQ-32B-Preview"
]

[metachat]
# MetaChat API配置（备用）
enable = false
api_key = ""
base_url = "https://api.metachat.cn/v1"
# 默认模型
default_model = "gpt-3.5-turbo"
# 可用模型列表
available_models = [
    "gpt-3.5-turbo",
    "gpt-4",
    "gpt-4-turbo"
]

[settings]
# 默认使用的AI平台 (siliconflow/metachat)
default_platform = "siliconflow"
# 默认使用的prompt模板 (1-4)
default_prompt = 1
# 指令前缀
commands = ["小红书", "xhs", "xiaohongshu"]
# 最大生成字数
max_tokens = 2000
# 温度参数
temperature = 0.7
# 请求超时时间（秒）
timeout = 120
