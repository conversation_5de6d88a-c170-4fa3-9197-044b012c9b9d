# HackerRanking 插件

## 插件简介

HackerRanking 是一个用于获取和搜索黑客排行榜信息的微信机器人插件。该插件可以获取最新的黑客排行榜，并支持按昵称精确搜索特定黑客的详细信息。

## 功能特性

### 1. 黑客排行榜
- 获取最新的黑客排行榜信息
- 显示前10名黑客，避免刷屏
- 支持黑客大王标识（👑）
- 提供详情链接跳转

### 2. 搜索黑客
- 支持按昵称精确搜索黑客
- 返回黑客详细信息卡片
- 包含头像、ID、昵称等信息
- 支持多个触发命令

## 使用方法

### 获取黑客排行榜

支持以下命令：
- `黑客排行榜`
- `黑客榜`
- `hacker排行榜`
- `hacker榜`

**示例：**
```
黑客排行榜
```

**返回格式：**
```
🔰🔰【最新黑客排行榜】🔰🔰
No.1 👑张三 [详情链接](https://h-acker.cn/#/pages/hackerDetail/hackerDetail?id=123)
No.2 李四 [详情链接](https://h-acker.cn/#/pages/hackerDetail/hackerDetail?id=456)
...

(避免刷屏，仅获取Top10)
👑 表示黑客大王
```

### 搜索黑客

支持以下命令格式：
- `搜索黑客 <昵称>`
- `查找黑客 <昵称>`
- `找黑客 <昵称>`
- `hacker搜索 <昵称>`

**示例：**
```
搜索黑客 张三
```

**返回：**
- 发送链接卡片消息
- 标题：`123# 张三`
- 描述：`点击查看黑客详情👉`
- 跳转链接：黑客详情页面
- 缩略图：黑客头像

## 配置说明

### config.toml 配置项

```toml
[HackerRanking]
# 是否启用插件
enable = true

# 排行榜触发命令列表
command = ["黑客排行榜", "黑客榜", "hacker排行榜", "hacker榜"]

# 搜索黑客触发命令列表
search_command = ["搜索黑客", "查找黑客", "找黑客", "hacker搜索"]

# 命令格式说明
command-format = """⚙️获取黑客排行榜：
黑客排行榜
黑客榜
hacker排行榜
hacker榜

⚙️搜索黑客：
搜索黑客 <昵称>
查找黑客 <昵称>
找黑客 <昵称>
hacker搜索 <昵称>"""
```

### 配置项说明

- `enable`: 是否启用插件（true/false）
- `command`: 排行榜功能的触发命令列表
- `search_command`: 搜索功能的触发命令列表
- `command-format`: 命令格式说明文本

## 技术实现

### API接口
- **数据源**: `https://h-acker.cn/api/hacker/getList`
- **请求方式**: GET
- **返回格式**: JSON

### 数据字段
- `id`: 黑客ID
- `name`: 黑客昵称
- `headimg`: 头像相对路径
- `heikedawang`: 是否为黑客大王（"1"表示是）

### 搜索逻辑
1. 调用API获取完整黑客列表
2. 遍历列表，对`name`字段进行精确匹配
3. 找到匹配项后构建完整头像URL
4. 使用`send_link_message`发送链接卡片

### 错误处理
- API请求超时处理
- 网络连接异常处理
- Cookie失效自动重试
- 数据为空异常处理
- 未找到匹配结果提示

## 注意事项

1. **精确搜索**: 搜索功能使用精确匹配，不支持模糊搜索
2. **同名处理**: 如果存在多个同名黑客，返回第一个匹配结果并提示
3. **头像URL**: 自动拼接完整的头像URL路径
4. **防刷屏**: 排行榜仅显示前10名，避免消息过长

## 版本信息

- **版本**: 1.0.0
- **作者**: 老王
- **描述**: 黑客排行榜插件

## 更新日志

### v1.0.0
- 初始版本发布
- 支持获取黑客排行榜
- 支持按昵称搜索黑客
- 支持多种触发命令
- 完善的错误处理机制
