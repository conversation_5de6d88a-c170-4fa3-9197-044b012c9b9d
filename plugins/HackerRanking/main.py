import tomllib
import aiohttp
from loguru import logger

from WechatAPI import WechatAPIClient
from utils.decorators import *
from utils.plugin_base import PluginBase


class HackerRanking(PluginBase):
    description = "黑客排行榜插件"
    author = "老王"
    version = "1.0.0"

    def __init__(self):
        super().__init__()

        with open("plugins/HackerRanking/config.toml", "rb") as f:
            plugin_config = tomllib.load(f)

        config = plugin_config["HackerRanking"]

        self.enable = config["enable"]
        self.command = config["command"]
        self.search_command = config["search_command"]
        self.command_format = config["command-format"]
        self.api_url = "https://h-acker.cn/api/hacker/getList"

    @on_text_message
    async def handle_text(self, bot: WechatAPIClient, message: dict):
        if not self.enable:
            return

        content = str(message["Content"]).strip()

        # 检查是否匹配排行榜触发命令
        if content in self.command:
            await self._handle_ranking(bot, message)
            return

        # 检查是否匹配搜索黑客命令
        for search_cmd in self.search_command:
            if content.startswith(search_cmd + " "):
                nickname = content[len(search_cmd):].strip()
                if nickname:
                    await self._handle_search_hacker(bot, message, nickname)
                else:
                    await bot.send_at_message(message["FromWxid"], "\n⚠️ 请输入要搜索的黑客昵称！\n格式：搜索黑客 <昵称>", [message["SenderWxid"]])
                return

        # 如果都不匹配，直接返回
        return

    async def _handle_ranking(self, bot: WechatAPIClient, message: dict):
        """处理黑客排行榜请求"""
        logger.info(f"[*]: 正在调用获取黑客榜API接口...")

        try:
            # 设置请求头，模拟移动端浏览器
            headers = {
                "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1",
                'Accept': 'application/json,text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                'Referer': 'https://h-acker.cn/',
                'Cookie': 'guardret=UwNQ; guardret=AVFT; XSRF-TOKEN=eyJpdiI6Imc1cnM0MUdGd01LTWpKOWN4SjNhRWc9PSIsInZhbHVlIjoiWGNwMTZNNlpMc05NUVBqN3dVbUx1TUU3TjVWZzdqSXlNeEJvTjB5VzdBcEdxZGc2b1NtZllvWVJPSGtpL3VrL3YwbU9Tc1R5Z2I0WGFCTzNFWmZGSHVBK0ZhNG1NZHZ2b0ZFdUlTcytyeTRCOFNvY0JEZlNkNnpkZmNJOW1sbWciLCJtYWMiOiJmZTMyNTMzMjVlYjNjZjg0NGYxNjFiYThhOTUxYjgwZWVmZmVkZTg4NDJmM2ViOTgzNGY2ZmNhZjAyYTI3N2M2IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IlJtbzZlN1V2QWxMZWQxektFaXNRSFE9PSIsInZhbHVlIjoiN21ZdDBPNEp3MTkwRDZIdUxaMDRxSjVTQmhzSitWbWJrdE9ndlJWUkh0NEpSK1NLaGZJZGFibUlmT1lkQzlRY2pPREliOVZkd3F6YkdYNkRWODE0cW4xR09HS0NTUUk5Q0xDOHRtQWtYMzd6ZDJlSW4wSjhEdUtyaEJJTkxHeWUiLCJtYWMiOiI2ZWY1NThjMTZlMTExMmFhY2YyNzFkZjliYTVmOTk3NDI1ZGVlYWU4YWQ2ZWI0MWU0OWViODQ3NTMxMzk0YWNjIiwidGFnIjoiIn0%3D; guardok=b4bRu49JIYyiVLTY1gRr70GPK7kQei18YIg/GSmkQsrCSJoLlOtwzfdLQINhnRvwwT6yzG7dcmTh3joWP3bWVA=='
            }

            # 使用aiohttp异步请求API
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=30)) as session:
                async with session.get(self.api_url, headers=headers) as response:
                    if response.status != 200:
                        logger.error(f"[-]: API请求失败，状态码: {response.status}")
                        await bot.send_at_message(message["FromWxid"], "\n⚠️ 获取黑客排行榜失败，API请求异常！", [message["SenderWxid"]])
                        return

                    json_data = await response.json()

                    # 检查API返回的状态码
                    if json_data.get('code') != 200:
                        logger.error(f"[-]: API返回错误，code: {json_data.get('code')}, msg: {json_data.get('msg')}")
                        await bot.send_at_message(message["FromWxid"], f"\n⚠️ 获取黑客排行榜失败：{json_data.get('msg', '未知错误')}", [message["SenderWxid"]])
                        return

            # 解析API返回数据
            result = json_data.get('data')
            if not result:
                logger.error("[-]: API返回数据为空")
                await bot.send_at_message(message["FromWxid"], "\n⚠️ 获取黑客排行榜失败，数据为空！", [message["SenderWxid"]])
                return

            # 构建排行榜消息内容
            content_msg = '🔰🔰【最新黑客排行榜】🔰🔰\n'
            num = 1
            for hacker in result:
                if num > 10:  # 只显示前10名，避免刷屏
                    break

                hacker_name = hacker.get('name', '未知')
                hacker_id = hacker.get('id', '')
                # 检查是否为黑客大王
                is_king = hacker.get('heikedawang', '0') == '1'
                king_icon = '👑' if is_king else ''

                # 构建详情链接
                profile_url = f"https://h-acker.cn/#/pages/hackerDetail/hackerDetail?id={str(hacker_id)}"

                content_msg += f"No.{num} {king_icon}{hacker_name} [详情链接]({profile_url})\n"
                num += 1

            content_msg += "\n(避免刷屏，仅获取Top10)\n👑 表示黑客大王"
            
            # 发送排行榜消息
            await bot.send_at_message(message["FromWxid"], "\n" + content_msg, [message["SenderWxid"]])
            logger.info("[+]: 黑客排行榜获取成功")

        except aiohttp.ClientTimeout:
            logger.error("[-]: 获取黑客榜API接口超时")
            await bot.send_at_message(message["FromWxid"], "\n⚠️ 获取黑客排行榜超时，请稍后重试！", [message["SenderWxid"]])
        except aiohttp.ClientError as e:
            logger.error(f"[-]: 网络请求错误: {e}")
            await bot.send_at_message(message["FromWxid"], "\n⚠️ 网络请求失败，请检查网络连接！", [message["SenderWxid"]])
        except Exception as e:
            logger.error(f'[-]: 获取黑客榜API接口出现错误, 错误信息: {e}')
            await bot.send_at_message(message["FromWxid"], "\n⚠️ 获取黑客排行榜出现未知错误！", [message["SenderWxid"]])

    async def _handle_search_hacker(self, bot: WechatAPIClient, message: dict, nickname: str):
        """处理搜索黑客请求"""
        logger.info(f"[*]: 正在搜索黑客: {nickname}")

        try:
            # 设置请求头，模拟移动端浏览器
            headers = {
                "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1",
                'Accept': 'application/json,text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                'Referer': 'https://h-acker.cn/',
                'Cookie': 'guardret=UwNQ; guardret=AVFT; XSRF-TOKEN=eyJpdiI6Imc1cnM0MUdGd01LTWpKOWN4SjNhRWc9PSIsInZhbHVlIjoiWGNwMTZNNlpMc05NUVBqN3dVbUx1TUU3TjVWZzdqSXlNeEJvTjB5VzdBcEdxZGc2b1NtZllvWVJPSGtpL3VrL3YwbU9Tc1R5Z2I0WGFCTzNFWmZGSHVBK0ZhNG1NZHZ2b0ZFdUlTcytyeTRCOFNvY0JEZlNkNnpkZmNJOW1sbWciLCJtYWMiOiJmZTMyNTMzMjVlYjNjZjg0NGYxNjFiYThhOTUxYjgwZWVmZmVkZTg4NDJmM2ViOTgzNGY2ZmNhZjAyYTI3N2M2IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IlJtbzZlN1V2QWxMZWQxektFaXNRSFE9PSIsInZhbHVlIjoiN21ZdDBPNEp3MTkwRDZIdUxaMDRxSjVTQmhzSitWbWJrdE9ndlJWUkh0NEpSK1NLaGZJZGFibUlmT1lkQzlRY2pPREliOVZkd3F6YkdYNkRWODE0cW4xR09HS0NTUUk5Q0xDOHRtQWtYMzd6ZDJlSW4wSjhEdUtyaEJJTkxHeWUiLCJtYWMiOiI2ZWY1NThjMTZlMTExMmFhY2YyNzFkZjliYTVmOTk3NDI1ZGVlYWU4YWQ2ZWI0MWU0OWViODQ3NTMxMzk0YWNjIiwidGFnIjoiIn0%3D; guardok=b4bRu49JIYyiVLTY1gRr70GPK7kQei18YIg/GSmkQsrCSJoLlOtwzfdLQINhnRvwwT6yzG7dcmTh3joWP3bWVA=='
            }

            # 使用aiohttp异步请求API
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=30)) as session:
                async with session.get(self.api_url, headers=headers) as response:
                    if response.status != 200:
                        logger.error(f"[-]: API请求失败，状态码: {response.status}")
                        await bot.send_at_message(message["FromWxid"], "\n⚠️ 搜索黑客失败，API请求异常！", [message["SenderWxid"]])
                        return

                    json_data = await response.json()

                    # 检查API返回的状态码
                    if json_data.get('code') != 200:
                        logger.error(f"[-]: API返回错误，code: {json_data.get('code')}, msg: {json_data.get('msg')}")
                        await bot.send_at_message(message["FromWxid"], f"\n⚠️ 搜索黑客失败：{json_data.get('msg', '未知错误')}", [message["SenderWxid"]])
                        return

            # 解析API返回数据
            result = json_data.get('data')
            if not result:
                logger.error("[-]: API返回数据为空")
                await bot.send_at_message(message["FromWxid"], "\n⚠️ 搜索黑客失败，数据为空！", [message["SenderWxid"]])
                return

            # 精确搜索匹配的黑客昵称
            found_hackers = []
            for hacker in result:
                hacker_name = hacker.get('name', '')
                if hacker_name == nickname:  # 精确匹配
                    found_hackers.append(hacker)

            if not found_hackers:
                await bot.send_at_message(message["FromWxid"], f"\n❌ 未找到昵称为「{nickname}」的黑客！", [message["SenderWxid"]])
                return

            # 如果找到多个同名黑客，发送第一个
            hacker = found_hackers[0]
            hacker_id = hacker.get('id', '')
            hacker_name = hacker.get('name', '未知')
            head_img = hacker.get('headimg', '')

            # 构建完整的头像URL
            if head_img:
                if head_img.startswith('/'):
                    head_img = head_img[1:]  # 去掉开头的斜杠
                thumb_url = f"https://h-acker.cn/{head_img}"
            else:
                thumb_url = ""

            # 构建详情链接
            detail_url = f"https://h-acker.cn/#/pages/hackerDetail/hackerDetail?id={str(hacker_id)}"

            # 构建标题和描述
            title = f"{hacker_id}# {hacker_name}"
            description = "点击查看黑客详情👉"

            # 发送链接消息
            await bot.send_link_message(
                wxid=message["FromWxid"],
                url=detail_url,
                title=title,
                description=description,
                thumb_url=thumb_url
            )

            logger.info(f"[+]: 成功找到并发送黑客信息: {hacker_name} (ID: {hacker_id})")

            # 如果找到多个同名黑客，提示用户
            if len(found_hackers) > 1:
                await bot.send_at_message(message["FromWxid"], f"\n💡 找到{len(found_hackers)}个同名黑客，已显示第一个结果", [message["SenderWxid"]])

        except aiohttp.ClientTimeout:
            logger.error("[-]: 搜索黑客API接口超时")
            await bot.send_at_message(message["FromWxid"], "\n⚠️ 搜索黑客超时，请稍后重试！", [message["SenderWxid"]])
        except aiohttp.ClientError as e:
            logger.error(f"[-]: 网络请求错误: {e}")
            await bot.send_at_message(message["FromWxid"], "\n⚠️ 网络请求失败，请检查网络连接！", [message["SenderWxid"]])
        except Exception as e:
            logger.error(f'[-]: 搜索黑客出现错误, 错误信息: {e}')
            await bot.send_at_message(message["FromWxid"], "\n⚠️ 搜索黑客出现未知错误！", [message["SenderWxid"]])
