# 文件下载插件

这个插件用于自动下载微信文件，使用 XML 消息处理器来检测和下载文件。

## 功能

1. 自动检测文件消息并下载
2. 支持通过命令手动触发下载

## 使用方法

### 自动下载

当有人发送文件消息时，插件会自动检测并下载文件。下载完成后，会发送一条包含文件信息和保存路径的消息。

### 手动下载

如果自动下载功能被禁用，可以通过发送 `下载文件` 命令来触发下载。

## 配置选项

在 `config.toml` 文件中可以配置以下选项：

```toml
[basic]
# 是否启用插件
enable = true
# 是否自动下载文件
auto_download = true
```

- `enable`: 是否启用插件
- `auto_download`: 是否自动下载文件，如果设置为 `false`，则只显示文件信息，不自动下载

## 下载的文件

下载的文件将保存在插件目录下的 `downloads` 文件夹中，文件名会保留原始文件名和扩展名。
