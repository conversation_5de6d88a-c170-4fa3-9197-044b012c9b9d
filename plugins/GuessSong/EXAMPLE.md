# 音乐猜歌插件使用示例

## 基本使用流程

### 1. 启动游戏
在群聊或私聊中发送触发指令：
```
猜歌
```

### 2. 系统响应
```
-----音乐猜歌-----
🎮 猜歌游戏开始！
🎵 第1/1轮
⏰ 答题时间：3分钟
🎯 答对奖励：5积分
💡 请根据音乐猜出对应的歌名！
```

然后系统会发送一段音频文件（即下载即发送）。

### 3. 用户抢答
用户直接发送歌名：
```
勇气大爆发
```

### 4. 答对响应
```
🎉 恭喜 张三 答对了！
🎯 获得 5 积分奖励！
💰 当前积分：105
```

### 5. 显示答案
```
-----音乐猜歌答案-----
🎵 歌名：勇气大爆发
🎤 歌手：土豆王国小乐队
⏱️ 时长：34秒
```

### 6. 显示排行榜（仅群聊）
```
-----音乐猜歌排行榜-----
🥇 张三 - 15次
🥈 李四 - 12次
🥉 王五 - 8次
4. 赵六 - 5次
5. 虚以待位
```

## 多轮游戏示例

### 配置多轮游戏
在 `config.toml` 中设置：
```toml
game_rounds = 3  # 设置为3轮
```

### 游戏流程
第1轮结束后：
```
-----第1轮答案-----
🎵 歌名：勇气大爆发
🎤 歌手：土豆王国小乐队
⏱️ 时长：34秒

准备下一轮...
```

等待3秒后自动开始第2轮：
```
-----音乐猜歌-----
🎮 第2/3轮开始！
⏰ 答题时间：3分钟
🎯 答对奖励：5积分
💡 请根据音乐猜出对应的歌名！
```

最后一轮结束时：
```
-----第3/3轮答案-----
🎵 歌名：最后一首歌
🎤 歌手：某歌手
⏱️ 时长：45秒

🎮 游戏结束！共3轮
```

## 帮助指令示例

用户发送：
```
猜歌帮助
```

系统响应：
```
-----音乐猜歌-----
🎮 游戏指令：
• 猜歌 - 开始游戏
• 猜歌名 - 开始游戏
• 音乐猜歌 - 开始游戏
• 开始猜歌 - 开始游戏

🆘 帮助指令：
• 猜歌帮助 - 显示此帮助信息
• 音乐猜歌帮助 - 显示此帮助信息
• 猜歌说明 - 显示此帮助信息

🎯 游戏规则（抢答模式）：
• 答题时间限制：3分钟
• 第一个答对的人获胜并立刻结束游戏
• 答对奖励：5积分
• 答错不扣分，忽略不响应
• 游戏结束后显示详细答案信息和排行榜

📊 排行榜功能：
• 私聊中无排行榜显示
• 群聊中显示TOP5排行榜
• 每个群聊排行榜独立
• 显示昵称而非wxid

⚙️ 限制设置：
• 每个群聊每天最多20次游戏
• 支持多轮游戏模式
```

## 限制情况示例

### 每日次数限制
当群聊达到每日游戏次数上限时：
```
-----音乐猜歌-----
❌ 今日游戏次数已达上限(20次)，请明天再来！
```

### 游戏进行中
当游戏正在进行时再次触发：
```
-----音乐猜歌-----
🎮 游戏正在进行中，请等待当前游戏结束！
```

### 超时情况
当答题时间到时：
```
-----音乐猜歌答案-----
🎵 歌名：某首歌
🎤 歌手：某歌手
⏱️ 时长：30秒
⏰ 时间到！没有人答对。
```

## 私聊与群聊的区别

### 私聊
- 无排行榜显示
- 无每日次数限制
- 答对后只显示积分奖励和答案

### 群聊
- 显示排行榜
- 有每日次数限制
- 答对后显示积分奖励、答案和排行榜
- 支持多人抢答

## 配置自定义示例

### 自定义触发指令
```toml
trigger_commands = ["听歌猜名", "音乐竞猜", "歌曲竞答"]
```

### 自定义奖励和限制
```toml
reward_points = 10        # 答对奖励10积分
answer_time_limit = 5     # 5分钟答题时间
leaderboard_top_count = 10 # 显示TOP10
daily_game_limit = 50     # 每日50次限制
```
