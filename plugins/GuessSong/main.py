import asyncio
import json
import os
import re
import tomllib
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from io import BytesIO

import aiohttp
from loguru import logger
from pydub import AudioSegment

from WechatAPI import WechatAPIClient
from database.XYBotDB import XYBotDB
from utils.decorators import *
from utils.plugin_base import PluginBase


class GuessSong(PluginBase):
    description = "音乐猜歌名 - 通过播放音乐片段，让群成员抢答对应的歌名"
    author = "Assistant"
    version = "1.0.0"

    def __init__(self):
        super().__init__()
        
        # 获取配置文件路径
        config_path = os.path.join(os.path.dirname(__file__), "config.toml")
        
        try:
            with open(config_path, "rb") as f:
                config = tomllib.load(f)

            # 读取配置
            plugin_config = config.get("GuessSong", {})
            self.enable = plugin_config.get("enable", False)

            # 游戏设置
            self.trigger_commands = plugin_config.get("trigger_commands", ["猜歌"])
            self.game_rounds = plugin_config.get("game_rounds", 1)
            self.answer_time_limit = plugin_config.get("answer_time_limit", 3)  # 分钟
            self.reward_points = plugin_config.get("reward_points", 5)
            self.leaderboard_top_count = plugin_config.get("leaderboard_top_count", 5)
            self.daily_game_limit = plugin_config.get("daily_game_limit", 20)
            
            # API配置
            self.api_secret = plugin_config.get("api_secret", "6c3b0ca62dd045e9dcf15e3dcbe4c03a")
            self.command_format = plugin_config.get("command_format", "")

            # 音频处理配置
            self.max_audio_size_str = plugin_config.get("max_audio_size", "3MB")
            self.audio_quality = plugin_config.get("audio_quality", "medium")
            self.max_audio_size_bytes = self._parse_size_string(self.max_audio_size_str)

        except Exception as e:
            logger.error(f"加载GuessSong配置文件失败: {str(e)}")
            self.enable = False

        # 初始化数据库和存储
        self.db = XYBotDB()
        self.active_games = {}  # group_id -> game_state
        self.leaderboard_file = os.path.join(os.path.dirname(__file__), "leaderboard.json")
        self.daily_count_file = os.path.join(os.path.dirname(__file__), "daily_count.json")

        # 加载排行榜数据
        self.leaderboard_data = self._load_leaderboard()
        self.daily_count_data = self._load_daily_count()

    async def async_init(self):
        return

    def _parse_size_string(self, size_str: str) -> int:
        """解析大小字符串，返回字节数"""
        try:
            # 使用正则表达式解析大小字符串
            match = re.match(r'^(\d+(?:\.\d+)?)\s*(KB|MB|GB)?$', size_str.upper().strip())
            if not match:
                logger.warning(f"无效的大小格式: {size_str}，使用默认值2MB")
                return 2 * 1024 * 1024  # 默认2MB

            number = float(match.group(1))
            unit = match.group(2) or 'B'

            multipliers = {
                'B': 1,
                'KB': 1024,
                'MB': 1024 * 1024,
                'GB': 1024 * 1024 * 1024
            }

            result = int(number * multipliers[unit])
            logger.info(f"音频大小限制: {size_str} = {result} 字节")
            return result

        except Exception as e:
            logger.error(f"解析大小字符串失败: {e}")
            return 2 * 1024 * 1024  # 默认2MB

    def _load_leaderboard(self) -> dict:
        """加载排行榜数据"""
        try:
            if os.path.exists(self.leaderboard_file):
                with open(self.leaderboard_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            logger.error(f"加载排行榜数据失败: {e}")
        return {}

    def _save_leaderboard(self):
        """保存排行榜数据"""
        try:
            with open(self.leaderboard_file, 'w', encoding='utf-8') as f:
                json.dump(self.leaderboard_data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存排行榜数据失败: {e}")

    def _load_daily_count(self) -> dict:
        """加载每日游戏次数数据"""
        try:
            if os.path.exists(self.daily_count_file):
                with open(self.daily_count_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    # 清理过期数据
                    today = datetime.now().strftime("%Y-%m-%d")
                    return {k: v for k, v in data.items() if k.endswith(today)}
        except Exception as e:
            logger.error(f"加载每日游戏次数数据失败: {e}")
        return {}

    def _save_daily_count(self):
        """保存每日游戏次数数据"""
        try:
            with open(self.daily_count_file, 'w', encoding='utf-8') as f:
                json.dump(self.daily_count_data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存每日游戏次数数据失败: {e}")

    def _check_daily_limit(self, group_id: str) -> bool:
        """检查每日游戏次数限制"""
        today = datetime.now().strftime("%Y-%m-%d")
        key = f"{group_id}_{today}"
        current_count = self.daily_count_data.get(key, 0)
        return current_count < self.daily_game_limit

    def _increment_daily_count(self, group_id: str):
        """增加每日游戏次数"""
        today = datetime.now().strftime("%Y-%m-%d")
        key = f"{group_id}_{today}"
        self.daily_count_data[key] = self.daily_count_data.get(key, 0) + 1
        self._save_daily_count()

    async def _fetch_song_data(self) -> Optional[dict]:
        """获取歌曲数据"""
        try:
            async with aiohttp.ClientSession() as session:
                url = "https://api.dudunas.top/api/guesssong"
                params = {"AppSecret": self.api_secret}
                
                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        if data.get("code") == 200:
                            song_info = data.get("data", {})
                            return {
                                'song_name': song_info.get("song", {}).get("name", ""),
                                'singer': song_info.get("song", {}).get("singer", ""),
                                'audio_url': song_info.get("audio", {}).get("url", ""),
                                'duration': song_info.get("audio", {}).get("duration", 0),
                                'lyrics': song_info.get("song", {}).get("lyrics", [])
                            }
        except Exception as e:
            logger.error(f"获取歌曲数据失败: {e}")
        return None

    async def _download_audio(self, audio_url: str) -> bytes:
        """下载音频文件"""
        try:
            logger.info(f"🔽 准备下载音频: {audio_url}")

            # 检查URL的有效性
            if not audio_url or not audio_url.startswith('http'):
                logger.error(f"❌ 无效的音频URL: {audio_url}")
                return None

            # 下载音频
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }

            download_start_time = datetime.now()

            async with aiohttp.ClientSession() as session:
                logger.info(f"🌐 开始HTTP请求: {audio_url}")
                async with session.get(audio_url, timeout=30, headers=headers) as response:
                    download_time = (datetime.now() - download_start_time).total_seconds()
                    logger.info(f"📊 HTTP响应状态: {response.status} (耗时: {download_time:.2f}秒)")
                    logger.info(f"📋 响应头: {dict(response.headers)}")

                    if response.status == 200:
                        content_length = response.headers.get('Content-Length')
                        if content_length:
                            logger.info(f"📏 预期文件大小: {content_length} 字节")

                        audio_data = await response.read()
                        total_time = (datetime.now() - download_start_time).total_seconds()
                        logger.info(f"✅ 音频下载完成: {len(audio_data)} 字节 (总耗时: {total_time:.2f}秒)")

                        if len(audio_data) == 0:
                            logger.error("❌ 下载的音频数据为空")
                            return None

                        # 验证音频数据
                        if not self._validate_audio_data(audio_data):
                            logger.error("❌ 下载的音频数据无效")
                            return None

                        # 检查并压缩音频
                        compressed_audio = await self._compress_audio_if_needed(audio_data)
                        return compressed_audio
                    else:
                        logger.error(f"❌ 音频下载失败: HTTP {response.status}")
                        response_text = await response.text()
                        logger.error(f"📄 响应内容: {response_text[:500]}")
                        return None

        except asyncio.TimeoutError:
            logger.error("❌ 音频下载超时")
            return None
        except Exception as e:
            logger.error(f"❌ 下载音频失败: {str(e)}")
            logger.error(f"🔍 错误类型: {type(e).__name__}")
            import traceback
            logger.error(f"📋 详细错误信息: {traceback.format_exc()}")
            return None

    def _validate_audio_data(self, audio_data: bytes) -> bool:
        """验证音频数据的有效性"""
        try:
            if len(audio_data) < 44:  # WAV文件头至少44字节
                logger.warning("音频数据太小，可能无效")
                return False

            # 检查WAV文件头
            if audio_data[:4] == b'RIFF' and audio_data[8:12] == b'WAVE':
                logger.info("✅ 检测到有效的WAV文件头")
                return True

            # 检查MP3文件头
            if audio_data[:3] == b'ID3' or (audio_data[0] == 0xFF and (audio_data[1] & 0xE0) == 0xE0):
                logger.info("✅ 检测到有效的MP3文件头")
                return True

            logger.warning(f"⚠️ 未知音频格式，头部: {audio_data[:16].hex()}")
            return False

        except Exception as e:
            logger.error(f"验证音频数据失败: {e}")
            return False



    async def _compress_audio_if_needed(self, audio_data: bytes) -> bytes:
        """如果音频超过大小限制，则进行压缩"""
        try:
            original_size = len(audio_data)
            logger.info(f"🎵 原始音频大小: {original_size} 字节 ({original_size / 1024 / 1024:.2f} MB)")
            logger.info(f"🎵 大小限制: {self.max_audio_size_bytes} 字节 ({self.max_audio_size_bytes / 1024 / 1024:.2f} MB)")

            # 验证音频数据
            if not self._validate_audio_data(audio_data):
                logger.warning("⚠️ 音频数据验证失败，返回原始数据")
                return audio_data

            if original_size <= self.max_audio_size_bytes:
                logger.info("✅ 音频大小在限制范围内，直接使用")
                return audio_data

            logger.warning(f"⚠️ 音频超出大小限制 ({original_size / 1024 / 1024:.2f} MB > {self.max_audio_size_bytes / 1024 / 1024:.2f} MB)")
            logger.info("🔧 开始压缩音频...")

            # 进行音频压缩
            compressed_audio = await self._compress_audio_with_ffmpeg(audio_data)
            if compressed_audio:
                compressed_size = len(compressed_audio)
                logger.info(f"✅ 音频压缩完成: {compressed_size} 字节 ({compressed_size / 1024 / 1024:.2f} MB)")
                logger.info(f"📉 压缩比: {(1 - compressed_size / original_size) * 100:.1f}%")
                return compressed_audio
            else:
                logger.warning("⚠️ 音频压缩失败，使用原始音频")
                return audio_data

        except Exception as e:
            logger.error(f"❌ 音频处理失败: {e}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            logger.warning("🔄 返回原始音频")
            return audio_data

    async def _compress_audio_with_ffmpeg(self, audio_data: bytes) -> bytes:
        """使用ffmpeg压缩音频"""
        import tempfile
        import os
        import subprocess

        try:
            # 根据质量等级设置压缩参数
            quality_settings = {
                "low": {"bitrate": "32k", "sample_rate": "22050"},
                "medium": {"bitrate": "64k", "sample_rate": "44100"},
                "high": {"bitrate": "128k", "sample_rate": "44100"}
            }

            settings = quality_settings.get(self.audio_quality, quality_settings["medium"])
            logger.info(f"🎛️ 使用压缩设置: {self.audio_quality} - 比特率: {settings['bitrate']}, 采样率: {settings['sample_rate']}")

            # 创建临时文件
            with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as input_file:
                input_file.write(audio_data)
                input_path = input_file.name

            with tempfile.NamedTemporaryFile(suffix='.mp3', delete=False) as output_file:
                output_path = output_file.name

            try:
                # 使用ffmpeg进行压缩
                cmd = [
                    'ffmpeg', '-y',  # -y 覆盖输出文件
                    '-i', input_path,  # 输入文件
                    '-acodec', 'mp3',  # 音频编码器
                    '-ab', settings['bitrate'],  # 比特率
                    '-ar', settings['sample_rate'],  # 采样率
                    '-ac', '1',  # 单声道
                    '-f', 'mp3',  # 输出格式
                    output_path
                ]

                logger.info(f"🔧 执行ffmpeg命令: {' '.join(cmd)}")

                # 执行ffmpeg命令
                result = subprocess.run(
                    cmd,
                    capture_output=True,
                    text=True,
                    timeout=30  # 30秒超时
                )

                if result.returncode == 0:
                    # 读取压缩后的音频数据
                    with open(output_path, 'rb') as f:
                        compressed_data = f.read()

                    logger.info(f"✅ ffmpeg压缩成功，输出大小: {len(compressed_data)} 字节")
                    return compressed_data
                else:
                    logger.error(f"❌ ffmpeg压缩失败: {result.stderr}")
                    return None

            finally:
                # 清理临时文件
                try:
                    os.unlink(input_path)
                    os.unlink(output_path)
                except:
                    pass

        except subprocess.TimeoutExpired:
            logger.error("❌ ffmpeg压缩超时")
            return None
        except FileNotFoundError:
            logger.error("❌ 未找到ffmpeg，请确保已安装ffmpeg")
            return None
        except Exception as e:
            logger.error(f"❌ 音频压缩异常: {e}")
            return None

    def _detect_audio_format(self, audio_data: bytes) -> str:
        """检测音频格式"""
        if len(audio_data) >= 4:
            header = audio_data[:4]
            if header == b'RIFF':
                return "wav"
            elif header[:3] == b'ID3' or header[:2] == b'\xff\xfb':
                return "mp3"
        return "wav"  # 默认返回wav

    async def _send_voice_with_smart_format(self, bot: WechatAPIClient, chat_id: str, audio_data: bytes) -> tuple:
        """智能选择格式发送语音"""
        # 检测音频格式
        detected_format = self._detect_audio_format(audio_data)
        logger.info(f"🎵 检测到音频格式: {detected_format}")

        # 首先尝试检测到的格式
        try:
            logger.info(f"尝试{detected_format}格式发送...")
            result = await bot.send_voice_message(chat_id, audio_data, format=detected_format)
            logger.info(f"✅ {detected_format}格式发送成功: {result}")
            return result
        except Exception as e:
            logger.warning(f"❌ {detected_format}格式发送失败: {str(e)}")

            # 尝试另一种格式
            fallback_format = "mp3" if detected_format == "wav" else "wav"
            try:
                logger.info(f"尝试{fallback_format}格式发送...")
                result = await bot.send_voice_message(chat_id, audio_data, format=fallback_format)
                logger.info(f"✅ {fallback_format}格式发送成功: {result}")
                return result
            except Exception as e2:
                logger.error(f"❌ {fallback_format}格式也发送失败: {str(e2)}")
                raise e2

    async def _check_voice_send_capability(self, bot: WechatAPIClient, chat_id: str) -> bool:
        """检查语音发送能力"""
        try:
            logger.info(f"🔍 检查语音发送能力: {chat_id}")

            # 检查是否是群聊
            is_group = "@chatroom" in chat_id
            logger.info(f"📍 聊天类型: {'群聊' if is_group else '私聊'}")

            # 移除群聊成员列表检查，因为这不是语音发送的必要条件
            # 群聊成员列表获取失败不应该阻止语音发送
            if is_group:
                logger.info("📝 群聊模式，跳过成员列表检查")

            logger.info("✅ 语音发送能力检查通过")
            return True

        except Exception as e:
            logger.error(f"❌ 检查语音发送能力失败: {e}")
            return False

    async def _test_audio_download(self, bot: WechatAPIClient, chat_id: str):
        """测试音频下载和发送"""
        try:
            await bot.send_text_message(chat_id, "🔧 开始测试音频下载...")

            # 获取歌曲数据
            song_data = await self._fetch_song_data()
            if not song_data:
                await bot.send_text_message(chat_id, "❌ 获取歌曲数据失败")
                return

            await bot.send_text_message(chat_id, f"✅ 获取歌曲数据成功\n歌名: {song_data['song_name']}\n歌手: {song_data['singer']}\n音频URL: {song_data['audio_url']}")

            # 测试音频下载
            logger.info(f"测试下载音频: {song_data['audio_url']}")
            audio_data = await self._download_audio(song_data['audio_url'])

            if audio_data:
                await bot.send_text_message(chat_id, f"✅ 音频下载成功，大小: {len(audio_data)} 字节")

                # 测试发送语音
                try:
                    # 先尝试wav格式
                    result = await bot.send_voice_message(chat_id, audio_data, format="wav")
                    await bot.send_text_message(chat_id, f"✅ 语音发送成功(wav)，结果: {result}")
                except Exception as e:
                    await bot.send_text_message(chat_id, f"❌ wav格式发送失败: {str(e)}")
                    try:
                        # 再尝试mp3格式
                        result = await bot.send_voice_message(chat_id, audio_data, format="mp3")
                        await bot.send_text_message(chat_id, f"✅ 语音发送成功(mp3)，结果: {result}")
                    except Exception as e2:
                        await bot.send_text_message(chat_id, f"❌ mp3格式也发送失败: {str(e2)}")
                        logger.error(f"语音发送失败: {e2}")
            else:
                await bot.send_text_message(chat_id, "❌ 音频下载失败")

        except Exception as e:
            logger.error(f"测试音频下载失败: {e}")
            await bot.send_text_message(chat_id, f"❌ 测试失败: {str(e)}")

    async def _test_game_flow(self, bot: WechatAPIClient, chat_id: str):
        """测试完整游戏流程"""
        try:
            await bot.send_text_message(chat_id, "🔧 开始测试完整游戏流程...")

            # 模拟正式游戏流程
            is_group = "@chatroom" in chat_id
            await self._start_game(bot, chat_id, is_group)

        except Exception as e:
            logger.error(f"测试游戏流程失败: {e}")
            await bot.send_text_message(chat_id, f"❌ 游戏流程测试失败: {str(e)}")

    async def _test_nickname_fetch(self, bot: WechatAPIClient, chat_id: str, sender_wxid: str):
        """测试昵称获取功能"""
        try:
            await bot.send_text_message(chat_id, "🔧 开始测试昵称获取...")

            # 使用简化的昵称获取方法
            try:
                nickname = await bot.get_nickname(sender_wxid)
                if not nickname:
                    nickname = "未知用户"
            except:
                nickname = "未知用户"

            await bot.send_text_message(chat_id, f"✅ 昵称获取结果:\nwxid: {sender_wxid}\n昵称: {nickname}")

        except Exception as e:
            logger.error(f"测试昵称获取失败: {e}")
            await bot.send_text_message(chat_id, f"❌ 昵称获取测试失败: {str(e)}")

    async def _test_voice_send(self, bot: WechatAPIClient, chat_id: str):
        """测试语音发送功能"""
        try:
            await bot.send_text_message(chat_id, "🔧 开始测试语音发送功能...")

            # 检查语音发送能力
            if not await self._check_voice_send_capability(bot, chat_id):
                await bot.send_text_message(chat_id, "❌ 语音发送能力检查失败")
                return

            # 获取测试音频
            song_data = await self._fetch_song_data()
            if not song_data:
                await bot.send_text_message(chat_id, "❌ 获取测试音频数据失败")
                return

            await bot.send_text_message(chat_id, f"✅ 获取测试音频成功\n歌名: {song_data['song_name']}\n歌手: {song_data['singer']}")

            # 下载音频
            audio_data = await self._download_audio(song_data['audio_url'])
            if not audio_data:
                await bot.send_text_message(chat_id, "❌ 音频下载失败")
                return

            await bot.send_text_message(chat_id, f"✅ 音频下载成功，大小: {len(audio_data)} 字节")

            # 测试发送语音
            try:
                result = await bot.send_voice_message(chat_id, audio_data, format="wav")
                await bot.send_text_message(chat_id, f"✅ 语音发送成功: {result}")
            except Exception as e:
                await bot.send_text_message(chat_id, f"❌ 语音发送失败: {str(e)}")
                logger.error(f"语音发送测试失败: {e}")

        except Exception as e:
            logger.error(f"测试语音发送失败: {e}")
            await bot.send_text_message(chat_id, f"❌ 语音发送测试失败: {str(e)}")

    async def _test_voice_send_simple(self, bot: WechatAPIClient, chat_id: str):
        """简单的语音发送测试，跳过所有检查"""
        try:
            await bot.send_text_message(chat_id, "🔧 开始简单语音发送测试...")

            # 直接获取音频数据并发送，跳过所有检查
            song_data = await self._fetch_song_data()
            if not song_data:
                await bot.send_text_message(chat_id, "❌ 获取音频数据失败")
                return

            await bot.send_text_message(chat_id, f"✅ 获取音频数据成功: {song_data['song_name']}")

            # 直接下载和发送
            audio_data = await self._download_audio(song_data['audio_url'])
            if not audio_data:
                await bot.send_text_message(chat_id, "❌ 音频下载失败")
                return

            await bot.send_text_message(chat_id, f"✅ 音频下载成功: {len(audio_data)} 字节")

            # 直接发送语音，不做任何检查
            logger.info("🎵 直接发送语音消息...")
            result = await bot.send_voice_message(chat_id, audio_data, format="wav")
            logger.info(f"✅ 语音发送结果: {result}")

            # 解析返回结果
            if isinstance(result, tuple) and len(result) >= 3:
                msg_id, timestamp, new_msg_id = result
                await bot.send_text_message(chat_id,
                    f"✅ 语音发送成功!\n"
                    f"📨 消息ID: {msg_id}\n"
                    f"⏰ 时间戳: {timestamp}\n"
                    f"🆔 NewMsgId: {new_msg_id}\n"
                    f"🎵 音频时长: {len(audio_data)} 字节\n"
                    f"💡 如果没收到语音，可能是微信客户端问题")
            else:
                await bot.send_text_message(chat_id, f"✅ 语音发送成功: {result}")

            # 等待3秒后发送确认消息
            await asyncio.sleep(3)
            await bot.send_text_message(chat_id, "🔍 请确认是否收到了语音消息？如果没有，可能是微信客户端缓存或网络问题。")

        except Exception as e:
            logger.error(f"简单语音发送测试失败: {e}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            await bot.send_text_message(chat_id, f"❌ 简单语音发送测试失败: {str(e)}")

    async def _test_small_audio(self, bot: WechatAPIClient, chat_id: str):
        """测试发送小音频文件"""
        try:
            await bot.send_text_message(chat_id, "🔧 开始小音频测试...")

            # 创建一个最小的WAV文件（1秒静音）
            # WAV文件头 + 1秒44100Hz 16bit单声道静音数据
            wav_header = bytes([
                0x52, 0x49, 0x46, 0x46,  # "RIFF"
                0x24, 0x16, 0x01, 0x00,  # 文件大小-8
                0x57, 0x41, 0x56, 0x45,  # "WAVE"
                0x66, 0x6D, 0x74, 0x20,  # "fmt "
                0x10, 0x00, 0x00, 0x00,  # fmt chunk大小
                0x01, 0x00,              # 音频格式(PCM)
                0x01, 0x00,              # 声道数(1)
                0x44, 0xAC, 0x00, 0x00,  # 采样率(44100)
                0x88, 0x58, 0x01, 0x00,  # 字节率
                0x02, 0x00,              # 块对齐
                0x10, 0x00,              # 位深度(16)
                0x64, 0x61, 0x74, 0x61,  # "data"
                0x00, 0x16, 0x01, 0x00   # 数据大小
            ])

            # 1秒静音数据 (44100 samples * 2 bytes = 88200 bytes)
            silence_data = b'\x00' * 88200
            small_audio = wav_header + silence_data

            logger.info(f"🎵 创建小音频文件: {len(small_audio)} 字节")
            await bot.send_text_message(chat_id, f"✅ 创建小音频: {len(small_audio)} 字节")

            # 发送小音频
            logger.info("🎵 发送小音频...")
            result = await bot.send_voice_message(chat_id, small_audio, format="wav")
            logger.info(f"✅ 小音频发送结果: {result}")

            await bot.send_text_message(chat_id, f"✅ 小音频发送成功: {result}")
            await asyncio.sleep(2)
            await bot.send_text_message(chat_id, "🔍 请确认是否收到了1秒静音的语音消息？")

        except Exception as e:
            logger.error(f"小音频测试失败: {e}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            await bot.send_text_message(chat_id, f"❌ 小音频测试失败: {str(e)}")

    async def _test_no_compression(self, bot: WechatAPIClient, chat_id: str):
        """测试不压缩的音频发送"""
        try:
            await bot.send_text_message(chat_id, "🔧 开始无压缩音频测试...")

            # 获取音频数据
            song_data = await self._fetch_song_data()
            if not song_data:
                await bot.send_text_message(chat_id, "❌ 获取音频数据失败")
                return

            await bot.send_text_message(chat_id, f"✅ 获取音频数据成功: {song_data['song_name']}")

            # 直接下载，不压缩
            logger.info("🔽 直接下载音频，跳过压缩...")

            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }

            async with aiohttp.ClientSession() as session:
                async with session.get(song_data['audio_url'], timeout=30, headers=headers) as response:
                    if response.status == 200:
                        audio_data = await response.read()
                        logger.info(f"✅ 原始音频下载成功: {len(audio_data)} 字节")

                        await bot.send_text_message(chat_id, f"✅ 原始音频下载成功: {len(audio_data)} 字节")

                        # 直接发送，不压缩
                        logger.info("🎵 直接发送原始音频...")
                        result = await bot.send_voice_message(chat_id, audio_data, format="wav")
                        logger.info(f"✅ 原始音频发送结果: {result}")

                        await bot.send_text_message(chat_id, f"✅ 原始音频发送成功: {result}")
                    else:
                        await bot.send_text_message(chat_id, f"❌ 音频下载失败: HTTP {response.status}")

        except Exception as e:
            logger.error(f"无压缩音频测试失败: {e}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            await bot.send_text_message(chat_id, f"❌ 无压缩音频测试失败: {str(e)}")

    async def _test_api_audio(self, bot: WechatAPIClient, chat_id: str):
        """测试API返回的音频数据质量"""
        try:
            await bot.send_text_message(chat_id, "🔧 开始API音频数据测试...")

            # 获取多个音频样本进行测试
            for i in range(3):
                await bot.send_text_message(chat_id, f"📡 获取第{i+1}个音频样本...")

                song_data = await self._fetch_song_data()
                if not song_data:
                    await bot.send_text_message(chat_id, f"❌ 第{i+1}个样本获取失败")
                    continue

                await bot.send_text_message(chat_id,
                    f"✅ 第{i+1}个样本: {song_data['song_name']}\n"
                    f"🎤 歌手: {song_data['singer']}\n"
                    f"🔗 URL: {song_data['audio_url'][:50]}...")

                # 下载并检查音频数据
                headers = {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
                }

                async with aiohttp.ClientSession() as session:
                    async with session.get(song_data['audio_url'], timeout=30, headers=headers) as response:
                        if response.status == 200:
                            audio_data = await response.read()

                            # 分析音频数据
                            size_mb = len(audio_data) / 1024 / 1024
                            is_valid = self._validate_audio_data(audio_data)

                            header_info = ""
                            if len(audio_data) >= 16:
                                header_info = f"头部: {audio_data[:16].hex()}"

                            await bot.send_text_message(chat_id,
                                f"📊 第{i+1}个样本分析:\n"
                                f"📏 大小: {size_mb:.2f} MB\n"
                                f"✅ 有效: {'是' if is_valid else '否'}\n"
                                f"🔍 {header_info}")

                            # 如果数据有效，尝试发送
                            if is_valid and size_mb < 10:  # 小于10MB才尝试发送
                                try:
                                    result = await bot.send_voice_message(chat_id, audio_data, format="wav")
                                    await bot.send_text_message(chat_id, f"✅ 第{i+1}个样本发送成功")
                                except Exception as e:
                                    await bot.send_text_message(chat_id, f"❌ 第{i+1}个样本发送失败: {str(e)}")
                            else:
                                await bot.send_text_message(chat_id, f"⚠️ 第{i+1}个样本跳过发送（无效或过大）")
                        else:
                            await bot.send_text_message(chat_id, f"❌ 第{i+1}个样本下载失败: HTTP {response.status}")

                # 间隔2秒
                await asyncio.sleep(2)

            await bot.send_text_message(chat_id, "🏁 API音频数据测试完成")

        except Exception as e:
            logger.error(f"API音频测试失败: {e}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            await bot.send_text_message(chat_id, f"❌ API音频测试失败: {str(e)}")

    @on_text_message(priority=10)
    async def handle_text(self, bot: WechatAPIClient, message: dict):
        """处理文本消息"""
        if not self.enable:
            return True

        content = str(message["Content"]).strip()
        from_wxid = message["FromWxid"]
        sender_wxid = message.get("SenderWxid", from_wxid)
        
        # 检查是否是帮助指令
        if content in ["猜歌帮助", "音乐猜歌帮助", "猜歌说明"]:
            await bot.send_text_message(from_wxid, self.command_format)
            return False

        # 检查是否是测试指令
        if content == "测试猜歌音频":
            await self._test_audio_download(bot, from_wxid)
            return False

        # 检查是否是测试游戏流程指令
        if content == "测试猜歌游戏":
            await self._test_game_flow(bot, from_wxid)
            return False

        # 检查是否是测试昵称获取指令
        if content == "测试猜歌昵称":
            await self._test_nickname_fetch(bot, from_wxid, sender_wxid)
            return False

        # 检查是否是测试语音发送指令
        if content == "测试猜歌语音":
            await self._test_voice_send(bot, from_wxid)
            return False

        # 检查是否是简单语音测试指令
        if content == "测试猜歌语音简单":
            await self._test_voice_send_simple(bot, from_wxid)
            return False

        # 检查是否是小音频测试指令
        if content == "测试猜歌小音频":
            await self._test_small_audio(bot, from_wxid)
            return False

        # 检查是否是无压缩测试指令
        if content == "测试猜歌无压缩":
            await self._test_no_compression(bot, from_wxid)
            return False

        # 检查是否是API音频测试指令
        if content == "测试猜歌API":
            await self._test_api_audio(bot, from_wxid)
            return False

        # 检查是否是触发指令
        if any(content == cmd for cmd in self.trigger_commands):
            # 检查是否是群聊
            is_group = "@chatroom" in from_wxid

            if is_group:
                # 检查每日游戏次数限制
                if not self._check_daily_limit(from_wxid):
                    await bot.send_text_message(from_wxid,
                        f"-----音乐猜歌-----\n❌ 今日游戏次数已达上限({self.daily_game_limit}次)，请明天再来！")
                    return False

                # 增加游戏次数
                self._increment_daily_count(from_wxid)

            # 开始游戏
            await self._start_game(bot, from_wxid, is_group)
            return False
        
        # 检查是否是游戏答案
        if from_wxid in self.active_games:
            await self._check_answer(bot, message, content, sender_wxid, from_wxid)
            return False
        
        return True

    async def _start_game(self, bot: WechatAPIClient, chat_id: str, is_group: bool):
        """开始猜歌游戏"""
        logger.info(f"开始游戏: chat_id={chat_id}, is_group={is_group}")

        if chat_id in self.active_games:
            logger.info(f"游戏已在进行中: {chat_id}")
            await bot.send_text_message(chat_id, "-----音乐猜歌-----\n🎮 游戏正在进行中，请等待当前游戏结束！")
            return

        # 获取歌曲数据
        logger.info("开始获取歌曲数据")
        song_data = await self._fetch_song_data()
        if not song_data:
            logger.error("获取歌曲数据失败")
            await bot.send_text_message(chat_id, "-----音乐猜歌-----\n❌ 获取歌曲数据失败，请稍后再试！")
            return

        logger.info(f"获取歌曲数据成功: {song_data['song_name']} - {song_data['singer']}")

        # 初始化游戏状态
        game_state = {
            'song_name': song_data['song_name'],
            'singer': song_data['singer'],
            'audio_url': song_data['audio_url'],
            'duration': song_data['duration'],
            'lyrics': song_data['lyrics'],
            'start_time': datetime.now(),
            'is_group': is_group,
            'current_round': 1,
            'total_rounds': self.game_rounds if self.game_rounds > 0 else 1,
            'participants': {},  # wxid -> {'nickname': str, 'correct_count': int}
            'answered_correctly': set()  # 已答对的用户wxid
        }

        self.active_games[chat_id] = game_state
        logger.info(f"游戏状态已保存: {chat_id}")

        # 发送游戏开始消息
        start_message = (
            f"-----音乐猜歌-----\n"
            f"🎮 猜歌游戏开始！\n"
            f"🎵 第{game_state['current_round']}/{game_state['total_rounds']}轮\n"
            f"⏰ 答题时间：{self.answer_time_limit}分钟\n"
            f"🎯 答对奖励：{self.reward_points}积分\n"
            f"💡 请根据音乐猜出对应的歌名！"
        )

        logger.info("发送游戏开始消息")
        await bot.send_text_message(chat_id, start_message)

        # 下载并发送音频
        audio_sent_successfully = False
        try:
            logger.info(f"=== 开始音频处理流程 ===")
            logger.info(f"群聊ID: {chat_id}")
            logger.info(f"音频URL: {song_data['audio_url']}")

            # 检查语音发送能力
            if not await self._check_voice_send_capability(bot, chat_id):
                logger.error("❌ 语音发送能力检查失败")
                await bot.send_text_message(chat_id, f"语音发送功能异常，请访问：{song_data['audio_url']}")
                return

            audio_data = await self._download_audio(song_data['audio_url'])
            if audio_data:
                logger.info(f"音频下载成功，数据大小: {len(audio_data)} 字节")

                # 检查音频数据的有效性
                if len(audio_data) < 1000:  # 小于1KB可能是无效数据
                    logger.warning(f"音频数据过小，可能无效: {len(audio_data)} 字节")

                # 记录音频数据的前几个字节用于调试
                header_bytes = audio_data[:16] if len(audio_data) >= 16 else audio_data
                logger.info(f"音频数据头部: {header_bytes.hex()}")

                # 智能发送语音消息
                logger.info("准备发送语音消息...")
                try:
                    result = await self._send_voice_with_smart_format(bot, chat_id, audio_data)
                    audio_sent_successfully = True
                except Exception as e:
                    logger.error(f"❌ 语音发送失败: {str(e)}")
                    logger.error(f"错误类型: {type(e).__name__}")
                    raise e
            else:
                logger.error("❌ 音频下载失败，audio_data为空")
                await bot.send_text_message(chat_id, f"音频下载失败，请访问：{song_data['audio_url']}")
        except Exception as e:
            logger.error(f"❌ 发送音频失败: {str(e)}")
            logger.error(f"错误类型: {type(e).__name__}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            await bot.send_text_message(chat_id, f"音频发送失败，请访问：{song_data['audio_url']}")

        logger.info(f"=== 音频处理流程结束，成功: {audio_sent_successfully} ===")

        # 只有在音频发送成功后才设置定时器
        if audio_sent_successfully:
            logger.info(f"音频发送成功，设置游戏定时器: {self.answer_time_limit}分钟")
            asyncio.create_task(self._game_timer(bot, chat_id))
        else:
            logger.error("音频发送失败，不启动游戏定时器")
            # 清理游戏状态
            if chat_id in self.active_games:
                del self.active_games[chat_id]

    async def _game_timer(self, bot: WechatAPIClient, chat_id: str):
        """游戏定时器"""
        logger.info(f"游戏定时器启动: {chat_id}, 等待{self.answer_time_limit}分钟")
        await asyncio.sleep(self.answer_time_limit * 60)  # 转换为秒

        logger.info(f"游戏定时器到期: {chat_id}")
        if chat_id in self.active_games:
            game_state = self.active_games[chat_id]
            logger.info(f"时间到，结束游戏: {chat_id}")

            # 时间到，显示答案
            await self._show_answer_and_end_game(bot, chat_id, None, timeout=True)
        else:
            logger.info(f"游戏已结束，定时器无需处理: {chat_id}")

    async def _check_answer(self, bot: WechatAPIClient, message: dict, content: str, sender_wxid: str, chat_id: str):
        """检查答案"""
        if chat_id not in self.active_games:
            return

        game_state = self.active_games[chat_id]

        # 检查用户是否已经在本轮答对过
        if sender_wxid in game_state['answered_correctly']:
            return

        correct_answer = game_state['song_name'].strip()

        # 检查答案是否正确（精确匹配）
        if content.strip() == correct_answer:
            # 标记用户已答对
            game_state['answered_correctly'].add(sender_wxid)
            # 答对了！
            await self._handle_correct_answer(bot, message, sender_wxid, chat_id)

    async def _handle_correct_answer(self, bot: WechatAPIClient, message: dict, sender_wxid: str, chat_id: str):
        """处理正确答案"""
        game_state = self.active_games[chat_id]

        # 获取用户昵称
        try:
            nickname = await bot.get_nickname(sender_wxid)
            if not nickname:
                nickname = "未知用户"
        except:
            nickname = "未知用户"

        # 奖励积分
        self.db.add_points(sender_wxid, self.reward_points)

        # 更新游戏参与者数据（仅群聊）
        if game_state['is_group']:
            if 'participants' not in game_state:
                game_state['participants'] = {}

            if sender_wxid not in game_state['participants']:
                game_state['participants'][sender_wxid] = {
                    'nickname': nickname,
                    'correct_count': 0
                }

            game_state['participants'][sender_wxid]['correct_count'] += 1
            game_state['participants'][sender_wxid]['nickname'] = nickname  # 更新昵称

        # 发送恭喜消息
        congrats_message = (
            f"🎉 恭喜 {nickname} 答对了！\n"
            f"🎯 获得 {self.reward_points} 积分奖励！\n"
            f"💰 当前积分：{self.db.get_points(sender_wxid)}"
        )

        await bot.send_text_message(chat_id, congrats_message)

        # 检查是否还有下一轮
        if game_state['current_round'] < game_state['total_rounds']:
            # 还有下一轮，继续游戏
            await self._start_next_round(bot, chat_id)
        else:
            # 游戏结束，显示答案和排行榜
            await self._show_answer_and_end_game(bot, chat_id, sender_wxid)

    def _load_group_leaderboard(self, group_id: str) -> dict:
        """加载群聊排行榜数据"""
        try:
            if group_id in self.leaderboard_data:
                return self.leaderboard_data[group_id]
            return {}
        except Exception as e:
            logger.error(f"加载群聊排行榜数据失败: {e}")
            return {}

    def _save_group_leaderboard(self, group_id: str, leaderboard: dict):
        """保存群聊排行榜数据"""
        try:
            if group_id not in self.leaderboard_data:
                self.leaderboard_data[group_id] = {}
            self.leaderboard_data[group_id] = leaderboard
            self._save_leaderboard()
        except Exception as e:
            logger.error(f"保存群聊排行榜数据失败: {e}")

    async def _update_and_show_leaderboard(self, bot: WechatAPIClient, group_id: str, game_state: dict):
        """更新排行榜数据并显示"""
        try:
            # 加载现有排行榜数据
            leaderboard = self._load_group_leaderboard(group_id)

            # 更新排行榜数据
            for wxid, user_data in game_state.get('participants', {}).items():
                if wxid not in leaderboard:
                    leaderboard[wxid] = {
                        'nickname': user_data['nickname'],
                        'total_correct': 0
                    }
                leaderboard[wxid]['total_correct'] += user_data['correct_count']
                leaderboard[wxid]['nickname'] = user_data['nickname']  # 更新昵称

            # 保存排行榜数据
            self._save_group_leaderboard(group_id, leaderboard)

            # 发送排行榜
            await self._send_leaderboard(bot, group_id, leaderboard)

        except Exception as e:
            logger.error(f"更新和显示排行榜失败: {e}")
            await bot.send_text_message(group_id, "-----音乐猜歌排行榜-----\n❌ 排行榜数据更新失败")

    async def _start_next_round(self, bot: WechatAPIClient, chat_id: str):
        """开始下一轮游戏"""
        if chat_id not in self.active_games:
            return

        game_state = self.active_games[chat_id]

        # 显示当前轮答案
        answer_message = (
            f"-----第{game_state['current_round']}轮答案-----\n"
            f"🎵 歌名：{game_state['song_name']}\n"
            f"🎤 歌手：{game_state['singer']}\n"
            f"⏱️ 时长：{game_state['duration'] // 1000}秒\n\n"
            f"准备下一轮..."
        )
        await bot.send_text_message(chat_id, answer_message)

        # 等待3秒后开始下一轮
        await asyncio.sleep(3)

        # 获取新的歌曲数据
        song_data = await self._fetch_song_data()
        if not song_data:
            await bot.send_text_message(chat_id, "❌ 获取下一轮歌曲数据失败，游戏结束！")
            del self.active_games[chat_id]
            return

        # 更新游戏状态
        game_state['current_round'] += 1
        game_state['song_name'] = song_data['song_name']
        game_state['singer'] = song_data['singer']
        game_state['audio_url'] = song_data['audio_url']
        game_state['duration'] = song_data['duration']
        game_state['lyrics'] = song_data['lyrics']
        game_state['start_time'] = datetime.now()
        game_state['answered_correctly'].clear()  # 清空本轮已答对的用户

        # 发送新一轮开始消息
        start_message = (
            f"-----音乐猜歌-----\n"
            f"🎮 第{game_state['current_round']}/{game_state['total_rounds']}轮开始！\n"
            f"⏰ 答题时间：{self.answer_time_limit}分钟\n"
            f"🎯 答对奖励：{self.reward_points}积分\n"
            f"💡 请根据音乐猜出对应的歌名！"
        )

        await bot.send_text_message(chat_id, start_message)

        # 下载并发送音频
        audio_sent_successfully = False
        try:
            logger.info(f"=== 开始下一轮音频处理流程 ===")
            logger.info(f"群聊ID: {chat_id}")
            logger.info(f"当前轮次: {game_state['current_round']}/{game_state['total_rounds']}")
            logger.info(f"音频URL: {song_data['audio_url']}")

            audio_data = await self._download_audio(song_data['audio_url'])
            if audio_data:
                logger.info(f"音频下载成功，数据大小: {len(audio_data)} 字节")

                # 检查音频数据的有效性
                if len(audio_data) < 1000:  # 小于1KB可能是无效数据
                    logger.warning(f"音频数据过小，可能无效: {len(audio_data)} 字节")

                # 记录音频数据的前几个字节用于调试
                header_bytes = audio_data[:16] if len(audio_data) >= 16 else audio_data
                logger.info(f"音频数据头部: {header_bytes.hex()}")

                # 智能发送语音消息
                logger.info("准备发送语音消息...")
                try:
                    result = await self._send_voice_with_smart_format(bot, chat_id, audio_data)
                    audio_sent_successfully = True
                except Exception as e:
                    logger.error(f"❌ 语音发送失败: {str(e)}")
                    logger.error(f"错误类型: {type(e).__name__}")
                    raise e
            else:
                logger.error("❌ 音频下载失败，audio_data为空")
                await bot.send_text_message(chat_id, f"音频下载失败，请访问：{song_data['audio_url']}")
        except Exception as e:
            logger.error(f"❌ 发送音频失败: {str(e)}")
            logger.error(f"错误类型: {type(e).__name__}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            await bot.send_text_message(chat_id, f"音频发送失败，请访问：{song_data['audio_url']}")

        logger.info(f"=== 下一轮音频处理流程结束，成功: {audio_sent_successfully} ===")

        # 只有在音频发送成功后才设置定时器
        if audio_sent_successfully:
            logger.info(f"音频发送成功，设置新的游戏定时器: {self.answer_time_limit}分钟")
            asyncio.create_task(self._game_timer(bot, chat_id))
        else:
            logger.error("音频发送失败，结束游戏")
            # 结束游戏
            await self._show_answer_and_end_game(bot, chat_id, None, timeout=False)

    async def _show_answer_and_end_game(self, bot: WechatAPIClient, chat_id: str, winner_wxid: Optional[str] = None, timeout: bool = False):
        """显示答案并结束游戏"""
        if chat_id not in self.active_games:
            return

        game_state = self.active_games[chat_id]

        # 构建答案信息
        if game_state['total_rounds'] > 1:
            answer_message = (
                f"-----第{game_state['current_round']}/{game_state['total_rounds']}轮答案-----\n"
                f"🎵 歌名：{game_state['song_name']}\n"
                f"🎤 歌手：{game_state['singer']}\n"
                f"⏱️ 时长：{game_state['duration'] // 1000}秒\n\n"
                f"🎮 游戏结束！共{game_state['total_rounds']}轮"
            )
        else:
            answer_message = (
                f"-----音乐猜歌答案-----\n"
                f"🎵 歌名：{game_state['song_name']}\n"
                f"🎤 歌手：{game_state['singer']}\n"
                f"⏱️ 时长：{game_state['duration'] // 1000}秒"
            )

        if timeout:
            answer_message += "\n⏰ 时间到！没有人答对。"

        await bot.send_text_message(chat_id, answer_message)
        
        # 更新并显示排行榜（仅群聊）
        if game_state['is_group']:
            await self._update_and_show_leaderboard(bot, chat_id, game_state)
        
        # 清理游戏状态
        del self.active_games[chat_id]

    async def _send_leaderboard(self, bot: WechatAPIClient, group_id: str, leaderboard: dict):
        """发送排行榜"""
        try:
            # 按答对次数排序
            sorted_users = sorted(leaderboard.items(),
                                key=lambda x: x[1]['total_correct'],
                                reverse=True) if leaderboard else []

            # 生成排行榜消息
            message = "-----音乐猜歌排行榜-----\n🎵 TOP排名 🎵\n"

            rank_emojis = ["🥇", "🥈", "🥉", "4️⃣", "5️⃣"]

            # 显示实际排名
            for i, (wxid, user_data) in enumerate(sorted_users[:self.leaderboard_top_count]):
                rank = i + 1
                emoji = rank_emojis[i] if i < len(rank_emojis) else f"{rank}."
                nickname = user_data.get('nickname', '未知用户')
                correct_count = user_data.get('total_correct', 0)

                message += f"{emoji} {nickname} - {correct_count}次\n"

            # 如果排名不足TOP数，补充"虚以待位"
            actual_count = len(sorted_users)
            if actual_count < self.leaderboard_top_count:
                for i in range(actual_count, self.leaderboard_top_count):
                    rank = i + 1
                    emoji = rank_emojis[i] if i < len(rank_emojis) else f"{rank}."
                    message += f"{emoji} 虚以待位\n"

            await bot.send_text_message(group_id, message)

        except Exception as e:
            logger.error(f"发送排行榜失败: {e}")
            await bot.send_text_message(group_id, "-----音乐猜歌排行榜-----\n❌ 排行榜显示失败")


