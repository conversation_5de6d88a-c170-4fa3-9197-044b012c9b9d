# 音乐猜歌插件

通过播放音乐片段，让群成员抢答对应的歌名。

## 功能特性

- ✅ **抢答模式**：第一个答对的人立刻获胜并结束游戏
- ✅ 支持自定义触发指令
- ✅ 每次答题有时间限制（默认3分钟）
- ✅ 答题结束后根据答对题目数量排名并发送排行榜
- ✅ 群员答错忽略不响应，答对奖励积分
- ✅ 每个群聊的排名不互通，区分开
- ✅ 私聊中无排行榜显示，群聊中才有排行榜
- ✅ 可自定义游戏轮数、时间限制、奖励积分等
- ✅ 每日游戏次数限制

## 配置说明

### 基本配置
- `enable`: 是否启用插件
- `trigger_commands`: 触发指令列表

### 游戏设置
- `game_rounds`: 游戏轮数（留空或填0即默认只有一轮）
- `answer_time_limit`: 每轮限定的时间（分钟）
- `reward_points`: 每次答对的奖励积分数
- `leaderboard_top_count`: 排行榜TOP数
- `daily_game_limit`: 每个群聊当天游戏次数上限

### API配置
- `api_secret`: 固定的AppSecret

## 使用方法

### 触发游戏
在聊天中发送任一指令：
```
猜歌
猜歌名
音乐猜歌
开始猜歌
```

### 游戏规则
1. 游戏开始后，机器人会发送一段音频
2. 群成员需要在限定时间内回答出对应的歌名
3. 第一个答对的人立即获胜并结束游戏
4. 答对者获得积分奖励
5. 答错不扣分，忽略不响应
6. 游戏结束后显示答案和排行榜（仅群聊）

### 排行榜功能
- 私聊中触发游戏时，没有显示排行榜的环节
- 群聊中触发游戏时才有排行榜环节
- 排行榜每个群聊是互相独立，不互通的
- 排行榜根据wxid来标识每个人的数据，但显示的时候是显示其昵称

## 接口信息

- **接口地址**: https://api.dudunas.top/api/guesssong
- **请求方法**: GET
- **返回格式**: JSON
- **请求参数**: AppSecret（固定值：6c3b0ca62dd045e9dcf15e3dcbe4c03a）

### 返回示例
```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "user": {
      "nickname": "茉 莉 𝑺𝑵𝒁𝒀 • ",
      "gender": "male",
      "avatar_url": "http://img-cdn.api.singduck.cn/user-img/e00191c317b04b278f85db1b5260b796.jpg"
    },
    "song": {
      "name": "勇气大爆发",
      "singer": "土豆王国小乐队",
      "lyrics": [
        "心里种下一颗种子 哒啦滴哒啦",
        "他能实现小小愿望 有神奇魔法"
      ]
    },
    "audio": {
      "url": "http://audio-cdn.api.singduck.cn/ugc/230315_1502712509_04eaa9e218a6.wav",
      "duration": 34474,
      "like_count": 2597,
      "link": "https://m.api.singduck.cn/user-piece/Koj3bFjg9iTHHFVwp",
      "publish": "2023/03/15 15:37:23",
      "publish_at": 1678865843000
    }
  }
}
```

## 数据存储

插件使用以下文件存储数据：
- `leaderboard.json`: 存储各群聊的排行榜数据
- `daily_count.json`: 存储每日游戏次数统计

## 音频处理功能

### 自动压缩
- 当音频文件超过设定大小限制时，使用ffmpeg自动压缩
- 支持配置最大文件大小（默认3MB）
- 支持KB、MB单位，如："500KB"、"3MB"
- 三种质量等级：low、medium、high
- 压缩后转换为MP3格式以获得更好的压缩比

### 智能格式检测
- 自动检测音频格式（WAV/MP3）
- 智能选择最佳发送格式
- 压缩失败时使用原始音频

## 注意事项

1. 音频文件通过网络下载，需要确保网络连接正常
2. 音频即下载即发送，不进行缓存
3. 超出大小限制的音频会使用ffmpeg自动压缩
4. 需要系统安装ffmpeg才能使用音频压缩功能
5. 答案匹配采用精确匹配，需要完全一致
6. 每个群聊每天有游戏次数限制，防止刷屏
7. 私聊和群聊的行为不同，私聊无排行榜功能
