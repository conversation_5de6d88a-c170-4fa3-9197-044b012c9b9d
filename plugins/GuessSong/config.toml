[GuessSong]
# 是否启用插件
enable = true

# 触发指令（留空则无法触发该功能）
trigger_commands = ["猜歌", "猜歌名", "音乐猜歌", "开始猜歌"]

# 游戏设置
game_rounds = 1  # 游戏轮数（留空或填0即默认只有一轮）
answer_time_limit = 2  # 每轮限定的时间（分钟，留空则默认3分钟）
reward_points = 5  # 每次答对的奖励积分数（留空则默认5积分）

# 排行榜设置
leaderboard_top_count = 5  # 排行榜TOP数（留空则默认TOP5）

# 群聊限制
daily_game_limit = 20  # 每个群聊当天游戏次数上限（留空则默认20次）

# API配置
api_secret = "6c3b0ca62dd045e9dcf15e3dcbe4c03a"  # 固定的AppSecret

# 音频处理配置
max_audio_size = "3MB"  # 最大音频文件大小，支持KB、MB单位，如: "500KB", "3MB"
audio_quality = "medium"  # 音频质量: "low", "medium", "high"

# 指令格式提示
command_format = """-----音乐猜歌-----
🎮 游戏指令：
• 猜歌 - 开始游戏
• 猜歌名 - 开始游戏
• 音乐猜歌 - 开始游戏
• 开始猜歌 - 开始游戏

🆘 帮助指令：
• 猜歌帮助 - 显示此帮助信息
• 音乐猜歌帮助 - 显示此帮助信息
• 猜歌说明 - 显示此帮助信息

🔧 调试指令：
• 测试猜歌音频 - 测试音频下载和发送功能
• 测试猜歌游戏 - 测试完整游戏流程
• 测试猜歌昵称 - 测试昵称获取功能
• 测试猜歌语音 - 专门测试语音发送功能
• 测试猜歌语音简单 - 简单语音发送测试（跳过检查）
• 测试猜歌小音频 - 测试发送小音频文件
• 测试猜歌无压缩 - 测试发送原始音频（不压缩）
• 测试猜歌API - 测试API返回的音频数据质量

🎯 游戏规则（抢答模式）：
• 答题时间限制：2分钟
• 第一个答对的人获胜并立刻结束游戏
• 答对奖励：5积分
• 答错不扣分，忽略不响应
• 游戏结束后显示详细答案信息和排行榜

📊 排行榜功能：
• 私聊中无排行榜显示
• 群聊中显示TOP5排行榜
• 每个群聊排行榜独立
• 显示昵称而非wxid

⚙️ 限制设置：
• 每个群聊每天最多20次游戏
• 支持多轮游戏模式

🎵 音频处理：
• 最大音频大小：3MB（可配置）
• 超出大小自动压缩音频
• 支持KB、MB单位设置
• 音频质量：medium（可选：low/medium/high）
• 压缩格式：MP3（高效压缩）"""
