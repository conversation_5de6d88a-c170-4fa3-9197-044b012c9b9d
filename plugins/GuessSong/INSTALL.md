# 音乐猜歌插件安装说明

## 快速安装

### 1. 启用插件
编辑 `plugins/GuessSong/config.toml` 文件：
```toml
[GuessSong]
enable = true
```

### 2. 配置触发指令
在配置文件中设置触发指令：
```toml
trigger_commands = ["猜歌", "猜歌名", "音乐猜歌", "开始猜歌"]
```

### 3. 开始游戏
在聊天中发送任一指令：
```
猜歌
猜歌名
音乐猜歌
开始猜歌
```

## 功能特性 ✅

- ✅ 关键词触发游戏（猜歌、猜歌名、音乐猜歌、开始猜歌）
- ✅ 答题时间限制（默认3分钟，可配置）
- ✅ 积分奖励系统（答对奖励积分）
- ✅ 排行榜功能（显示前5名，按群聊分别统计，不足时显示"虚以待位"）
- ✅ 抢答机制（第一个答对立即结束游戏）
- ✅ 精确答案匹配
- ✅ 音频自动下载和发送
- ✅ 私聊/群聊区分处理
- ✅ 每日游戏次数限制
- ✅ 完整的错误处理

## 配置说明

### 基本配置
```toml
[GuessSong]
# 是否启用插件
enable = true

# 触发指令（留空则无法触发该功能）
trigger_commands = ["猜歌", "猜歌名", "音乐猜歌", "开始猜歌"]
```

### 游戏设置
```toml
# 游戏轮数（留空或填0即默认只有一轮）
game_rounds = 1

# 每轮限定的时间（分钟，留空则默认3分钟）
answer_time_limit = 3

# 每次答对的奖励积分数（留空则默认5积分）
reward_points = 5

# 排行榜TOP数（留空则默认TOP5）
leaderboard_top_count = 5

# 每个群聊当天游戏次数上限（留空则默认20次）
daily_game_limit = 20
```

### API配置
```toml
# 固定的AppSecret
api_secret = "6c3b0ca62dd045e9dcf15e3dcbe4c03a"
```

### 音频处理配置
```toml
# 最大音频大小，支持KB、MB单位
max_audio_size = "2MB"

# 音频质量：low、medium、high
audio_quality = "medium"
```

## 接口信息

- **接口地址**: https://api.dudunas.top/api/guesssong
- **请求方法**: GET
- **返回格式**: JSON
- **请求参数**: AppSecret（固定值：6c3b0ca62dd045e9dcf15e3dcbe4c03a）

### 返回参数说明
- `name`: 歌名（即答案）
- `singer`: 歌手
- `audio.url`: 音频链接（即题目）
- `audio.duration`: 音频时长
- `lyrics`: 歌词数组

## 游戏规则

### 抢答模式
1. 游戏开始后发送音频片段
2. 第一个答对的人立刻获胜并结束游戏
3. 答对奖励积分，答错不扣分
4. 游戏结束后显示详细答案信息

### 排行榜功能
- **私聊**: 无排行榜显示
- **群聊**: 显示TOP5排行榜
- **数据隔离**: 每个群聊排行榜独立
- **昵称显示**: 显示昵称而非wxid，支持昵称更新

### 限制设置
- **时间限制**: 默认3分钟答题时间
- **次数限制**: 每个群聊每天最多20次游戏
- **答案匹配**: 精确匹配，必须完全一致

## 数据文件

插件会在插件目录下创建以下数据文件：
- `leaderboard.json`: 存储各群聊的排行榜数据
- `daily_count.json`: 存储每日游戏次数统计

## 依赖要求

- Python 3.7+
- aiohttp
- pydub (音频处理和压缩)
- 现有的积分系统 (XYBotDB)
- 微信API客户端

## 故障排除

### 常见问题

1. **音频下载失败**
   - 检查网络连接
   - 确认API接口可访问

2. **音频下载失败**
   - 检查网络连接
   - 确认音频URL是否有效

3. **积分不增加**
   - 检查数据库连接
   - 确认XYBotDB正常工作

4. **排行榜不显示**
   - 确认是在群聊中触发
   - 检查leaderboard.json文件权限

5. **游戏次数限制**
   - 检查daily_count.json文件
   - 可手动清理过期数据

### 日志调试
插件使用loguru进行日志记录，可通过日志查看详细的运行信息和错误信息。

### 调试指令
发送 `测试猜歌音频` 可以测试音频下载和发送功能，帮助诊断问题：
- 测试API接口连接
- 测试音频下载
- 测试语音消息发送
- 显示详细的调试信息

## 更新日志

### v1.0.0 (2024-12-XX)
- ✅ 初始版本发布
- ✅ 实现基本的猜歌游戏功能
- ✅ 支持抢答模式
- ✅ 集成积分奖励系统
- ✅ 实现群聊排行榜功能
- ✅ 支持每日游戏次数限制
- ✅ 音频即下载即发送，无缓存机制
