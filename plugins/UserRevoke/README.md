# UserRevoke 插件

## 简介

UserRevoke（用户撤回）是一个允许群员和好友通过引用机器人消息来撤回该消息的插件。插件支持细粒度的权限控制，管理员可以为特定用户或群聊授予撤回权限。

## 功能特性

- 🔄 **消息撤回**：通过引用机器人消息并发送撤回指令来撤回消息
- 🛡️ **权限控制**：只有管理员和拥有权限的用户/群聊才能使用撤回功能
- 👥 **用户权限**：管理员可以为特定用户添加或移除撤回权限
- 🏠 **群聊权限**：管理员可以为整个群聊添加或移除撤回权限
- 🔒 **安全限制**：只能撤回机器人自己发送的消息

## 使用方法

### 基本撤回功能

1. **引用机器人的消息**
2. **发送撤回指令**：
   ```
   撤回
   撤回消息
   消息撤回
   ```

### 权限管理（仅管理员）

#### 用户权限管理

**添加用户撤回权限：**
```
增加撤回权限 @用户
添加撤回权限 @用户
新增撤回权限 @用户
```

**移除用户撤回权限：**
```
移除撤回权限 @用户
删除撤回权限 @用户
```

#### 群聊权限管理

**为群聊添加撤回权限：**
```
增加撤回权限
添加撤回权限
新增撤回权限
```

**移除群聊撤回权限：**
```
移除撤回权限
删除撤回权限
```

## 权限说明

### 权限层级

1. **管理员**：拥有所有权限，包括撤回消息和管理权限
2. **白名单用户**：拥有撤回权限的特定用户
3. **白名单群聊**：整个群聊的所有成员都拥有撤回权限
4. **普通用户**：无撤回权限

### 权限检查顺序

1. 检查是否为管理员
2. 检查用户个人权限
3. 检查群聊权限（仅群聊中有效）

## 使用示例

### 撤回消息示例

1. 机器人发送了一条消息
2. 用户引用该消息并回复：`撤回`
3. 如果用户有权限，机器人会撤回被引用的消息

### 权限管理示例

**管理员为用户添加权限：**
```
管理员：增加撤回权限 @张三
机器人：✅ 已添加 1 个用户的撤回权限
```

**管理员为群聊添加权限：**
```
管理员：增加撤回权限
机器人：✅ 已为本群添加撤回权限
```

## 配置说明

配置文件 `config.toml` 包含以下设置：

```toml
[UserRevoke]
# 插件开关（全局）
enable = true

# 撤回指令
revoke_commands = ["撤回", "撤回消息", "消息撤回"]

# 权限管理指令
[UserRevoke.permission_commands]
add = ["增加撤回权限", "添加撤回权限", "新增撤回权限"]
remove = ["移除撤回权限", "删除撤回权限"]

# 默认配置
[UserRevoke.defaults]
default_permission = false
```

## 工作流程

1. **引用消息检测** → 检查是否引用了机器人的消息
2. **指令识别** → 验证是否为撤回指令
3. **权限验证** → 检查用户是否有撤回权限
4. **数据库查询** → 通过NewMsgId在聊天记录数据库中查询消息
5. **消息验证** → 确认被引用的消息确实来自机器人
6. **参数提取** → 使用数据库中的MsgId作为client_msg_id
7. **执行撤回** → 调用API撤回消息
8. **结果反馈** → 向用户反馈操作结果

## 技术原理

### 撤回参数获取

插件使用以下步骤获取正确的撤回参数：

1. **从引用消息获取NewMsgId**：
   ```
   new_msg_id = quote_info.get("NewMsgId")
   ```

2. **查询聊天记录数据库**：
   ```python
   message_record = await self._get_message_by_new_msg_id(new_msg_id, bot_wxid)
   ```

3. **验证消息来源**：
   ```python
   if message_record["sender_wxid"] != bot_wxid:
       # 拒绝撤回非机器人消息
   ```

4. **使用数据库MsgId作为client_msg_id**：
   ```python
   client_msg_id = message_record["msg_id"]  # 这是关键！
   create_time = quote_info.get("Createtime")
   new_msg_id = quote_info.get("NewMsgId")
   ```

### 为什么需要查询数据库

- **引用消息中的参数不准确**：引用消息中可能缺少正确的ClientMsgId
- **数据库中的MsgId是准确的**：聊天记录数据库中存储的MsgId是撤回API需要的正确参数
- **安全验证**：通过数据库查询可以确保只撤回机器人自己发送的消息

## 注意事项

- 只能撤回机器人自己发送的消息
- 撤回功能需要相应的权限
- 管理员列表从主配置文件 `main_config.toml` 中读取
- 权限信息存储在KeyvalDB中，重启后仍然有效
- 私聊和群聊都支持撤回功能

## 错误处理

插件包含完善的错误处理机制：

- **权限不足**：`❌ 您没有撤回消息的权限`
- **未找到引用**：`❌ 未找到引用的消息`
- **非机器人消息**：`❌ 只能撤回机器人自己发送的消息`
- **消息信息不完整**：`❌ 消息信息不完整，无法撤回`
- **撤回失败**：`❌ 消息撤回失败`

## 安装方法

1. 将插件文件夹复制到 `plugins/UserRevoke/` 目录
2. 根据需要修改 `config.toml` 配置文件
3. 重启机器人或重新加载插件
4. 管理员可以开始为用户或群聊分配撤回权限

## 版本信息

- **版本**：1.0.0
- **作者**：AI Assistant
- **依赖**：KeyvalDB

## 免责声明

本插件仅供学习和交流使用，请合理使用撤回功能，避免滥用。使用者需对使用本插件产生的后果承担责任。
