import tomllib
import json
import os
from loguru import logger

from WechatAPI import WechatAPIClient
from utils.decorators import *
from utils.plugin_base import PluginBase
from database.keyvalDB import KeyvalDB
from database.messsagDB import MessageDB
from admin.server import get_bot_instance


class UserRevoke(PluginBase):
    description = "用户撤回插件"
    author = "AI Assistant"
    version = "1.0.0"

    def __init__(self):
        super().__init__()
        
        # 加载配置文件
        config_path = os.path.join(os.path.dirname(__file__), "config.toml")
        try:
            with open(config_path, "rb") as f:
                plugin_config = tomllib.load(f)
            
            config = plugin_config["UserRevoke"]
            
            self.enable = config["enable"]
            self.revoke_commands = config["revoke_commands"]
            self.permission_commands = config["permission_commands"]
            
            # 默认配置
            defaults = config["defaults"]
            self.default_permission = defaults["default_permission"]
            
            logger.success("UserRevoke插件配置加载成功")
            
        except Exception as e:
            logger.error(f"加载UserRevoke配置文件失败: {str(e)}")
            self.enable = False

    async def async_init(self):
        """异步初始化"""
        # 初始化KeyvalDB
        self.keyval_db = KeyvalDB()
        await self.keyval_db.initialize()

        # 初始化MessageDB
        self.msg_db = MessageDB()
        await self.msg_db.initialize()

        logger.info("UserRevoke插件异步初始化完成")

    def _get_permission_key(self, wxid: str, permission_type: str) -> str:
        """获取权限配置的键名"""
        return f"userrevoke:{wxid}:{permission_type}"

    async def _get_user_permission(self, wxid: str) -> bool:
        """获取用户撤回权限"""
        key = self._get_permission_key(wxid, "permission")
        result = await self.keyval_db.get(key)
        # 处理字符串形式的存储
        if result is None:
            return self.default_permission
        elif isinstance(result, bool):
            return result
        elif isinstance(result, str):
            return result.lower() in ('true', '1', 'yes', 'on')
        else:
            return bool(result)

    async def _set_user_permission(self, wxid: str, enabled: bool):
        """设置用户撤回权限"""
        key = self._get_permission_key(wxid, "permission")
        # 将布尔值转换为字符串存储
        await self.keyval_db.set(key, str(enabled))

    async def _get_group_permission(self, group_wxid: str) -> bool:
        """获取群聊撤回权限"""
        key = self._get_permission_key(group_wxid, "group_permission")
        result = await self.keyval_db.get(key)
        # 处理字符串形式的存储
        if result is None:
            return self.default_permission
        elif isinstance(result, bool):
            return result
        elif isinstance(result, str):
            return result.lower() in ('true', '1', 'yes', 'on')
        else:
            return bool(result)

    async def _set_group_permission(self, group_wxid: str, enabled: bool):
        """设置群聊撤回权限"""
        key = self._get_permission_key(group_wxid, "group_permission")
        # 将布尔值转换为字符串存储
        await self.keyval_db.set(key, str(enabled))

    def _extract_at_users(self, message: dict) -> list:
        """提取消息中@的用户列表"""
        at_users = []
        
        # 从消息对象中提取被@用户
        if "AtUserList" in message and isinstance(message["AtUserList"], list):
            at_users = message["AtUserList"]
            logger.debug(f"从AtUserList获取到的@用户: {at_users}")
        elif "Ats" in message and isinstance(message["Ats"], list):
            at_users = message["Ats"]
            logger.debug(f"从Ats获取到的@用户: {at_users}")
        
        return at_users

    async def _check_permission(self, sender_wxid: str, from_wxid: str, admins: list) -> bool:
        """检查用户是否有撤回权限"""
        # 管理员总是有权限
        if sender_wxid in admins:
            return True

        # 检查用户个人权限
        user_permission = await self._get_user_permission(sender_wxid)
        if user_permission:
            return True

        # 如果是群聊，检查群聊权限
        if from_wxid.endswith("@chatroom"):
            group_permission = await self._get_group_permission(from_wxid)
            if group_permission:
                return True

        return False

    async def _send_message(self, bot: WechatAPIClient, wxid: str, content: str):
        """发送消息的辅助方法"""
        await bot.send_text_message(wxid, content)

    async def _get_message_by_new_msg_id(self, new_msg_id: str, bot_wxid: str) -> dict:
        """通过NewMsgId从聊天记录数据库查询消息"""
        try:
            logger.debug(f"查询参数详情: new_msg_id={new_msg_id}, bot_wxid={bot_wxid}")
            
            # 先查询最近的几条消息，看看数据库中有什么
            recent_messages = await self.msg_db.get_messages(limit=5)
            logger.debug(f"数据库中最近5条消息:")
            for i, msg in enumerate(recent_messages):
                logger.debug(f"  {i+1}. MsgId={msg.msg_id}, NewMsgId={msg.new_msg_id}, Sender={msg.sender_wxid}")
            
            # 使用新的数据库方法直接通过NewMsgId查询
            message = await self.msg_db.get_message_by_new_msg_id(new_msg_id, bot_wxid)

            if message:
                logger.info(f"找到匹配的消息记录: MsgId={message.msg_id}, NewMsgId={message.new_msg_id}, SenderWxid={message.sender_wxid}")
                return {
                    "msg_id": message.msg_id,
                    "new_msg_id": message.new_msg_id,
                    "sender_wxid": message.sender_wxid,
                    "from_wxid": message.from_wxid,
                    "content": message.content,
                    "timestamp": message.timestamp
                }
            else:
                logger.warning(f"未找到NewMsgId={new_msg_id}对应的机器人消息记录")
                return None

        except Exception as e:
            logger.error(f"查询消息记录失败: {str(e)}")
            return None

    @on_quote_message(priority=70)
    async def handle_quote_message(self, bot: WechatAPIClient, message: dict):
        """处理引用消息"""
        try:
            if not self.enable:
                return True
            
            content = str(message.get("Content", "")).strip()
            from_wxid = message.get("FromWxid", "")
            sender_wxid = message.get("SenderWxid", "")
            
            # 检查是否为撤回指令（使用精确匹配）
            is_revoke_command = any(content == cmd or content.strip() == cmd for cmd in self.revoke_commands)
            if not is_revoke_command:
                return True
            
            logger.info(f"UserRevoke收到撤回指令: 群聊={from_wxid}, 发送者={sender_wxid}, 内容='{content}'")
            
            # 获取管理员列表
            with open("main_config.toml", "rb") as f:
                main_config = tomllib.load(f)
            admins = main_config["XYBot"]["admins"]
            
            # 检查权限
            has_permission = await self._check_permission(sender_wxid, from_wxid, admins)
            if not has_permission:
                await self._send_message(bot, from_wxid, "❌ 您没有撤回消息的权限")
                return False
            
            # 获取引用消息信息
            quote_info = message.get("Quote", {})
            if not quote_info:
                await self._send_message(bot, from_wxid, "❌ 未找到引用的消息")
                return False
            
            # 获取机器人wxid
            bot_wxid = bot.wxid

            # 通过NewMsgId查询聊天记录数据库，验证消息是否来自机器人
            new_msg_id = quote_info.get("NewMsgId")
            if not new_msg_id:
                await self._send_message(bot, from_wxid, "❌ 引用消息缺少NewMsgId")
                return False

            # 从数据库查询消息记录
            message_record = await self._get_message_by_new_msg_id(new_msg_id, bot_wxid)
            if not message_record:
                await self._send_message(bot, from_wxid, "❌ 未找到对应的消息记录或该消息不是机器人发送的")
                return False

            # 验证消息确实来自机器人
            if message_record["sender_wxid"] != bot_wxid:
                await self._send_message(bot, from_wxid, "❌ 只能撤回机器人自己发送的消息")
                return False
            
            # 获取撤回所需的参数
            # 根据实际消息结构，NewMsgId应该用作client_msg_id
            new_msg_id = quote_info.get("NewMsgId")
            create_time = quote_info.get("Createtime")

            # 对于撤回API，所有三个参数都使用NewMsgId
            client_msg_id = message_record.get("msg_id")

            logger.info(f"撤回参数: ClientMsgId={client_msg_id}(NewMsgId), CreateTime={create_time}, NewMsgId={new_msg_id}")
            logger.debug(f"数据库消息记录: {message_record}")
            logger.debug(f"引用消息完整信息: {quote_info}")

            if not all([client_msg_id, create_time, new_msg_id]):
                missing_fields = []
                if not client_msg_id: missing_fields.append("ClientMsgId(NewMsgId)")
                if not create_time: missing_fields.append("CreateTime")
                if not new_msg_id: missing_fields.append("NewMsgId")

                await self._send_message(bot, from_wxid, f"❌ 消息信息不完整，缺少字段: {', '.join(missing_fields)}")
                return False

            # 尝试撤回消息
            try:
                # 转换参数类型
                create_time = int(create_time) if isinstance(create_time, str) else create_time
                new_msg_id_int = int(new_msg_id) if isinstance(new_msg_id, str) else new_msg_id
                client_msg_id = int(client_msg_id) if isinstance(client_msg_id, str) else client_msg_id

                logger.info(f"尝试撤回消息: wxid={from_wxid}, client_msg_id={client_msg_id}, create_time={create_time}, new_msg_id={new_msg_id_int}")

                success = await bot.revoke_message(from_wxid, client_msg_id, create_time, new_msg_id_int)
                
                if success:
                    logger.info(f"消息撤回成功: 群聊={from_wxid}, 操作者={sender_wxid}")
                    await self._send_message(bot, from_wxid, "✅ 消息撤回成功")
                else:
                    await self._send_message(bot, from_wxid, "❌ 消息撤回失败")

            except Exception as e:
                logger.error(f"撤回消息时出错: {str(e)}")
                await self._send_message(bot, from_wxid, f"❌ 撤回消息时出错: {str(e)}")
            
            return False
            
        except Exception as e:
            logger.error(f"UserRevoke插件处理引用消息时出错: {str(e)}")
            return True

    @on_text_message(priority=70)
    async def handle_text_message(self, bot: WechatAPIClient, message: dict):
        """处理权限管理指令"""
        try:
            if not self.enable:
                return True
            
            content = str(message.get("Content", "")).strip()
            from_wxid = message.get("FromWxid", "")
            sender_wxid = message.get("SenderWxid", "")
            
            # 获取管理员列表
            with open("main_config.toml", "rb") as f:
                main_config = tomllib.load(f)
            admins = main_config["XYBot"]["admins"]
            
            # 处理权限管理指令
            if await self._handle_permission_commands(bot, message, content, from_wxid, sender_wxid, admins):
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"UserRevoke插件处理文本消息时出错: {str(e)}")
            return True

    async def _handle_permission_commands(self, bot: WechatAPIClient, message: dict, content: str,
                                        from_wxid: str, sender_wxid: str, admins: list) -> bool:
        """处理权限管理指令"""
        # 检查是否为权限管理指令（使用精确匹配或开头匹配）
        is_add_command = any(content.startswith(cmd) or content == cmd for cmd in self.permission_commands["add"])
        is_remove_command = any(content.startswith(cmd) or content == cmd for cmd in self.permission_commands["remove"])

        if not (is_add_command or is_remove_command):
            return False
        
        # 只有管理员可以管理权限
        if sender_wxid not in admins:
            await self._send_message(bot, from_wxid, "❌ 只有管理员才能管理撤回权限")
            return True
        
        # 提取@的用户
        at_users = self._extract_at_users(message)
        
        if at_users:
            # 对@的用户进行权限操作
            for user_wxid in at_users:
                if is_add_command:
                    await self._set_user_permission(user_wxid, True)
                else:
                    await self._set_user_permission(user_wxid, False)
            
            action = "添加" if is_add_command else "移除"
            await self._send_message(bot, from_wxid, f"✅ 已{action} {len(at_users)} 个用户的撤回权限")
        else:
            # 没有@用户，则对当前群聊进行权限操作
            if from_wxid.endswith("@chatroom"):
                if is_add_command:
                    await self._set_group_permission(from_wxid, True)
                    await self._send_message(bot, from_wxid, "✅ 已为本群添加撤回权限")
                else:
                    await self._set_group_permission(from_wxid, False)
                    await self._send_message(bot, from_wxid, "✅ 已移除本群的撤回权限")
            else:
                await self._send_message(bot, from_wxid, "❌ 请@要操作的用户，或在群聊中直接使用指令操作群权限")
        
        return True
