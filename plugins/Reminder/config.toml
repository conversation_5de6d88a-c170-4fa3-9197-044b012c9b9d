[Reminder]
enable = true

commands = ["记录", "我的记录", "删除"]
# 这里配置其他插件的唤醒词，备忘录会自动识别并触发
other-plugin_cmd = [
    "天气",
    "翻译", 
    "搜索",
    "点歌",
    "抽签",
    "AI",
    "帮我",
    "问问",
    "新闻",
    "摸鱼",
    "日历",
    "计算",
    "提醒",
    "闹钟"
]
command-tip = """-----老夏的金库-----
备忘录指令：
记录 <时间> <内容>：用于存储备忘录信息。
我的记录：用于查询备忘录信息。
删除 <序号>：用于删除指定序号的备忘录。
删除 全部：用于删除所有备忘录。

支持的时间格式：
1. 每天 HH:MM（如：每天 08:00）
2. 每周一/二/三/四/五/六/日 HH:MM
3. 每月DD HH:MM
4. XX分钟后
5. XX小时后
6. XX天后

插件联动示例：
$备忘录 每天 08:00 天气 北京
$备忘录 每天 12:00 新闻
$备忘录 每周一 09:00 日历
$备忘录 每天 18:00 AI 帮我总结今天的工作

支持触发以下插件命令：
天气、翻译、搜索、点歌、抽签、AI、帮我、问问、新闻、摸鱼、日历、计算、提醒、闹钟
"""

price = 1 #操作一次扣积分，如果0则不扣
admin_ignore = true
whitelist_ignore = true
http-proxy = ""