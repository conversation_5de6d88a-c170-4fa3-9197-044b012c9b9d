# 群抢劫小游戏插件 (GroupRobbery)

这是一个为 xxxbot-pad 开发的群抢劫小游戏插件，随机抢劫群友的积分，具有100多种不同风格的随机事件，带来丰富的游戏体验。

## 功能特点

- **随机抢劫**: 随机选择一位群友进行抢劫
- **指定打劫**: 支持@群友进行指定目标的打劫
- **昵称显示**: 优先使用群昵称进行显示，增强交互感
- **100+随机事件**: 包含普通、搞笑、科技、电影、奇幻、历史、动物、职业和超级英雄等多种风格的事件描述
- **冷却时间**: 每次抢劫后有冷却时间，防止过度使用
- **特权用户**: 管理员和白名单用户无视冷却时间限制
- **机器人反击**: 当玩家打劫机器人时，有很高概率会触发机器人的反击事件
- **随机积分变化**: 抢劫成功或失败时的积分变化随机，增加游戏的不确定性

## 使用方法

在群聊中发送以下命令:

- `抢劫` - 随机抢劫一位群友
- `抢劫 @群友` - 指定抢劫某位群友
- `打劫` - 随机抢劫一位群友
- `打劫 @群友` - 指定抢劫某位群友
- `抢积分` - 同样可以触发随机抢劫功能
- `抢积分 @群友` - 指定抢劫某位群友

也可以直接@机器人并说"打劫"，例如：`@机器人 打劫`，但要小心机器人的反击！

## 抢劫结果

抢劫有以下几种可能的结果:

1. **抢劫成功**: 从目标用户那里获得随机数量的积分
2. **抢劫失败**: 抢劫失败，损失随机数量的积分
3. **机器人反击**: 当你尝试打劫机器人时，有90%的概率会触发机器人的反击，损失更多积分

## 配置说明

在 `config.toml` 文件中可以调整以下参数:

```toml
[GroupRobbery]
# 是否启用插件
enable = true

# 触发命令
command = ["抢劫", "抢积分", "打劫"]

# 冷却时间(秒)
cooldown = 30

# 机器人反击基础扣分 - 当玩家打劫机器人时触发
robot_counterattack_base_points = 10

# 机器人反击随机扣分范围
robot_counterattack_random_range = 20

# 抢劫成功率 (0-100)
success_probability = 80

# 抢劫基础积分
base_points = 10

# 抢劫随机积分范围
random_points_range = 5
```

## 特权用户

以下用户可以无视冷却时间限制：

- **管理员**: 在主配置文件(`main_config.toml`)的`XYBot.admins`列表中的用户
- **白名单用户**: 通过`设置白名单`命令添加的用户

## 示例

### 随机抢劫成功
```
📢 你随机打劫到了 👤 群友昵称
━━━━━━━━━━
🎯 恭喜你！打劫成功 🎉
💎 你像蜘蛛侠一样飞荡在楼宇间，顺手牵走了 35 积分！

💰 你现在拥有: 135 积分 ✨
```

### 指定抢劫失败
```
📢 你正在打劫 👤 群友昵称
━━━━━━━━━━
💥 很抱歉！打劫失败 😫
🔍 你试图像超人一样飞行，从楼上跳下，结果摔断了腿，医疗费高达 28 积分！

💰 你现在拥有: 72 积分 ✨
```

### 机器人反击
```
📢 你正在打劫 👤 机器人
━━━━━━━━━━
💥 警告！机器人反击 ⚠️
🔍 机器人使用引力控制器，将你的 45 积分吸到了自己这边！

💰 你现在拥有: 55 积分 ✨
```

## 注意事项

- 抢劫功能仅在群聊中可用
- 不能抢劫自己
- 普通用户每次抢劫后有冷却时间，避免过度使用
- 管理员和白名单用户可以无视冷却时间限制
- 打劫机器人会有很高概率触发反击事件，请谨慎尝试

## 数据存储

本插件使用以下数据库表存储必要信息：

- **用户积分**: 存储在XYBotDB的user表中，通过XYBotDB类接口进行管理
- **用户昵称**: 从contacts.db的group_members表中获取，优先使用display_name，如果为空则使用nickname
- **打劫时间记录**: 存储在pluginsDB.db的robbery_time表中，记录用户最近一次打劫的时间，用于冷却时间检查

robbery_time表结构：
```
id             - 自增主键
wxid           - 用户wxid（唯一索引）
robbery_time   - 打劫时间（ISO格式字符串）
```

插件启动时会自动检查并创建必要的数据库表。

## 版本信息

- **版本**: 1.0.0
- **开发者**: wspzf 