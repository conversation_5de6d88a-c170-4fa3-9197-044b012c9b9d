import datetime
import random
import sqlite3
import tomllib
import os
from typing import Dict, Tuple, List, Optional

from loguru import logger
from WechatAPI import WechatAPIClient
from database.XYBotDB import XYBotDB
from utils.decorators import *
from utils.plugin_base import PluginBase

from .events import SUCCESS_EVENTS, FAIL_EVENTS, ROBOT_COUNTERATTACK_EVENTS


class GroupRobbery(PluginBase):
    """群抢劫插件 - 随机抢劫群友积分"""
    description = "群抢劫小游戏 - 随机抢劫群友积分"
    author = "wspzf"
    version = "1.0.0"

    def __init__(self):
        super().__init__()

        # 读取配置文件
        with open("plugins/GroupRobbery/config.toml", "rb") as f:
            plugin_config = tomllib.load(f)

        # 读取主配置文件获取管理员列表
        with open("main_config.toml", "rb") as f:
            main_config = tomllib.load(f)

        config = plugin_config["GroupRobbery"]
        main_config = main_config.get("XYBot", {})

        # 基础配置
        self.enable = config["enable"]  # 是否启用
        self.command = config["command"]  # 触发命令
        self.cooldown = config["cooldown"]  # 冷却时间(秒)

        # 机器人反抢劫配置 - 虽然不再随机触发，但保留用于打劫机器人时
        self.robot_counterattack_base_points = config["robot_counterattack_base_points"]  # 机器人基础扣分
        self.robot_counterattack_random_range = config["robot_counterattack_random_range"]  # 机器人随机扣分范围

        # 抢劫成功率和积分配置
        self.success_probability = config["success_probability"]  # 抢劫成功概率
        self.base_points = config["base_points"]  # 基础抢劫积分
        self.random_points_range = config["random_points_range"]  # 随机积分范围

        # 管理员列表
        self.admins = main_config.get("admins", [])
        logger.info(f"管理员列表: {self.admins}")

        # 数据库
        self.db = XYBotDB()
        self.contacts_db = None

        # pluginsDB数据库路径
        self.plugins_db_path = os.path.join("database", "pluginsDB.db")

    async def async_init(self):
        """插件异步初始化"""
        # 创建打劫时间记录表
        self.init_robbery_time_table()
        return

    def init_robbery_time_table(self):
        """初始化打劫时间记录表"""
        try:
            # 连接数据库
            conn = sqlite3.connect(self.plugins_db_path)
            cursor = conn.cursor()

            # 创建robbery_time表，记录用户打劫时间
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS robbery_time (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                wxid TEXT NOT NULL,                 -- 用户wxid
                robbery_time TIMESTAMP NOT NULL,    -- 打劫时间
                UNIQUE(wxid)                        -- wxid唯一索引
            )
            ''')

            conn.commit()
            conn.close()

            logger.success(f"GroupRobbery打劫时间记录表初始化成功")

        except Exception as e:
            logger.error(f"GroupRobbery打劫时间记录表初始化失败: {str(e)}")

    def get_contacts_db(self):
        """连接到contacts.db数据库"""
        if self.contacts_db is None:
            self.contacts_db = sqlite3.connect("database/contacts.db")
        return self.contacts_db

    def get_group_members(self, group_wxid):
        """从contacts.db中获取群成员信息"""
        try:
            conn = self.get_contacts_db()
            cursor = conn.cursor()
            cursor.execute(
                "SELECT member_wxid, nickname, display_name FROM group_members WHERE group_wxid = ?",
                (group_wxid,)
            )
            members = cursor.fetchall()
            logger.debug(f"从contacts.db获取到群 {group_wxid} 的成员: {len(members)} 人")
            return members
        except Exception as e:
            logger.error(f"从contacts.db获取群成员失败: {e}")
            return []

    @on_at_message(priority=50000)  # 设置超高优先级，确保在FastGPT之前处理@消息
    async def handle_at_message(self, bot: WechatAPIClient, message: dict):
        """处理@消息，优先拦截打劫相关命令"""
        logger.debug(f"GroupRobbery收到@消息: 内容={message.get('Content')}")

        if not self.enable:
            logger.debug("GroupRobbery插件未启用")
            return True  # 如果插件未启用，允许其他插件处理

        content = str(message["Content"]).strip()

        # 检查是否包含打劫命令
        is_robbery_cmd = False
        for cmd in self.command:
            # 检查各种可能的打劫命令格式
            if cmd in content:
                is_robbery_cmd = True
                logger.info(f"在@消息中匹配到打劫命令: {content}")
                break

        if not is_robbery_cmd:
            logger.debug(f"@消息不包含打劫命令: {content}")
            return True  # 如果不包含打劫命令，允许其他插件处理

        # 获取发送者信息和群聊信息
        sender_wxid = message["SenderWxid"]
        chatroom_wxid = message["FromWxid"]

        logger.info(f"拦截到@打劫命令: {content}, 发送者: {sender_wxid}, 群聊: {chatroom_wxid}")

        # 调用handle_text方法处理，因为该方法包含了完整的打劫逻辑
        # 注意：这里直接调用，不需要await，因为在这之后我们会返回False，阻止其他插件处理
        return await self.handle_text(bot, message)

    @on_text_message(priority=10000)  # 设置为超高优先级，确保最先处理
    async def handle_text(self, bot: WechatAPIClient, message: dict):
        """处理文本消息"""
        # 输出详细的消息类型和内容，帮助调试
        logger.debug(f"GroupRobbery收到消息: 类型={message.get('Type')}, 内容={message.get('Content')}")

        if not self.enable:
            logger.debug("GroupRobbery插件未启用")
            return True  # 如果插件未启用，允许其他插件处理

        content = str(message["Content"]).strip()

        # 改进命令匹配逻辑，只匹配严格的打劫命令格式
        is_robbery_cmd = False
        used_cmd = None
        target_in_content = None  # 从内容中提取的目标

        # 严格匹配模式：必须是单独的"打劫"命令，或者"打劫@"开头的消息
        for cmd in self.command:
            # 检查是否是独立的"打劫"命令
            if content == cmd:
                is_robbery_cmd = True
                used_cmd = cmd
                logger.debug(f"匹配到独立打劫命令: {cmd}")
                break
            # 检查是否以"打劫@"开头 - 提取被@的用户
            elif content.startswith(cmd + "@") or content.startswith(cmd + " @"):
                is_robbery_cmd = True
                used_cmd = cmd
                # 尝试从内容中提取被@的用户名
                try:
                    target_in_content = content.split('@', 1)[1].strip()
                    logger.debug(f"从内容提取目标: {target_in_content}")
                except:
                    logger.debug("无法从内容提取目标用户")
                logger.debug(f"匹配到打劫@命令: {cmd}, 完整内容: {content}, 目标: {target_in_content}")
                break
            # 检查是否以"@用户打劫"结尾
            elif content.endswith(cmd) and "@" in content:
                is_robbery_cmd = True
                used_cmd = cmd
                logger.debug(f"匹配到@用户打劫命令: {cmd}, 完整内容: {content}")
                break

        if not is_robbery_cmd:
            logger.debug(f"不是打劫命令: {content}")
            return True  # 如果不是打劫命令，允许其他插件处理

        # 命令匹配成功，输出详细日志
        logger.info(f"打劫命令匹配成功: {content}, 使用命令: {used_cmd}")

        # 获取发送者信息
        sender_wxid = message["SenderWxid"]
        chatroom_wxid = message["FromWxid"]

        # 增加日志记录
        logger.info(f"打劫者: {sender_wxid}, 群聊: {chatroom_wxid}")

        # 判断是否为群聊
        if not chatroom_wxid.endswith("@chatroom"):
            await bot.send_text_message(chatroom_wxid, "❌打劫功能仅在群聊中可用！")
            logger.info("尝试在非群聊中使用打劫功能")
            return False  # 不允许其他插件处理

        # 检查冷却时间 - 管理员和白名单用户无视冷却
        is_admin = sender_wxid in self.admins
        is_whitelisted = self.db.get_whitelist(sender_wxid)

        if not is_admin and not is_whitelisted:
            last_robbery_time = self.get_last_robbery_time(sender_wxid)
            current_time = datetime.datetime.now()

            if last_robbery_time:
                time_diff = (current_time - last_robbery_time).total_seconds()
                if time_diff < self.cooldown:
                    remaining_time = int(self.cooldown - time_diff)
                    minutes = remaining_time // 60
                    seconds = remaining_time % 60

                    try:
                        await bot.send_at_message(
                            chatroom_wxid,
                            f"⏱️ 冷却中... 你刚刚进行过打劫\n🕒 剩余冷却时间: {minutes}分{seconds}秒",
                            [sender_wxid]
                        )
                    except Exception as e:
                        logger.error(f"发送冷却消息失败: {e}")
                        sender_nickname = await self.get_display_name(sender_wxid, chatroom_wxid)
                        await bot.send_text_message(
                            chatroom_wxid,
                            f"@{sender_nickname} ⏱️ 冷却中... 你刚刚进行过打劫\n🕒 剩余冷却时间: {minutes}分{seconds}秒"
                        )
                    logger.info(f"用户{sender_wxid}在冷却中，剩余时间: {minutes}分{seconds}秒")
                    return False  # 不允许其他插件处理
        else:
            logger.info(f"用户 {sender_wxid} 是{'管理员' if is_admin else '白名单用户'}，无视冷却时间")

        # 获取群成员 - 从contacts.db中获取
        logger.info("开始从contacts.db获取群成员信息")
        group_members = self.get_group_members(chatroom_wxid)

        if not group_members:
            logger.error("从contacts.db获取群成员返回空列表")
            await bot.send_text_message(chatroom_wxid, "❌获取群成员失败！")
            return False  # 不允许其他插件处理

        # 添加调试信息，输出部分群成员
        logger.debug(f"群成员前5个: {group_members[:5] if len(group_members) > 5 else group_members}")

        # 判断是否是指定打劫(@群友)
        target_wxid = None
        target_is_bot = False  # 标记，表示目标是否为机器人自己
        is_random_robbery = True  # 默认为随机打劫

        # 1. 首先，检查@列表
        if message.get("Ats") and len(message["Ats"]) > 0:
            at_wxid = message["Ats"][0]
            logger.debug(f"检测到@消息，目标wxid: {at_wxid}")

            # 检查是否@了机器人自己
            if at_wxid == bot.wxid:
                target_wxid = at_wxid
                target_is_bot = True
                is_random_robbery = False  # 指定打劫
                logger.info(f"检测到用户@机器人，wxid: {at_wxid}")
            else:
                # @了其他用户
                target_wxid = at_wxid
                is_random_robbery = False  # 指定打劫
                logger.info(f"检测到用户@其他人，wxid: {at_wxid}")

        # 2. 如果没有从@列表找到目标但有从内容提取的目标，尝试查找对应用户
        elif target_in_content:
            logger.info(f"尝试从内容中的名称查找用户: {target_in_content}")
            # 尝试根据昵称找到用户wxid
            try:
                # 遍历群成员，比较昵称
                for member in group_members:
                    member_wxid = member[0]

                    if member_wxid == sender_wxid or member_wxid == bot.wxid:
                        continue  # 跳过自己和机器人

                    # 优先使用display_name，如果为空则使用nickname
                    member_nickname = member[2] if member[2] else member[1]
                    if not member_nickname:
                        member_nickname = member_wxid

                    if (member_nickname and
                        (member_nickname == target_in_content or
                         target_in_content in member_nickname or
                         member_nickname in target_in_content)):
                        target_wxid = member_wxid
                        is_random_robbery = False  # 指定打劫
                        logger.info(f"根据内容中的名称找到目标: {member_nickname}({member_wxid})")
                        break
            except Exception as e:
                logger.error(f"通过昵称查找目标时出错: {e}")

            # 如果仍然没找到，输出提示
            if not target_wxid:
                logger.info(f"无法根据名称 '{target_in_content}' 找到对应用户")
                try:
                    await bot.send_at_message(
                        chatroom_wxid,
                        f"🔍 无法找到用户 '{target_in_content}'，将进行随机打劫",
                        [sender_wxid]
                    )
                except Exception as e:
                    logger.error(f"发送消息失败: {e}")

        # 3. 如果没找到目标，进行随机打劫
        if not target_wxid:
            logger.info("未指定目标或无法识别目标，进行随机打劫")

            # 从群成员中随机选择一个目标(排除自己、机器人和文件传输助手)
            valid_targets = [m[0] for m in group_members if m[0] != sender_wxid and m[0] != bot.wxid and m[0] != "filehelper"]
            logger.debug(f"有效打劫目标数量: {len(valid_targets)}")

            if not valid_targets:
                logger.error("没有有效的打劫目标")
                try:
                    await bot.send_at_message(
                        chatroom_wxid,
                        "🔎 打劫失败！群内没有可用的打劫目标",
                        [sender_wxid]
                    )
                except Exception as e:
                    logger.error(f"发送消息失败: {e}")
                    sender_nickname = await self.get_display_name(sender_wxid, chatroom_wxid)
                    await bot.send_text_message(chatroom_wxid, f"@{sender_nickname} 🔎 打劫失败！群内没有可用的打劫目标")

                logger.error("没有可用的打劫目标")
                return False  # 不允许其他插件处理

            # 随机选择目标并记录日志
            try:
                target_wxid = random.choice(valid_targets)
                logger.info(f"随机选择打劫目标成功: {target_wxid}")
            except Exception as e:
                logger.error(f"随机选择目标时出错: {e}")
                await bot.send_text_message(chatroom_wxid, "❌选择打劫目标时出错！")
                return False

            # 检查目标是否有效
            if target_wxid == sender_wxid:
                try:
                    await bot.send_at_message(
                        chatroom_wxid,
                        "🙅‍♂️ 无法进行操作！不能打劫自己",
                        [sender_wxid]
                    )
                except Exception as e:
                    logger.error(f"发送消息失败: {e}")
                    sender_nickname = await self.get_display_name(sender_wxid, chatroom_wxid)
                    await bot.send_text_message(chatroom_wxid, f"@{sender_nickname} 🙅‍♂️ 无法进行操作！不能打劫自己")

                logger.info("用户尝试打劫自己，已阻止")
                return False  # 不允许其他插件处理

        # 如果目标是机器人，对打劫机器人进行特殊处理
        if target_is_bot or target_wxid == bot.wxid:
            logger.info(f"用户{sender_wxid}尝试打劫机器人")

            # 确保有正确的机器人wxid
            if not target_wxid:
                target_wxid = bot.wxid
                logger.info(f"修正机器人wxid: {target_wxid}")

            # 获取机器人昵称
            try:
                bot_nickname = await bot.get_nickname(bot.wxid) or "机器人"
                logger.info(f"获取机器人昵称: {bot_nickname}")
            except Exception as e:
                logger.error(f"获取机器人昵称失败: {e}")
                bot_nickname = "机器人"

            # 打劫机器人时触发机器人反击事件
            # 打劫机器人固定失败率90%
            if random.randint(1, 100) <= 90:  # 90%的失败率
                # 打劫失败 - 触发机器人反击事件
                points_lost = self.robot_counterattack_base_points + random.randint(
                    -self.robot_counterattack_random_range,
                    self.robot_counterattack_random_range
                )

                # 确保扣分为正数
                points_lost = max(5, points_lost)  # 至少扣5分

                # 更新用户积分
                self.db.add_points(sender_wxid, -points_lost)

                # 随机选择一个机器人反击事件
                event_text = random.choice(ROBOT_COUNTERATTACK_EVENTS).format(points=points_lost)

                # 发送失败消息
                fail_message = (
                    f"📢 {'你随机打劫到了' if is_random_robbery else '你正在打劫'} 👤 {bot_nickname}\n"
                    f"━━━━━━━━━━\n"
                    f"💥 警告！机器人反击 ⚠️\n"
                    f"🔍 {event_text}\n\n"
                    f"💰 你现在拥有: {self.db.get_points(sender_wxid)} 积分 ✨"
                )

                try:
                    await bot.send_at_message(
                        chatroom_wxid,
                        fail_message,
                        [sender_wxid]
                    )
                except Exception as e:
                    logger.error(f"发送打劫机器人失败消息失败: {e}")
                    sender_nickname = await self.get_display_name(sender_wxid, chatroom_wxid)
                    await bot.send_text_message(chatroom_wxid, f"@{sender_nickname} {fail_message}")

                logger.info(f"用户{sender_wxid}打劫机器人失败，触发反击，损失{points_lost}积分")
            else:
                # 打劫成功
                points_gain = random.randint(1, 5)  # 从机器人那里只能抢到较少积分
                self.db.add_points(sender_wxid, points_gain)

                # 随机选择一个成功事件
                event_text = random.choice(SUCCESS_EVENTS).format(points=points_gain)

                # 发送成功消息
                success_message = (
                    f"📢 {'你随机打劫到了' if is_random_robbery else '你正在打劫'} 👤 {bot_nickname}\n"
                    f"━━━━━━━━━━\n"
                    f"🎯 恭喜你！打劫成功 🎉\n"
                    f"💎 {event_text}\n\n"
                    f"💰 你现在拥有: {self.db.get_points(sender_wxid)} 积分 ✨"
                )

                try:
                    await bot.send_at_message(
                        chatroom_wxid,
                        success_message,
                        [sender_wxid]
                    )
                except Exception as e:
                    logger.error(f"发送打劫机器人成功消息失败: {e}")
                    sender_nickname = await self.get_display_name(sender_wxid, chatroom_wxid)
                    await bot.send_text_message(chatroom_wxid, f"@{sender_nickname} {success_message}")

                logger.info(f"用户{sender_wxid}打劫机器人成功，获得{points_gain}积分")

            # 记录抢劫时间
            if not is_admin and not is_whitelisted:
                logger.info(f"记录抢劫时间: {sender_wxid}")
                self.set_last_robbery_time(sender_wxid, datetime.datetime.now())

            return False  # 不允许其他插件处理

        # 普通用户打劫逻辑
        # 检查目标是否在群里 - 使用group_members列表
        target_in_group = any(member[0] == target_wxid for member in group_members)
        if not target_in_group:
            try:
                await bot.send_at_message(
                    chatroom_wxid,
                    "🔍 目标查找失败！该用户不在当前群聊中",
                    [sender_wxid]
                )
            except Exception as e:
                logger.error(f"发送目标不在群聊消息失败: {e}")
                sender_nickname = await self.get_display_name(sender_wxid, chatroom_wxid)
                await bot.send_text_message(chatroom_wxid, f"@{sender_nickname} 🔍 目标查找失败！该用户不在当前群聊中")

            logger.info(f"打劫目标 {target_wxid} 不在当前群聊中")
            return False  # 不允许其他插件处理

        # 获取目标的昵称
        logger.info(f"开始获取目标昵称: {target_wxid}")
        try:
            target_nickname = await self.get_display_name(target_wxid, chatroom_wxid)
            logger.info(f"获取到目标昵称: {target_nickname}")
        except Exception as e:
            logger.error(f"获取目标昵称出错: {e}")
            target_nickname = "神秘人"

        logger.info(f"打劫目标昵称: {target_nickname}")

        # 抢劫成功率判定
        success = random.randint(1, 100) <= self.success_probability

        # 计算积分变化，base_points ± random_range
        points_change = self.base_points + random.randint(
            -self.random_points_range,
            self.random_points_range
        )

        # 确保最小为1积分
        points_change = max(1, points_change)

        logger.info(f"打劫 {'成功' if success else '失败'}, 积分变化: {points_change}")

        # 抢劫结果处理
        if success:
            # 抢劫成功
            logger.info(f"开始处理抢劫成功逻辑")
            try:
                target_points = self.db.get_points(target_wxid)
                logger.debug(f"目标 {target_nickname}({target_wxid}) 当前积分: {target_points}")

                # 如果目标积分不足，则最多只能抢走目标拥有的全部积分
                if target_points < points_change:
                    logger.info(f"目标积分不足，从 {points_change} 调整为 {max(1, target_points)}")
                    points_change = max(1, target_points)

                # 更新积分
                logger.debug(f"更新积分: {target_wxid} -{points_change}, {sender_wxid} +{points_change}")
                self.db.add_points(target_wxid, -points_change)
                self.db.add_points(sender_wxid, points_change)

                # 随机选择一个成功事件
                event_text = random.choice(SUCCESS_EVENTS).format(points=points_change)

                # 构建消息
                message_text = (
                    f"📢 {'你随机打劫到了' if is_random_robbery else '你正在打劫'} 👤 {target_nickname}\n"
                    f"━━━━━━━━━━\n"
                    f"🎯 恭喜你！打劫成功 🎉\n"
                    f"💎 {event_text}\n\n"
                    f"💰 你现在拥有: {self.db.get_points(sender_wxid)} 积分 ✨"
                )

                logger.info(f"打劫成功: {sender_wxid} 从 {target_wxid} 获得 {points_change} 积分")
            except Exception as e:
                logger.error(f"处理抢劫成功逻辑时出错: {e}")
                message_text = f"处理抢劫结果时出错，请联系管理员。"
        else:
            # 抢劫失败
            logger.info(f"开始处理抢劫失败逻辑")
            try:
                # 更新积分
                logger.debug(f"更新积分: {sender_wxid} -{points_change}")
                self.db.add_points(sender_wxid, -points_change)

                # 随机选择一个失败事件
                event_text = random.choice(FAIL_EVENTS).format(points=points_change)

                # 构建消息
                message_text = (
                    f"📢 {'你随机打劫到了' if is_random_robbery else '你正在打劫'} 👤 {target_nickname}\n"
                    f"━━━━━━━━━━\n"
                    f"💥 很抱歉！打劫失败 😫\n"
                    f"🔍 {event_text}\n\n"
                    f"💰 你现在拥有: {self.db.get_points(sender_wxid)} 积分 ✨"
                )

                logger.info(f"打劫失败: {sender_wxid} 损失 {points_change} 积分")
            except Exception as e:
                logger.error(f"处理抢劫失败逻辑时出错: {e}")
                message_text = f"处理抢劫结果时出错，请联系管理员。"

        # 记录抢劫时间 - 仅对非管理员和非白名单用户记录
        if not is_admin and not is_whitelisted:
            logger.info(f"记录抢劫时间: {sender_wxid}")
            self.set_last_robbery_time(sender_wxid, datetime.datetime.now())

        # 发送消息
        logger.info(f"准备发送抢劫结果消息")
        try:
            logger.debug(f"发送@消息到群 {chatroom_wxid}, 提及用户 {sender_wxid}")
            await bot.send_at_message(chatroom_wxid, message_text, [sender_wxid])
            logger.info(f"已成功发送打劫结果消息")
        except Exception as e:
            logger.error(f"发送打劫结果消息失败: {e}")
            # 尝试使用普通消息发送
            try:
                logger.debug(f"尝试使用普通消息发送")
                sender_nickname = await self.get_display_name(sender_wxid, chatroom_wxid)
                await bot.send_text_message(chatroom_wxid, f"@{sender_nickname} {message_text}")
                logger.info("使用普通文本消息发送打劫结果成功")
            except Exception as e:
                logger.error(f"使用普通文本消息发送也失败: {e}")

        logger.info(f"打劫流程完成")
        # 无论抢劫成功与否，都不让其他插件继续处理
        return False

    def get_last_robbery_time(self, wxid: str) -> datetime.datetime:
        """获取用户上次抢劫时间 - 从pluginsDB中的robbery_time表获取"""
        try:
            # 连接数据库
            conn = sqlite3.connect(self.plugins_db_path)
            cursor = conn.cursor()

            # 查询用户的打劫时间
            cursor.execute("""
            SELECT robbery_time FROM robbery_time
            WHERE wxid = ?
            """, (wxid,))

            result = cursor.fetchone()
            conn.close()

            if result:
                try:
                    # 将字符串转换为datetime对象
                    return datetime.datetime.fromisoformat(result[0])
                except ValueError:
                    logger.error(f"无法解析时间戳: {result[0]}")

            return None

        except Exception as e:
            logger.error(f"获取打劫时间出错: {e}")
            return None

    def set_last_robbery_time(self, wxid: str, time: datetime.datetime) -> bool:
        """设置用户上次抢劫时间 - 保存到pluginsDB中的robbery_time表"""
        try:
            # 连接数据库
            conn = sqlite3.connect(self.plugins_db_path)
            cursor = conn.cursor()

            # 使用REPLACE语法，如果存在则更新，不存在则插入
            cursor.execute("""
            REPLACE INTO robbery_time (wxid, robbery_time)
            VALUES (?, ?)
            """, (wxid, time.isoformat()))

            conn.commit()
            conn.close()

            return True

        except Exception as e:
            logger.error(f"保存打劫时间出错: {e}")
            return False

    async def get_display_name(self, wxid: str, group_wxid: str) -> str:
        """获取用户在群聊中的显示名称，优先使用display_name"""
        try:
            # 从contacts.db获取成员信息
            conn = self.get_contacts_db()
            cursor = conn.cursor()
            cursor.execute(
                "SELECT nickname, display_name FROM group_members WHERE group_wxid = ? AND member_wxid = ?",
                (group_wxid, wxid)
            )
            result = cursor.fetchone()

            if result:
                # 优先使用display_name，如果为空则使用nickname
                nickname, display_name = result
                if display_name:
                    return display_name
                elif nickname:
                    return nickname

            # 如果在数据库中找不到，尝试使用API获取
            try:
                api_nickname = await bot.get_nickname(wxid)
                if api_nickname:
                    return api_nickname
            except Exception as e:
                logger.error(f"使用API获取昵称时出错: {e}")

            # 如果所有方法都失败，返回wxid的一部分
            return wxid[:8] + "..." if len(wxid) > 10 else wxid

        except Exception as e:
            logger.error(f"获取用户显示名称时出错: {e}")
            # 如果出错，返回wxid的一部分
            return wxid[:8] + "..." if len(wxid) > 10 else wxid

    def __del__(self):
        """清理资源"""
        if hasattr(self, 'contacts_db') and self.contacts_db:
            try:
                self.contacts_db.close()
            except:
                pass