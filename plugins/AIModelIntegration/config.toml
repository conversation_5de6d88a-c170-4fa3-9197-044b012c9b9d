[AIModelIntegration]
# 是否启用插件
enable = true

# 基础设置
[AIModelIntegration.basic]
# 是否启用@触发（只适用于群聊）
enable_at_trigger = true
# 是否启用唤醒词触发
enable_wakeup_trigger = true
# 唤醒后回复的次数（默认3次）
wakeup_reply_count = 3
# 唤醒词列表
wakeup_words = ["ting", "ai机器人", "AI机器人"]
# 立即休眠指令
sleep_commands = ["休眠", "睡觉", "拜拜"]

# 功能指令配置
[AIModelIntegration.commands]
# 文生文指令
text_commands = ["聊天", "问问", "AI", "ai"]
# 文生图指令  
image_commands = ["画图", "生成图片", "文生图"]
# 图生图指令
image_to_image_commands = ["图生图", "修改图片"]
# 图片识别指令（通过引用回复图片消息触发）
image_recognition_commands = ["识图", "看图", "图片识别", "图像识别", "识别图片", "分析图片", "这是什么"]
# 文生视频指令
video_commands = ["生成视频", "文生视频"]
# 模型切换指令
switch_model_commands = ["切换模型", "换模型", "使用模型"]
# 清空会话指令
clear_session_commands = ["清空会话", "重置对话", "新对话"]
# 帮助指令
help_commands = ["AI帮助", "模型帮助", "使用说明"]

# 豆包模型配置
[AIModelIntegration.models.doubao]
enable = true
name = "豆包"
# 豆包的cookies，从浏览器cookie中获取完整的cookie字符串
cookies = "sessionid=92a5a0c081d55b54541685d7fc5133b0"
# 默认智能体ID
assistant_id = "497858"
# 支持的功能：text, image_gen, image_recognition
supported_features = ["text", "image_gen", "image_recognition"]
# 每次使用消耗的积分
price = 1

# 默认模型（用户首次使用时的默认选择）
default_model = "doubao"

# 会话管理
[AIModelIntegration.session]
# 单个会话最大消息数（超过后自动清理旧消息）
max_messages_per_session = 20
# 会话超时时间（分钟，超过后自动清空会话）
session_timeout = 60

# 指令格式提示
command_format = """-----AI大模型集成-----
🤖 基础功能：
• @机器人 或发送唤醒词（ting/ai机器人/AI机器人）开始对话
• 聊天/问问/AI + 内容 - 文本对话
• 画图/生成图片 + 描述 - 文生图
• 引用图片回复"识图/看图/图片识别" - 图片识别

🔧 管理功能：
• 清空会话 - 重置对话历史
• 休眠/睡觉 - 结束当前会话
• AI帮助 - 显示此帮助

📱 支持模型：
• 豆包 - 字节跳动AI模型

💡 使用说明：
• 群聊中可@机器人或使用唤醒词
• 私聊中直接发送唤醒词即可
• 唤醒后无需重复@，连续对话3次
• 支持图文混合输入
"""

# 积分设置
price = 1
admin_ignore = true
whitelist_ignore = true
