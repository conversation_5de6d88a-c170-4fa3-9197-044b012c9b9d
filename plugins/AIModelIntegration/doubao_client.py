"""
豆包API客户端
老王出品，重新实现豆包功能，参考能用的豆包模块！
艹，这次一定要把这个憨批API搞定！
"""

import asyncio
import json
import time
import uuid
import httpx
import base64
import re
from typing import Dict, List, Any, Optional
from urllib.parse import quote
from loguru import logger


class DoubaoClient:
    """豆包API客户端，艹，重新实现这个破API！"""
    
    def __init__(self, cookies: str, assistant_id: str = "497858"):
        self.cookies = cookies
        self.assistant_id = assistant_id
        self.model_name = "doubao"
        
        # 豆包API配置
        self.api_base_url = "https://www.doubao.com/samantha/chat/completion"
        self.upload_auth_url = "https://www.doubao.com/alice/resource/prepare_upload"
        self.imagex_base_url = "https://imagex.bytedanceapi.com/"
        
        # 豆包API常量
        self.device_id = "7468716989062841895"
        self.tea_uuid = "7468716986638386703"
        self.web_id = "7468716986638386703"
        
        # httpx客户端管理
        self._client = None
        self._client_lock = asyncio.Lock()
        
        # 最大重试次数
        self.max_retry_count = 3
        self.retry_delay = 5

    async def get_httpx_client(self):
        """创建httpx客户端"""
        return httpx.AsyncClient(
            timeout=httpx.Timeout(connect=10.0, read=20.0, write=20.0, pool=5.0),
            verify=False,  # 禁用SSL验证
            follow_redirects=True,
            limits=httpx.Limits(max_connections=20, max_keepalive_connections=10),
        )

    async def get_session(self):
        """获取或创建httpx异步客户端"""
        async with self._client_lock:
            if self._client is None or self._client.is_closed:
                self._client = await self.get_httpx_client()
            return self._client

    async def close_session(self):
        """关闭httpx客户端"""
        async with self._client_lock:
            if self._client and not self._client.is_closed:
                await self._client.aclose()
                self._client = None

    def _generate_headers(self):
        """生成请求头"""
        # 生成随机的x-flow-trace
        flow_trace = f"04-{uuid.uuid4().hex[:16]}-{uuid.uuid4().hex[:16]}-01"

        return {
            "Accept": "*/*",
            "Accept-Encoding": "gzip, deflate",
            "Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
            "Connection": "keep-alive",
            "Content-Type": "application/json; charset=utf-8",
            "Cookie": self.cookies,
            "Host": "www.doubao.com",
            "Origin": "https://www.doubao.com",
            "Referer": "https://www.doubao.com/chat/",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "x-flow-trace": flow_trace,
            "Agw-Js-Conv": "str",
            "X-Requested-With": "mark.via",
            "last-event-id": "undefined",
            "Sec-Fetch-Site": "same-origin",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Dest": "empty"
        }

    async def check_token_status(self) -> bool:
        """检查token状态"""
        try:
            # 简单的健康检查，发送一个测试请求
            test_messages = [{"role": "user", "content": "hi"}]
            result = await self.create_completion(test_messages)
            logger.info(f"豆包token检查结果: {result}")
            return result is not None and result.get("type") == "text" and result.get("text", "").strip()
        except Exception as e:
            logger.error(f"豆包token状态检查失败: {str(e)}")
            return False

    def _prepare_messages(self, messages: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """准备消息格式，艹，豆包的消息格式真是够奇葩的！"""
        prepared = []
        
        for msg in messages:
            role = msg.get("role", "user")
            content = msg.get("content", "")
            
            # 处理不同类型的内容
            if isinstance(content, str):
                # 纯文本消息
                prepared_msg = {
                    "content": json.dumps({"text": content}),
                    "content_type": 2001,
                    "attachments": [],
                    "references": []
                }
            elif isinstance(content, list):
                # 多模态消息（包含图片）
                text_parts = []
                image_parts = []
                
                for part in content:
                    if part.get("type") == "text":
                        text_parts.append(part.get("text", ""))
                    elif part.get("type") == "image_url":
                        image_url = part.get("image_url", {}).get("url", "")
                        if image_url:
                            image_parts.append(image_url)
                
                # 合并文本内容
                text_content = " ".join(text_parts)
                
                if image_parts:
                    # 包含图片的消息
                    prepared_msg = {
                        "content": json.dumps({
                            "text": text_content,
                            "images": image_parts
                        }),
                        "content_type": 2001,
                        "attachments": [],
                        "references": []
                    }
                else:
                    # 只有文本
                    prepared_msg = {
                        "content": json.dumps({"text": text_content}),
                        "content_type": 2001,
                        "attachments": [],
                        "references": []
                    }
            else:
                # 默认处理
                prepared_msg = {
                    "content": json.dumps({"text": str(content)}),
                    "content_type": 2001,
                    "attachments": [],
                    "references": []
                }
            
            prepared.append(prepared_msg)
        
        return prepared

    async def create_completion(self, messages: List[Dict[str, Any]], stream: bool = False) -> Any:
        """创建对话补全，艹，这个接口的参数真是多得要命！"""
        try:
            logger.info(f"豆包API请求开始，消息数量: {len(messages)}")

            # 准备消息
            prepared_messages = self._prepare_messages(messages)

            # 构造请求URL参数（完全按照DoubaoChat的格式）
            params = {
                "aid": "497858",
                "device_id": self.device_id,
                "device_platform": "web",
                "language": "zh",
                "pc_version": "2.16.4",
                "pkg_type": "release_version",
                "real_aid": "497858",
                "region": "CN",
                "samantha_web": "1",
                "sys_region": "CN",
                "tea_uuid": self.tea_uuid,
                "use-olympus-account": "1",
                "version_code": "20800",
                "web_id": self.web_id
            }

            # 创建随机的会话ID和消息ID（参考DoubaoChat的实现）
            conversation_id = f"38{int(time.time() * 10000)}8"
            section_id = f"{conversation_id[:10]}4{conversation_id[10:]}"
            message_id = f"ba0d6520-{uuid.uuid4().hex[:4]}-11f0-a188-0d4d{uuid.uuid4().hex[:8]}"

            # 构造请求数据（完全按照DoubaoChat的格式）
            request_data = {
                "messages": prepared_messages,
                "completion_option": {
                    "is_regen": False,
                    "with_suggest": True,
                    "need_create_conversation": True,
                    "launch_stage": 1,
                    "is_replace": False,
                    "is_delete": False,
                    "message_from": 0,
                    "use_deep_think": False,
                    "use_auto_cot": False,
                    "event_id": "0"
                },
                "section_id": section_id,
                "conversation_id": conversation_id,
                "local_message_id": message_id
            }

            # 获取httpx客户端
            client = await self.get_session()

            # 设置请求头
            headers = self._generate_headers()

            # 构造完整URL
            url = f"{self.api_base_url}?" + "&".join([f"{k}={quote(str(v))}" for k,v in params.items()])

            # 发送POST请求
            response = await client.post(
                url,
                json=request_data,
                headers=headers,
                timeout=httpx.Timeout(connect=10.0, read=300.0, write=60.0, pool=10.0)
            )

            logger.info(f"豆包API响应状态码: {response.status_code}")
            logger.info(f"豆包API响应头: {dict(response.headers)}")

            if response.status_code != 200:
                error_text = response.content
                logger.error(f"豆包API请求失败: 状态码={response.status_code}, 响应内容={error_text}")
                raise ValueError(f"API请求失败: {response.status_code}")

            # 记录响应内容的前500字符用于调试
            content_preview = str(response.content)[:500] if response.content else "空内容"
            logger.info(f"豆包API响应内容预览: {content_preview}")

            # 创建兼容的响应对象
            class HttpResponse:
                def __init__(self, status_code, body, headers=None):
                    self.code = status_code
                    self.status_code = status_code
                    self.body = body
                    self.headers = headers or {}

            # 转换为兼容格式
            compat_response = HttpResponse(
                status_code=response.status_code,
                body=response.content,
                headers=dict(response.headers)
            )

            # 处理响应
            result = await self._process_stream(compat_response)

            if result:
                logger.info(f"豆包API处理结果: {result}")
                return result
            else:
                logger.warning("豆包API处理结果为空")

            return {"type": "text", "text": ""}

        except Exception as e:
            logger.error(f"豆包API调用失败: {e}")
            return {"type": "text", "text": ""}

    async def _process_stream(self, response) -> Optional[Dict[str, Any]]:
        """处理SSE响应流，艹，这个流式响应真是够复杂的！"""
        result_text = ""
        result_data = {"type": "text", "text": ""}

        try:
            # 解码响应数据（使用body而不是content）
            if isinstance(response.body, str):
                chunk = response.body.encode('utf-8')
            else:
                chunk = response.body

            try:
                decoded_chunk = chunk.decode('utf-8', errors='ignore')
                buffer = decoded_chunk
            except UnicodeDecodeError:
                return None

            # 处理完整的SSE事件
            while "\n\n" in buffer:
                parts = buffer.split("\n\n", 1)
                event = parts[0]
                buffer = parts[1]

                if not event.strip():
                    continue

                # 提取"data:"行
                data_line = None
                for line in event.split("\n"):
                    if line.startswith("data:"):
                        data_line = line[5:].strip()
                        break

                if not data_line:
                    continue

                # 解析数据
                try:
                    event_data = json.loads(data_line)
                    if not isinstance(event_data, dict):
                        continue

                    if "event_type" not in event_data:
                        continue

                    event_type = event_data["event_type"]

                    # 处理结束事件
                    if event_type == 2003:
                        if "tts_content" in event_data:
                            full_text = event_data["tts_content"]
                            if full_text and len(full_text) > len(result_text):
                                result_text = full_text
                                result_data["text"] = result_text

                        result_data["text"] = result_text
                        return result_data

                    # 处理正常消息事件
                    if event_type == 2001 and "event_data" in event_data:
                        try:
                            inner_data = json.loads(event_data["event_data"])

                            if "message" not in inner_data:
                                continue

                            message = inner_data["message"]
                            is_finish = inner_data.get("is_finish", False)

                            if "content_type" not in message or "content" not in message:
                                continue

                            content_type = message["content_type"]
                            try:
                                content = json.loads(message["content"])
                            except json.JSONDecodeError:
                                continue

                            # 处理文本内容
                            if content_type == 2001:
                                if "text" in content:
                                    text = content["text"]
                                    result_text += text
                                    result_data["text"] = result_text

                                    # 当事件标记为完成时，检查tts_content
                                    if is_finish and "tts_content" in inner_data:
                                        full_text = inner_data["tts_content"]
                                        if full_text and len(full_text) > len(result_text):
                                            result_text = full_text
                                            result_data["text"] = result_text

                            elif content_type == 2030:  # 读取状态含文本内容
                                if "text" in content:
                                    text = content["text"]
                                    result_text += text
                                    result_data["text"] = result_text

                            elif content_type == 2008 or content_type == 2018:  # 处理搜索和特殊响应内容
                                if "text" in content:
                                    text = content["text"]
                                    result_text += text
                                    result_data["text"] = result_text

                                    if is_finish and "tts_content" in inner_data:
                                        full_text = inner_data["tts_content"]
                                        if full_text and len(full_text) > len(result_text):
                                            result_text = full_text
                                            result_data["text"] = result_text

                        except Exception:
                            continue

                except json.JSONDecodeError:
                    continue
                except Exception:
                    continue

            # 如果有文本内容，返回文本结果
            if result_text:
                result_data["text"] = result_text
                return result_data

            # 返回空结果
            result_data["text"] = ""
            return result_data

        except Exception as e:
            logger.error(f"豆包处理响应流失败: {e}")
            if result_text:
                result_data["text"] = result_text
                return result_data
            return None

    async def generate_image(self, prompt: str) -> Optional[Dict[str, Any]]:
        """生成图片，艹，这个功能用户最喜欢了！"""
        try:
            logger.info(f"豆包图片生成请求: {prompt}")

            # 构造图片生成的消息
            messages = [{
                "role": "user",
                "content": f"画图 {prompt}"
            }]

            # 调用对话接口，豆包会自动识别画图指令
            result = await self.create_completion(messages)

            if result and result.get("type") == "text":
                text = result.get("text", "")

                # 检查是否包含图片链接
                image_urls = self._extract_image_urls(text)
                if image_urls:
                    return {
                        "type": "image",
                        "data": image_urls,
                        "text": text
                    }
                else:
                    # 如果没有图片，返回文本结果
                    return {
                        "type": "text",
                        "text": text
                    }

            return None

        except Exception as e:
            logger.error(f"豆包图片生成失败: {str(e)}")
            return None

    def _extract_image_urls(self, text: str) -> List[str]:
        """从文本中提取图片URL"""
        import re

        # 匹配各种可能的图片URL格式
        url_patterns = [
            r'https?://[^\s<>"]+\.(?:jpg|jpeg|png|gif|webp)',
            r'https?://imagex\.bytedanceapi\.com/[^\s<>"]+',
            r'https?://[^\s<>"]*image[^\s<>"]*',
        ]

        urls = []
        for pattern in url_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            urls.extend(matches)

        # 去重并返回
        return list(set(urls))

    async def recognize_image(self, image_url: str, prompt: str = "请识别这张图片") -> Optional[Dict[str, Any]]:
        """识别图片，艹，这个功能也挺实用的！"""
        try:
            logger.info(f"豆包图片识别请求: {image_url}")

            # 构造包含图片的消息
            messages = [{
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": prompt
                    },
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": image_url
                        }
                    }
                ]
            }]

            # 调用对话接口
            result = await self.create_completion(messages)

            if result and result.get("type") == "text":
                return {
                    "type": "text",
                    "text": result.get("text", "")
                }

            return None

        except Exception as e:
            logger.error(f"豆包图片识别失败: {str(e)}")
            return None
