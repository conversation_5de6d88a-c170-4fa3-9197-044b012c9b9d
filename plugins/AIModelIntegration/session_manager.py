"""
会话管理器
管理用户的AI对话会话，支持@触发和唤醒词机制
老王出品，专门管理这些SB用户的会话状态！
"""

import time
from typing import Dict, List, Any, Optional, Set
from dataclasses import dataclass, field
from loguru import logger


@dataclass
class SessionData:
    """会话数据结构"""
    user_id: str
    chat_id: str  # 群聊ID或私聊ID
    model_name: str  # 当前使用的模型
    messages: List[Dict[str, Any]] = field(default_factory=list)  # 对话历史
    is_active: bool = False  # 是否处于活跃状态（唤醒后）
    reply_count: int = 0  # 已回复次数
    max_replies: int = 3  # 最大回复次数
    last_activity: float = field(default_factory=time.time)  # 最后活动时间
    created_at: float = field(default_factory=time.time)  # 创建时间


class SessionManager:
    """
    会话管理器
    艹，管理这些用户的会话状态真是够麻烦的！
    """
    
    def __init__(self, max_messages_per_session: int = 20, session_timeout: int = 60):
        self.sessions: Dict[str, SessionData] = {}  # 存储所有会话
        self.max_messages_per_session = max_messages_per_session
        self.session_timeout = session_timeout * 60  # 转换为秒
        
        # 活跃会话集合（用于快速查找唤醒状态的会话）
        self.active_sessions: Set[str] = set()
        
    def _get_session_key(self, user_id: str, chat_id: str) -> str:
        """生成会话键值"""
        return f"{user_id}_{chat_id}"
    
    def create_or_get_session(self, user_id: str, chat_id: str, model_name: str, max_replies: int = 3) -> SessionData:
        """
        创建或获取会话
        如果会话不存在则创建新会话，否则返回现有会话
        """
        session_key = self._get_session_key(user_id, chat_id)
        
        if session_key not in self.sessions:
            # 创建新会话
            session = SessionData(
                user_id=user_id,
                chat_id=chat_id,
                model_name=model_name,
                max_replies=max_replies
            )
            self.sessions[session_key] = session
            logger.info(f"创建新会话: {session_key}, 模型: {model_name}")
        else:
            # 更新现有会话的最后活动时间
            session = self.sessions[session_key]
            session.last_activity = time.time()
            
        return self.sessions[session_key]
    
    def activate_session(self, user_id: str, chat_id: str, model_name: str, max_replies: int = 3) -> SessionData:
        """
        激活会话（唤醒）
        用户发送唤醒词或@机器人时调用
        """
        session = self.create_or_get_session(user_id, chat_id, model_name, max_replies)
        session.is_active = True
        session.reply_count = 0
        session.last_activity = time.time()
        
        session_key = self._get_session_key(user_id, chat_id)
        self.active_sessions.add(session_key)
        
        logger.info(f"激活会话: {session_key}, 模型: {model_name}, 最大回复次数: {max_replies}")
        return session
    
    def deactivate_session(self, user_id: str, chat_id: str) -> bool:
        """
        停用会话（休眠）
        用户发送休眠指令时调用
        """
        session_key = self._get_session_key(user_id, chat_id)
        
        if session_key in self.sessions:
            session = self.sessions[session_key]
            session.is_active = False
            session.reply_count = 0
            
            if session_key in self.active_sessions:
                self.active_sessions.remove(session_key)
                
            logger.info(f"停用会话: {session_key}")
            return True
        
        return False
    
    def is_session_active(self, user_id: str, chat_id: str) -> bool:
        """检查会话是否处于活跃状态"""
        session_key = self._get_session_key(user_id, chat_id)
        return session_key in self.active_sessions
    
    def get_session(self, user_id: str, chat_id: str) -> Optional[SessionData]:
        """获取会话数据"""
        session_key = self._get_session_key(user_id, chat_id)
        return self.sessions.get(session_key)
    
    def add_message(self, user_id: str, chat_id: str, role: str, content: Any) -> bool:
        """
        添加消息到会话历史
        返回是否成功添加
        """
        session_key = self._get_session_key(user_id, chat_id)
        
        if session_key not in self.sessions:
            logger.warning(f"尝试向不存在的会话添加消息: {session_key}")
            return False
        
        session = self.sessions[session_key]
        
        # 添加消息
        message = {
            "role": role,
            "content": content,
            "timestamp": time.time()
        }
        session.messages.append(message)
        session.last_activity = time.time()
        
        # 如果消息数量超过限制，移除最旧的消息（保留系统消息）
        if len(session.messages) > self.max_messages_per_session:
            # 找到第一个非系统消息并移除
            for i, msg in enumerate(session.messages):
                if msg.get('role') != 'system':
                    session.messages.pop(i)
                    logger.info(f"会话 {session_key} 消息数量超限，移除最旧消息")
                    break
        
        logger.debug(f"向会话 {session_key} 添加消息: {role}")
        return True
    
    def increment_reply_count(self, user_id: str, chat_id: str) -> bool:
        """
        增加回复计数
        返回会话是否仍然活跃（未达到最大回复次数）
        """
        session_key = self._get_session_key(user_id, chat_id)
        
        if session_key not in self.sessions:
            return False
        
        session = self.sessions[session_key]
        session.reply_count += 1
        session.last_activity = time.time()
        
        # 检查是否达到最大回复次数
        if session.reply_count >= session.max_replies:
            session.is_active = False
            if session_key in self.active_sessions:
                self.active_sessions.remove(session_key)
            logger.info(f"会话 {session_key} 达到最大回复次数，自动停用")
            return False
        
        logger.debug(f"会话 {session_key} 回复计数: {session.reply_count}/{session.max_replies}")
        return True
    
    def clear_session_messages(self, user_id: str, chat_id: str) -> bool:
        """清空会话消息历史"""
        session_key = self._get_session_key(user_id, chat_id)
        
        if session_key not in self.sessions:
            return False
        
        session = self.sessions[session_key]
        session.messages.clear()
        session.last_activity = time.time()
        
        logger.info(f"清空会话 {session_key} 的消息历史")
        return True
    
    def switch_model(self, user_id: str, chat_id: str, new_model: str) -> bool:
        """切换会话使用的模型"""
        session_key = self._get_session_key(user_id, chat_id)
        
        if session_key not in self.sessions:
            return False
        
        session = self.sessions[session_key]
        old_model = session.model_name
        session.model_name = new_model
        session.last_activity = time.time()
        
        logger.info(f"会话 {session_key} 模型切换: {old_model} -> {new_model}")
        return True
    
    def get_session_messages(self, user_id: str, chat_id: str) -> List[Dict[str, Any]]:
        """获取会话的消息历史（不包含timestamp）"""
        session_key = self._get_session_key(user_id, chat_id)
        
        if session_key not in self.sessions:
            return []
        
        session = self.sessions[session_key]
        
        # 返回不包含timestamp的消息列表
        return [
            {k: v for k, v in msg.items() if k != 'timestamp'}
            for msg in session.messages
        ]
    
    def cleanup_expired_sessions(self):
        """清理过期的会话"""
        current_time = time.time()
        expired_keys = []
        
        for session_key, session in self.sessions.items():
            if current_time - session.last_activity > self.session_timeout:
                expired_keys.append(session_key)
        
        for key in expired_keys:
            if key in self.active_sessions:
                self.active_sessions.remove(key)
            del self.sessions[key]
            logger.info(f"清理过期会话: {key}")
        
        if expired_keys:
            logger.info(f"清理了 {len(expired_keys)} 个过期会话")
    
    def get_session_stats(self) -> Dict[str, Any]:
        """获取会话统计信息"""
        total_sessions = len(self.sessions)
        active_sessions = len(self.active_sessions)
        
        return {
            "total_sessions": total_sessions,
            "active_sessions": active_sessions,
            "inactive_sessions": total_sessions - active_sessions
        }
    
    def get_user_sessions(self, user_id: str) -> List[SessionData]:
        """获取指定用户的所有会话"""
        user_sessions = []
        for session in self.sessions.values():
            if session.user_id == user_id:
                user_sessions.append(session)
        return user_sessions
