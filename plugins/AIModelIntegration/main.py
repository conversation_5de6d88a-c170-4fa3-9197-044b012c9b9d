"""
AI大模型集成插件
集成豆包和千问等AI模型，支持文生文、文生图、图生图、图片识别、文生视频等功能
老王出品，必属精品！艹，这个插件功能真是够全面的！
"""

import os
import re
import asyncio
import tomllib
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

from loguru import logger
from utils.plugin_base import PluginBase
from utils.decorators import on_text_message, on_image_message, on_at_message, on_quote_message, on_xml_message
from WechatAPI import WechatAPIClient

from .doubao_client import DoubaoClient
from .session_manager import SessionManager


@dataclass
class ModelConfig:
    """模型配置数据类"""
    enable: bool
    name: str
    supported_features: List[str]
    price: int
    client: Any = None  # 客户端实例


class AIModelIntegration(PluginBase):
    """
    AI大模型集成插件
    艹，这个插件集成了豆包和千问，功能强大得一批！
    """
    
    description = "AI大模型集成插件，支持豆包、千问等多种AI模型"
    author = "老王"
    version = "1.0.0"
    is_ai_platform = True  # 标记为AI平台插件
    
    def __init__(self):
        super().__init__()
        
        # 配置相关
        self.enable = False
        self.models: Dict[str, ModelConfig] = {}
        self.default_model = "doubao"
        
        # 基础设置
        self.enable_at_trigger = True
        self.enable_wakeup_trigger = True
        self.wakeup_reply_count = 3
        self.wakeup_words = ["ting", "ai机器人", "AI机器人"]
        self.sleep_commands = ["休眠", "睡觉", "拜拜"]
        
        # 功能指令
        self.text_commands = ["聊天", "问问", "AI", "ai"]
        self.image_commands = ["画图", "生成图片", "文生图"]
        self.image_to_image_commands = ["图生图", "修改图片"]
        self.image_recognition_commands = ["识图", "看图", "图片识别"]
        self.video_commands = ["生成视频", "文生视频"]
        self.switch_model_commands = ["切换模型", "换模型", "使用模型"]
        self.clear_session_commands = ["清空会话", "重置对话", "新对话"]
        self.help_commands = ["AI帮助", "模型帮助", "使用说明"]
        
        # 会话管理器
        self.session_manager = None
        
        # 加载配置
        self._load_config()
    
    def _load_config(self):
        """加载配置文件，艹，这个配置真是够复杂的！"""
        config_path = os.path.join(os.path.dirname(__file__), "config.toml")
        
        try:
            with open(config_path, "rb") as f:
                config = tomllib.load(f)
            
            # 读取基本配置
            basic_config = config.get("AIModelIntegration", {})
            self.enable = basic_config.get("enable", False)
            
            # 读取基础设置
            basic_settings = basic_config.get("basic", {})
            self.enable_at_trigger = basic_settings.get("enable_at_trigger", True)
            self.enable_wakeup_trigger = basic_settings.get("enable_wakeup_trigger", True)
            self.wakeup_reply_count = basic_settings.get("wakeup_reply_count", 3)
            self.wakeup_words = basic_settings.get("wakeup_words", ["ting", "ai机器人", "AI机器人"])
            self.sleep_commands = basic_settings.get("sleep_commands", ["休眠", "睡觉", "拜拜"])
            
            # 读取指令配置
            commands_config = basic_config.get("commands", {})
            self.text_commands = commands_config.get("text_commands", ["聊天", "问问", "AI", "ai"])
            self.image_commands = commands_config.get("image_commands", ["画图", "生成图片", "文生图"])
            self.image_to_image_commands = commands_config.get("image_to_image_commands", ["图生图", "修改图片"])
            self.image_recognition_commands = commands_config.get("image_recognition_commands", ["识图", "看图", "图片识别", "图像识别", "识别图片", "分析图片", "这是什么"])
            self.video_commands = commands_config.get("video_commands", ["生成视频", "文生视频"])
            self.switch_model_commands = commands_config.get("switch_model_commands", ["切换模型", "换模型", "使用模型"])
            self.clear_session_commands = commands_config.get("clear_session_commands", ["清空会话", "重置对话", "新对话"])
            self.help_commands = commands_config.get("help_commands", ["AI帮助", "模型帮助", "使用说明"])
            
            # 读取模型配置
            models_config = basic_config.get("models", {})
            self.default_model = basic_config.get("default_model", "doubao")
            
            # 初始化豆包模型
            if "doubao" in models_config:
                doubao_config = models_config["doubao"]
                if doubao_config.get("enable", False):
                    cookies = doubao_config.get("cookies", "")
                    assistant_id = doubao_config.get("assistant_id", "497858")

                    if cookies and cookies not in ["your_doubao_cookies_here", "sessionid=your_sessionid_here"]:
                        doubao_client = DoubaoClient(cookies, assistant_id)
                        self.models["doubao"] = ModelConfig(
                            enable=True,
                            name=doubao_config.get("name", "豆包"),
                            supported_features=doubao_config.get("supported_features", ["text"]),
                            price=doubao_config.get("price", 1),
                            client=doubao_client
                        )
                        logger.info("豆包模型初始化成功")
                    else:
                        logger.warning("豆包模型配置无效：cookies未设置")
            
            # 初始化会话管理器
            session_config = basic_config.get("session", {})
            max_messages = session_config.get("max_messages_per_session", 20)
            session_timeout = session_config.get("session_timeout", 60)
            self.session_manager = SessionManager(max_messages, session_timeout)
            
            logger.info(f"AI大模型集成插件配置加载完成，启用状态: {self.enable}")
            logger.info(f"可用模型: {list(self.models.keys())}")
            logger.info(f"默认模型: {self.default_model}")

            # 输出模型详细信息
            for model_name, model_config in self.models.items():
                logger.info(f"模型 {model_name}: 启用={model_config.enable}, 名称={model_config.name}, 功能={model_config.supported_features}")
            
        except Exception as e:
            logger.error(f"加载AI大模型集成插件配置失败: {str(e)}")
            self.enable = False
    
    async def async_init(self):
        """异步初始化"""
        if not self.enable:
            return
        
        # 检查模型token状态
        for model_name, model_config in self.models.items():
            try:
                if hasattr(model_config.client, 'check_token_status'):
                    is_valid = await model_config.client.check_token_status()
                    if is_valid:
                        logger.success(f"{model_config.name} token状态正常")
                    else:
                        logger.warning(f"{model_config.name} token状态异常，请检查配置")
            except Exception as e:
                logger.error(f"检查{model_config.name} token状态失败: {str(e)}")
    
    def _is_wakeup_message(self, content: str) -> bool:
        """检查是否为唤醒消息"""
        if not self.enable_wakeup_trigger:
            return False
        
        content_lower = content.lower().strip()
        return any(word.lower() in content_lower for word in self.wakeup_words)
    
    def _is_sleep_message(self, content: str) -> bool:
        """检查是否为休眠消息"""
        content_stripped = content.strip()
        return any(cmd in content_stripped for cmd in self.sleep_commands)
    
    def _extract_command_and_content(self, content: str) -> tuple[Optional[str], str]:
        """
        提取指令和内容
        返回 (指令类型, 剩余内容)
        """
        content_stripped = content.strip()
        
        # 检查各种指令，优先级从高到低
        # 1. 帮助指令优先级最高，避免被其他指令误匹配
        for cmd in self.help_commands:
            if content_stripped == cmd or content_stripped.startswith(cmd + " "):
                logger.info(f"匹配到帮助指令: {cmd}")
                return "help", content_stripped[len(cmd):].strip()

        # 2. 会话管理指令
        for cmd in self.clear_session_commands:
            if content_stripped.startswith(cmd):
                return "clear_session", content_stripped[len(cmd):].strip()

        for cmd in self.switch_model_commands:
            if content_stripped.startswith(cmd):
                return "switch_model", content_stripped[len(cmd):].strip()

        # 3. 功能指令
        for cmd in self.image_commands:
            if content_stripped.startswith(cmd):
                return "image_gen", content_stripped[len(cmd):].strip()

        for cmd in self.image_to_image_commands:
            if content_stripped.startswith(cmd):
                return "image_to_image", content_stripped[len(cmd):].strip()

        for cmd in self.image_recognition_commands:
            if content_stripped.startswith(cmd):
                return "image_recognition", content_stripped[len(cmd):].strip()

        for cmd in self.video_commands:
            if content_stripped.startswith(cmd):
                return "video", content_stripped[len(cmd):].strip()

        # 4. 文本对话指令优先级最低，避免误匹配其他指令
        for cmd in self.text_commands:
            if content_stripped.startswith(cmd):
                return "text", content_stripped[len(cmd):].strip()
        
        return None, content_stripped
    
    def _get_available_models(self) -> List[str]:
        """获取可用的模型列表"""
        available = [name for name, config in self.models.items() if config.enable]
        logger.info(f"检查可用模型: 总模型数={len(self.models)}, 可用模型={available}")
        for name, config in self.models.items():
            logger.info(f"模型 {name}: enable={config.enable}")
        return available
    
    def _get_model_by_name(self, name: str) -> Optional[str]:
        """根据名称获取模型键值"""
        name_lower = name.lower().strip()
        
        for model_key, model_config in self.models.items():
            if (model_key.lower() == name_lower or 
                model_config.name.lower() == name_lower):
                return model_key
        
        return None
    
    async def _send_help_message(self, bot: WechatAPIClient, chat_id: str):
        """发送帮助信息"""
        available_models = self._get_available_models()
        if not available_models:
            await bot.send_text_message(chat_id, "❌ 当前没有可用的AI模型，请检查配置！")
            return

        # 优化帮助消息，确保清晰易懂
        help_text = "🤖 AI大模型集成插件 - 使用说明\n\n"

        # 支持的模型
        help_text += "📱 可用模型：\n"
        for model_key in available_models:
            model_config = self.models[model_key]
            features = "、".join(["对话" if "text" in model_config.supported_features else "",
                                "画图" if "image_gen" in model_config.supported_features else "",
                                "识图" if "image_recognition" in model_config.supported_features else ""]).strip("、")
            help_text += f"• {model_config.name} - 支持{features}\n"

        # 激活方式
        help_text += f"\n🚀 激活方式：\n"
        help_text += f"• 发送：ting / ai机器人 / AI机器人\n"
        help_text += f"• 群聊中：@机器人\n"

        # 基础功能
        help_text += f"\n💬 对话功能：\n"
        help_text += f"• 激活后直接发送消息即可对话\n"
        help_text += f"• 或使用：聊天/问问/AI + 内容\n"

        # 图片功能
        help_text += f"\n🎨 图片功能：\n"
        help_text += f"• 画图/生成图片 + 描述 → 文生图\n"
        help_text += f"• 引用图片回复 识图/看图 → 图片识别\n"

        # 管理功能
        help_text += f"\n⚙️ 管理功能：\n"
        help_text += f"• 清空会话 → 重置对话历史\n"
        help_text += f"• 休眠/睡觉/拜拜 → 结束会话\n"

        # 修复ModelConfig对象访问问题
        if self.default_model in self.models:
            current_model = self.models[self.default_model].name
        else:
            current_model = self.default_model
        help_text += f"\n📍 当前默认模型：{current_model}"

        logger.info(f"准备发送帮助消息，长度: {len(help_text)}")
        try:
            await bot.send_text_message(chat_id, help_text)
            logger.info("帮助消息发送成功")
        except Exception as e:
            logger.error(f"发送帮助消息失败: {str(e)}")
            # 如果还是失败，发送最简单的消息
            try:
                await bot.send_text_message(chat_id, "AI助手帮助功能正常，但消息发送遇到问题")
            except:
                logger.error("连简单消息都发送失败")
    
    @on_text_message(priority=1)  # 最高优先级，确保不被其他插件拦截
    async def handle_text_message(self, bot: WechatAPIClient, message: dict):
        """处理文本消息，艹，这个逻辑真是够复杂的！"""

        # 立即检查帮助指令，无视任何条件
        content = message.get("Content", "").strip()
        logger.info(f"🔍 检查消息内容: '{content}'")

        if content in ["AI帮助", "模型帮助", "使用说明"]:
            logger.info(f"🚀 立即处理帮助指令: {content}")
            chat_id = message.get("FromWxid", "")
            if message.get("Type", 0) == 2:  # 群聊
                chat_id = message.get("RoomWxid", "")

            logger.info(f"📤 准备发送帮助消息到: {chat_id}")

            try:
                # 先发送确认消息
                await bot.send_text_message(chat_id, f"📖 收到{content}指令！正在生成帮助信息...")
                logger.info("✅ 确认消息发送成功")

                # 再发送详细帮助
                await self._send_help_message(bot, chat_id)
                logger.info("✅ 帮助消息发送成功")

            except Exception as e:
                logger.error(f"❌ 帮助指令处理失败: {str(e)}")
                try:
                    await bot.send_text_message(chat_id, f"❌ 帮助功能异常: {str(e)}")
                except:
                    logger.error("连错误消息都发送失败")
            return False

        if not self.enable:
            logger.info("AI插件未启用，跳过处理")
            return True

        user_id = message.get("FromWxid", "")
        chat_id = message.get("FromWxid", "")
        is_group = message.get("Type", 0) == 2  # 群聊消息

        if is_group:
            chat_id = message.get("RoomWxid", "")
            user_id = message.get("SenderWxid", user_id)  # 群聊中获取发送者ID

        if not content or not user_id:
            return True

        logger.info(f"AI插件收到消息: {content} (用户: {user_id}, 聊天: {chat_id})")

        # 超级简单的帮助指令测试
        if content.strip() in ["AI帮助", "模型帮助", "使用说明"]:
            logger.info(f"🔥 直接匹配帮助指令: {content.strip()}")
            try:
                await bot.send_text_message(chat_id, f"🤖 收到帮助指令: {content.strip()}\n正在处理...")
                await self._send_help_message(bot, chat_id)
            except Exception as e:
                logger.error(f"处理帮助指令失败: {str(e)}")
                await bot.send_text_message(chat_id, f"❌ 帮助指令处理失败: {str(e)}")
            return False

        # 最高优先级：检查帮助指令，无需任何前置条件
        logger.info(f"检查帮助指令，当前内容: '{content.strip()}'")
        logger.info(f"帮助指令列表: {self.help_commands}")

        for cmd in self.help_commands:
            logger.info(f"检查指令: '{cmd}' vs '{content.strip()}'")
            if content.strip() == cmd or content.strip().startswith(cmd + " "):
                logger.info(f"✅ 检测到帮助指令: {cmd}")
                await self._send_help_message(bot, chat_id)
                return False

        logger.info("❌ 未匹配到帮助指令")
        
        # 检查是否为休眠指令
        if self._is_sleep_message(content):
            if self.session_manager.deactivate_session(user_id, chat_id):
                await bot.send_text_message(chat_id, "😴 AI助手已休眠，发送唤醒词可重新激活")
            return False
        
        # 检查是否为唤醒消息
        is_wakeup = self._is_wakeup_message(content)
        
        # 检查会话状态
        is_active = self.session_manager.is_session_active(user_id, chat_id)

        # 优先处理指令，不管会话是否活跃
        command_type, remaining_content = self._extract_command_and_content(content)
        logger.info(f"指令解析结果: type={command_type}, content={remaining_content}")

        # 帮助指令随时可用，不需要激活会话
        if command_type == "help":
            logger.info("处理帮助指令")
            await self._send_help_message(bot, chat_id)
            return False

        # 添加测试指令，确保消息发送正常
        if content.strip() == "AI测试":
            logger.info("处理AI测试指令")
            try:
                await bot.send_text_message(chat_id, "✅ AI插件测试成功！消息发送正常。")
                logger.info("AI测试消息发送成功")
            except Exception as e:
                logger.error(f"AI测试消息发送失败: {str(e)}")
            return False

        # 添加帮助测试指令
        if content.strip() == "帮助测试":
            logger.info("处理帮助测试指令")
            try:
                await bot.send_text_message(chat_id, "✅ 帮助功能测试成功！\n可用帮助指令：AI帮助、模型帮助、使用说明")
                logger.info("帮助测试消息发送成功")
            except Exception as e:
                logger.error(f"帮助测试消息发送失败: {str(e)}")
            return False

        # 如果是唤醒消息，激活会话
        if is_wakeup:
            available_models = self._get_available_models()
            if not available_models:
                await bot.send_text_message(chat_id, "❌ 当前没有可用的AI模型，请检查配置！")
                return False

            # 使用默认模型或第一个可用模型
            model_name = self.default_model if self.default_model in available_models else available_models[0]

            session = self.session_manager.activate_session(
                user_id, chat_id, model_name, self.wakeup_reply_count
            )

            model_config = self.models[model_name]
            await bot.send_text_message(
                chat_id,
                f"🤖 AI助手已激活！\n当前模型：{model_config.name}\n发送 {'/'.join(self.help_commands)} 查看使用说明"
            )
            return False

        # 如果不是唤醒消息且会话不活跃，则检查是否为其他需要会话的指令
        if not is_active:
            # 这些指令需要先激活会话
            if command_type in ["text", "image_gen", "clear_session", "switch_model"]:
                await bot.send_text_message(chat_id, "💡 请先发送唤醒词激活AI助手\n唤醒词：ting、ai机器人、AI机器人")
                return False
            # 其他指令或非指令消息直接忽略
            return True

        
        if command_type == "clear_session":
            if self.session_manager.clear_session_messages(user_id, chat_id):
                await bot.send_text_message(chat_id, "🗑️ 对话历史已清空")
            return False
        
        if command_type == "switch_model":
            if not remaining_content:
                available_models = self._get_available_models()
                model_list = "、".join([self.models[m].name for m in available_models])
                await bot.send_text_message(chat_id, f"请指定要切换的模型：{model_list}")
                return False
            
            target_model = self._get_model_by_name(remaining_content)
            if not target_model or target_model not in self._get_available_models():
                available_models = self._get_available_models()
                model_list = "、".join([self.models[m].name for m in available_models])
                await bot.send_text_message(chat_id, f"❌ 模型不存在或不可用\n可用模型：{model_list}")
                return False
            
            if self.session_manager.switch_model(user_id, chat_id, target_model):
                model_config = self.models[target_model]
                await bot.send_text_message(chat_id, f"✅ 已切换到模型：{model_config.name}")
            return False
        
        # 处理文生图
        if command_type == "image_gen":
            await self._handle_image_generation(bot, user_id, chat_id, remaining_content)
            return False

        # 图片识别和图生图需要通过引用消息触发
        if command_type == "image_recognition":
            await bot.send_text_message(chat_id, "💡 图片识别功能需要引用图片消息后回复识别指令\n例如：引用图片 -> 识图")
            return False

        if command_type == "image_to_image":
            await bot.send_text_message(chat_id, "💡 图生图功能需要引用图片消息后回复修改指令\n例如：引用图片 -> 图生图 改成卡通风格")
            return False

        # 其他功能暂时返回提示
        if command_type == "video":
            await bot.send_text_message(chat_id, f"🚧 {command_type} 功能正在开发中，敬请期待！")
            return False

        # 文本对话指令处理
        if command_type == "text":
            await self._handle_ai_chat(bot, user_id, chat_id, remaining_content)
            return False

        # 只有在会话活跃且没有匹配到任何指令时，才当作普通AI对话处理
        if command_type is None and is_active:
            await self._handle_ai_chat(bot, user_id, chat_id, content)
            return False
        
        return True
    
    async def _handle_ai_chat(self, bot: WechatAPIClient, user_id: str, chat_id: str, content: str):
        """处理AI对话，艹，这个才是核心逻辑！"""
        try:
            # 获取会话
            session = self.session_manager.get_session(user_id, chat_id)
            if not session:
                await bot.send_text_message(chat_id, "❌ 会话不存在，请先发送唤醒词激活")
                return
            
            # 检查模型是否可用
            if session.model_name not in self.models:
                await bot.send_text_message(chat_id, f"❌ 模型 {session.model_name} 不可用")
                return
            
            model_config = self.models[session.model_name]
            if not model_config.enable:
                await bot.send_text_message(chat_id, f"❌ 模型 {model_config.name} 已禁用")
                return
            
            # 添加用户消息到会话
            self.session_manager.add_message(user_id, chat_id, "user", content)
            
            # 获取对话历史
            messages = self.session_manager.get_session_messages(user_id, chat_id)
            
            # 调用AI模型
            client = model_config.client
            response = await client.create_completion(messages, stream=False)

            # 提取AI回复内容（适配豆包客户端的返回格式）
            ai_content = ""
            if response:
                if "choices" in response and response["choices"]:
                    # OpenAI格式
                    ai_content = response["choices"][0].get("message", {}).get("content", "")
                elif response.get("type") == "text":
                    # 豆包格式
                    ai_content = response.get("text", "")

            if not ai_content:
                logger.warning(f"AI模型返回空内容，响应: {response}")
                await bot.send_text_message(chat_id, "❌ AI模型返回空内容，请稍后重试")
                return
            
            # 添加AI回复到会话
            self.session_manager.add_message(user_id, chat_id, "assistant", ai_content)
            
            # 发送回复
            await bot.send_text_message(chat_id, ai_content)
            
            # 增加回复计数，检查是否需要停用会话
            is_still_active = self.session_manager.increment_reply_count(user_id, chat_id)
            if not is_still_active:
                await bot.send_text_message(chat_id, f"💤 已达到最大回复次数({self.wakeup_reply_count})，AI助手进入休眠\n发送唤醒词可重新激活")
            
        except Exception as e:
            logger.error(f"AI对话处理失败: {str(e)}")
            await bot.send_text_message(chat_id, f"❌ AI对话处理失败: {str(e)}")
    
    @on_at_message(priority=10)
    async def handle_at_message(self, bot: WechatAPIClient, message: dict):
        """处理@消息，艹，群聊@触发功能！"""
        if not self.enable or not self.enable_at_trigger:
            return True

        # 直接当作唤醒消息处理
        return await self.handle_text_message(bot, message)

    @on_quote_message(priority=10)
    async def handle_quote_message(self, bot: WechatAPIClient, message: dict):
        """处理引用消息，艹，通过引用回复来触发图片识别功能！"""
        if not self.enable:
            return True

        # 获取基本信息
        content = message.get("Content", "").strip()
        user_id = message.get("FromWxid", "")
        chat_id = message.get("FromWxid", "")
        is_group = message.get("Type", 0) == 2

        if is_group:
            chat_id = message.get("RoomWxid", "")
            user_id = message.get("SenderWxid", user_id)

        if not content or not user_id:
            return True

        # 检查会话是否活跃
        if not self.session_manager.is_session_active(user_id, chat_id):
            return True

        # 获取引用的消息信息
        quote_info = message.get("Quote", {})
        if not quote_info:
            return True

        quoted_content = quote_info.get("Content", "")
        quoted_msg_type = quote_info.get("MsgType", 0)

        # 检查引用的是否为图片消息（MsgType = 3）
        if quoted_msg_type != 3:
            return True

        # 检查是否为图片识别指令
        command_type, remaining_content = self._extract_command_and_content(content)

        if command_type == "image_recognition":
            # 图片识别功能
            await self._handle_image_recognition_from_quote(bot, user_id, chat_id, quoted_content, remaining_content)
            return False
        elif command_type == "image_to_image":
            # 图生图功能
            await self._handle_image_to_image_from_quote(bot, user_id, chat_id, quoted_content, remaining_content)
            return False
        else:
            # 检查是否直接是图片识别指令（不需要前缀）
            if any(cmd in content for cmd in self.image_recognition_commands):
                await self._handle_image_recognition_from_quote(bot, user_id, chat_id, quoted_content, content)
                return False

        return True

    @on_xml_message(priority=10)
    async def handle_xml_message(self, bot: WechatAPIClient, message: dict):
        """处理XML消息，特别是引用图片的消息，艹，这种消息格式真是够复杂的！"""
        if not self.enable:
            return True

        # 获取基本信息
        content = message.get("Content", "").strip()
        user_id = message.get("FromWxid", "")
        chat_id = message.get("FromWxid", "")
        is_group = message.get("Type", 0) == 2

        if is_group:
            chat_id = message.get("RoomWxid", "")
            user_id = message.get("SenderWxid", user_id)

        if not content or not user_id:
            return True

        # 检查会话是否活跃
        if not self.session_manager.is_session_active(user_id, chat_id):
            return True

        # 解析XML内容，查找引用消息
        try:
            import xml.etree.ElementTree as ET
            logger.info(f"开始解析XML消息，内容长度: {len(content)}")
            root = ET.fromstring(content)

            # 查找refermsg标签
            refermsg = root.find(".//refermsg")
            if refermsg is None:
                return True

            # 获取引用消息的类型和内容
            ref_type = refermsg.find("type")
            ref_content = refermsg.find("content")

            if ref_type is None or ref_content is None:
                return True

            # 检查是否引用的是图片消息（type=3）
            if ref_type.text != "3":
                return True

            # 获取用户输入的指令（从appmsg的title中获取）
            title_elem = root.find(".//title")
            if title_elem is None:
                return True

            user_command = title_elem.text.strip() if title_elem.text else ""
            logger.info(f"解析到用户指令: '{user_command}'")

            # 检查是否为图片识别指令
            logger.info(f"检查图片识别指令，支持的指令: {self.image_recognition_commands}")
            if any(cmd in user_command for cmd in self.image_recognition_commands):
                # 解析引用的图片内容
                ref_content_xml = ref_content.text
                if ref_content_xml:
                    # HTML解码
                    import html
                    decoded_xml = html.unescape(ref_content_xml)
                    logger.info(f"解码后的图片XML: {decoded_xml[:200]}...")
                    await self._handle_image_recognition_from_xml(bot, user_id, chat_id, decoded_xml, user_command, content)
                    return False

        except Exception as e:
            logger.error(f"解析XML引用消息失败: {str(e)}")
            return True

        return True

    async def _handle_image_recognition_from_xml(self, bot: WechatAPIClient, user_id: str, chat_id: str, image_xml: str, prompt: str = "", original_xml: str = ""):
        """处理从XML中解析出的图片识别请求，艹，这个解析真是够麻烦的！"""
        try:
            # 解析图片XML获取图片信息
            import xml.etree.ElementTree as ET
            img_root = ET.fromstring(image_xml)
            img_elem = img_root.find("img")

            if img_elem is None:
                await bot.send_text_message(chat_id, "❌ 无法解析引用的图片信息")
                return

            # 获取图片的CDN URL和AES密钥
            cdn_url = img_elem.get("cdnbigimgurl") or img_elem.get("cdnmidimgurl")
            aes_key = img_elem.get("aeskey")

            if not cdn_url or not aes_key:
                await bot.send_text_message(chat_id, "❌ 图片信息不完整，无法下载")
                return

            logger.info(f"解析到图片信息: CDN URL={cdn_url[:50]}..., AES Key={aes_key[:20]}...")

            # 尝试下载图片
            try:
                await bot.send_text_message(chat_id, "🔍 正在下载并识别图片，请稍候...")

                # 使用CDN下载方法
                try:
                    image_data = await bot.download_image(aes_key, cdn_url)
                    if image_data:
                        # 将图片数据转换为base64
                        import base64
                        if isinstance(image_data, str):
                            image_base64 = image_data
                        else:
                            image_base64 = base64.b64encode(image_data).decode('utf-8')

                        # 构造图片URL用于AI识别
                        image_url = f"data:image/jpeg;base64,{image_base64}"

                        # 调用图片识别处理
                        await self._handle_image_recognition_with_data(bot, user_id, chat_id, image_url, prompt)
                        return
                except Exception as download_error:
                    logger.warning(f"CDN下载失败: {download_error}")

                # 如果CDN下载失败，尝试使用消息ID下载
                # 从原始XML中获取消息ID（svrid在refermsg中，不在图片XML中）
                try:
                    # 重新解析原始XML获取svrid
                    original_root = ET.fromstring(original_xml)
                    svrid_elem = original_root.find(".//svrid")
                    if svrid_elem is not None and svrid_elem.text:
                        msg_id = svrid_elem.text
                        length = img_elem.get("length", "0")

                        logger.info(f"尝试使用消息ID下载: MsgId={msg_id}, Length={length}")

                        try:
                            image_data = await bot.get_msg_image(msg_id, user_id, int(length))
                            if image_data and len(image_data) > 100:
                                # 将图片数据转换为base64
                                import base64
                                image_base64 = base64.b64encode(image_data).decode('utf-8')
                                image_url = f"data:image/jpeg;base64,{image_base64}"

                                # 调用图片识别处理
                                await self._handle_image_recognition_with_data(bot, user_id, chat_id, image_url, prompt)
                                return
                        except Exception as msg_download_error:
                            logger.warning(f"消息图片下载失败: {msg_download_error}")
                except Exception as parse_error:
                    logger.warning(f"解析原始XML获取消息ID失败: {parse_error}")

                # 所有下载方法都失败
                await bot.send_text_message(chat_id, "❌ 图片下载失败，请重新发送图片后再试")

            except Exception as e:
                logger.error(f"图片下载处理失败: {str(e)}")
                await bot.send_text_message(chat_id, f"❌ 图片处理失败: {str(e)}")

        except Exception as e:
            logger.error(f"处理XML图片识别失败: {str(e)}")
            await bot.send_text_message(chat_id, f"❌ 图片识别失败: {str(e)}")

    async def _handle_image_recognition_with_data(self, bot: WechatAPIClient, user_id: str, chat_id: str, image_url: str, prompt: str = ""):
        """处理图片识别（使用已下载的图片数据），艹，这个功能终于能用了！"""
        try:
            # 获取会话
            session = self.session_manager.get_session(user_id, chat_id)
            if not session:
                await bot.send_text_message(chat_id, "❌ 会话不存在，请先发送唤醒词激活")
                return

            # 检查模型是否支持图片识别
            model_config = self.models.get(session.model_name)
            if not model_config or "image_recognition" not in model_config.supported_features:
                await bot.send_text_message(chat_id, f"❌ 当前模型 {model_config.name if model_config else session.model_name} 不支持图片识别")
                return

            # 构建包含图片的消息
            messages = self.session_manager.get_session_messages(user_id, chat_id)

            # 设置识别提示词
            recognition_text = prompt if prompt else "请识别这张图片"

            # 添加图片识别消息
            image_message = {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": recognition_text
                    },
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": image_url
                        }
                    }
                ]
            }
            messages.append(image_message)

            # 调用AI模型的图片识别功能
            client = model_config.client

            if hasattr(client, 'recognize_image'):
                # 使用专门的图片识别方法
                result = await client.recognize_image(image_url, recognition_text)

                if result and result.get("type") == "text":
                    ai_content = result.get("text", "")
                else:
                    ai_content = ""
            else:
                # 回退到通用对话接口
                response = await client.create_completion(messages, stream=False)

                # 提取AI回复内容（适配豆包客户端的返回格式）
                ai_content = ""
                if response:
                    if "choices" in response and response["choices"]:
                        # OpenAI格式
                        ai_content = response["choices"][0].get("message", {}).get("content", "")
                    elif response.get("type") == "text":
                        # 豆包格式
                        ai_content = response.get("text", "")

            if not ai_content:
                logger.warning(f"AI模型返回空内容，响应: {response}")
                await bot.send_text_message(chat_id, "❌ 图片识别失败，请稍后重试")
                return

            # 发送识别结果
            await bot.send_text_message(chat_id, f"🔍 图片识别结果：\n{ai_content}")

            # 添加到会话历史
            self.session_manager.add_message(user_id, chat_id, "user", f"[图片识别] {recognition_text}")
            self.session_manager.add_message(user_id, chat_id, "assistant", ai_content)

            # 增加回复计数
            is_still_active = self.session_manager.increment_reply_count(user_id, chat_id)
            if not is_still_active:
                await bot.send_text_message(chat_id, f"💤 已达到最大回复次数({self.wakeup_reply_count})，AI助手进入休眠\n发送唤醒词可重新激活")

        except Exception as e:
            logger.error(f"图片识别处理失败: {str(e)}")

            # 根据错误类型提供更友好的提示
            error_msg = str(e)
            if "cacheSize do not equal totalLen" in error_msg or "ret: -104" in error_msg:
                await bot.send_text_message(chat_id, "❌ 图片识别失败：图片缓存已过期\n💡 请重新发送图片后再试")
            elif "404" in error_msg or "CDN下载功能不可用" in error_msg:
                await bot.send_text_message(chat_id, "❌ 图片识别失败：图片下载功能暂不可用\n💡 请稍后重试或联系管理员")
            elif "无效的图片链接" in error_msg:
                await bot.send_text_message(chat_id, "❌ 图片识别失败：无效的图片链接\n💡 请确保引用的是图片消息")
            else:
                await bot.send_text_message(chat_id, f"❌ 图片识别失败: {error_msg}")

    async def _handle_image_recognition_from_quote(self, bot: WechatAPIClient, user_id: str, chat_id: str, image_url: str, prompt: str = ""):
        """处理引用图片的识别，艹，这个功能挺实用的！"""
        try:
            # 获取会话
            session = self.session_manager.get_session(user_id, chat_id)
            if not session:
                await bot.send_text_message(chat_id, "❌ 会话不存在，请先发送唤醒词激活")
                return

            # 检查模型是否支持图片识别
            model_config = self.models.get(session.model_name)
            if not model_config or "image_recognition" not in model_config.supported_features:
                await bot.send_text_message(chat_id, f"❌ 当前模型 {model_config.name if model_config else session.model_name} 不支持图片识别")
                return

            # 检查图片URL是否有效
            if not image_url or not image_url.startswith(('http://', 'https://')):
                await bot.send_text_message(chat_id, "❌ 无效的图片链接")
                return

            await bot.send_text_message(chat_id, "🔍 正在识别图片，请稍候...")

            # 构建包含图片的消息
            messages = self.session_manager.get_session_messages(user_id, chat_id)

            # 根据用户的提示词构建识别请求
            recognition_text = "请识别这张图片的内容"
            if prompt:
                # 移除指令词，保留用户的具体要求
                cleaned_prompt = prompt
                for cmd in self.image_recognition_commands:
                    if cmd in cleaned_prompt:
                        cleaned_prompt = cleaned_prompt.replace(cmd, "").strip()

                if cleaned_prompt:
                    recognition_text = f"请识别这张图片的内容，重点关注：{cleaned_prompt}"

            # 添加图片识别消息
            image_message = {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": recognition_text
                    },
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": image_url
                        }
                    }
                ]
            }

            messages.append(image_message)

            # 调用AI模型的图片识别功能
            client = model_config.client

            if hasattr(client, 'recognize_image'):
                # 使用专门的图片识别方法
                result = await client.recognize_image(image_url, recognition_text)

                if result and result.get("type") == "text":
                    ai_content = result.get("text", "")
                else:
                    ai_content = ""
            else:
                # 回退到通用对话接口
                response = await client.create_completion(messages, stream=False)

                # 提取AI回复内容
                ai_content = ""
                if response and "choices" in response and response["choices"]:
                    ai_content = response["choices"][0].get("message", {}).get("content", "")
                elif response and response.get("type") == "text":
                    ai_content = response.get("text", "")

            if not ai_content:
                await bot.send_text_message(chat_id, "❌ 图片识别失败，请稍后重试")
                return

            # 添加消息到会话历史
            self.session_manager.add_message(user_id, chat_id, "user", f"[图片识别] {recognition_text}")
            self.session_manager.add_message(user_id, chat_id, "assistant", ai_content)

            # 发送回复
            await bot.send_text_message(chat_id, f"🖼️ 图片识别结果：\n{ai_content}")

            # 增加回复计数
            is_still_active = self.session_manager.increment_reply_count(user_id, chat_id)
            if not is_still_active:
                await bot.send_text_message(chat_id, f"💤 已达到最大回复次数({self.wakeup_reply_count})，AI助手进入休眠\n发送唤醒词可重新激活")

        except Exception as e:
            logger.error(f"图片识别处理失败: {str(e)}")

            # 根据错误类型提供更友好的提示
            error_msg = str(e)
            if "cacheSize do not equal totalLen" in error_msg or "ret: -104" in error_msg:
                await bot.send_text_message(chat_id, "❌ 图片识别失败：图片缓存已过期\n💡 请重新发送图片后再试")
            elif "404" in error_msg or "CDN下载功能不可用" in error_msg:
                await bot.send_text_message(chat_id, "❌ 图片识别失败：图片下载功能暂不可用\n💡 请稍后重试或联系管理员")
            elif "无效的图片链接" in error_msg:
                await bot.send_text_message(chat_id, "❌ 图片识别失败：无效的图片链接\n💡 请确保引用的是图片消息")
            else:
                await bot.send_text_message(chat_id, f"❌ 图片识别失败: {error_msg}")

    async def _handle_image_to_image_from_quote(self, bot: WechatAPIClient, user_id: str, chat_id: str, image_url: str, prompt: str):
        """处理引用图片的图生图，艹，这个功能更复杂！"""
        try:
            # 获取会话
            session = self.session_manager.get_session(user_id, chat_id)
            if not session:
                await bot.send_text_message(chat_id, "❌ 会话不存在，请先发送唤醒词激活")
                return

            # 检查模型是否支持图生图
            model_config = self.models.get(session.model_name)
            if not model_config or "image_to_image" not in model_config.supported_features:
                await bot.send_text_message(chat_id, f"❌ 当前模型 {model_config.name if model_config else session.model_name} 不支持图生图")
                return

            # 检查图片URL是否有效
            if not image_url or not image_url.startswith(('http://', 'https://')):
                await bot.send_text_message(chat_id, "❌ 无效的图片链接")
                return

            # 提取修改描述
            modification_prompt = prompt
            for cmd in self.image_to_image_commands:
                if cmd in modification_prompt:
                    modification_prompt = modification_prompt.replace(cmd, "").strip()

            if not modification_prompt:
                await bot.send_text_message(chat_id, "❌ 请提供图片修改描述")
                return

            await bot.send_text_message(chat_id, "🎨 正在处理图生图请求，请稍候...")

            # TODO: 实现图生图功能
            # 这里需要根据不同模型的API来实现
            # 目前先返回提示信息
            await bot.send_text_message(chat_id, f"🚧 图生图功能正在开发中，敬请期待！\n您的修改要求：{modification_prompt}")

        except Exception as e:
            logger.error(f"图生图处理失败: {str(e)}")
            await bot.send_text_message(chat_id, f"❌ 图生图处理失败: {str(e)}")

    async def _handle_image_generation(self, bot: WechatAPIClient, user_id: str, chat_id: str, prompt: str):
        """处理文生图，艹，这个功能用户最喜欢了！"""
        try:
            # 获取会话
            session = self.session_manager.get_session(user_id, chat_id)
            if not session:
                await bot.send_text_message(chat_id, "❌ 会话不存在，请先发送唤醒词激活")
                return

            # 检查模型是否支持文生图
            model_config = self.models.get(session.model_name)
            if not model_config or "image_gen" not in model_config.supported_features:
                await bot.send_text_message(chat_id, f"❌ 当前模型 {model_config.name if model_config else session.model_name} 不支持文生图")
                return

            if not prompt:
                await bot.send_text_message(chat_id, "❌ 请提供图片生成描述")
                return

            await bot.send_text_message(chat_id, "🎨 正在生成图片，请稍候...")

            # 调用模型的图片生成功能
            client = model_config.client

            # 豆包模型支持图片生成
            if session.model_name == "doubao" and hasattr(client, 'generate_image'):
                result = await client.generate_image(prompt)

                # 处理生成结果
                if result:
                    if result.get("type") == "image" and "data" in result:
                        image_urls = result.get("data", [])
                        if image_urls:
                            # 发送生成的图片
                            for i, img_url in enumerate(image_urls[:3]):  # 最多发送3张
                                await bot.send_image_message(chat_id, img_url)

                            # 添加到会话历史
                            self.session_manager.add_message(user_id, chat_id, "user", f"[文生图] {prompt}")
                            self.session_manager.add_message(user_id, chat_id, "assistant", f"已生成{len(image_urls)}张图片")

                            # 增加回复计数
                            is_still_active = self.session_manager.increment_reply_count(user_id, chat_id)
                            if not is_still_active:
                                await bot.send_text_message(chat_id, f"💤 已达到最大回复次数({self.wakeup_reply_count})，AI助手进入休眠\n发送唤醒词可重新激活")
                        else:
                            await bot.send_text_message(chat_id, "❌ 图片生成失败，未返回图片URL")
                    elif result.get("type") == "text":
                        # 如果返回的是文本（可能包含解释），直接发送
                        text = result.get("text", "")
                        await bot.send_text_message(chat_id, f"🎨 {text}")

                        # 添加到会话历史
                        self.session_manager.add_message(user_id, chat_id, "user", f"[文生图] {prompt}")
                        self.session_manager.add_message(user_id, chat_id, "assistant", text)

                        # 增加回复计数
                        is_still_active = self.session_manager.increment_reply_count(user_id, chat_id)
                        if not is_still_active:
                            await bot.send_text_message(chat_id, f"💤 已达到最大回复次数({self.wakeup_reply_count})，AI助手进入休眠\n发送唤醒词可重新激活")
                    else:
                        await bot.send_text_message(chat_id, "❌ 图片生成失败，请稍后重试")
                else:
                    await bot.send_text_message(chat_id, "❌ 图片生成失败，请稍后重试")
            else:
                await bot.send_text_message(chat_id, "🚧 当前模型不支持文生图功能！")

        except Exception as e:
            logger.error(f"文生图处理失败: {str(e)}")
            await bot.send_text_message(chat_id, f"❌ 文生图处理失败: {str(e)}")
