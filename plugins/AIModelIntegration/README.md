# AI大模型集成插件

老王出品，必属精品！这个插件集成了豆包AI大模型，支持多种AI功能。

## 功能特性

### 🤖 支持的AI模型
- **豆包** - 字节跳动AI模型，支持文本对话、图片生成、图片识别

### 🎯 核心功能
- **文生文** - 智能对话，支持多轮上下文
- **文生图** - 根据文字描述生成图片
- **图片识别** - 识别图片内容并描述

### 💬 交互方式
- **@触发** - 群聊中@机器人激活对话
- **唤醒词触发** - 发送"ting"、"ai机器人"、"AI机器人"激活
- **会话管理** - 激活后连续对话，支持自定义回复次数
- **模型切换** - 用户可自由切换不同AI模型

## 安装配置

### 1. 获取API凭证

#### 豆包模型配置
1. 打开浏览器，访问 https://www.doubao.com
2. 登录账号后，按F12打开开发者工具
3. 在Application/Storage -> Cookies中复制完整的cookie字符串
4. 将cookie字符串填入配置文件的`cookies`字段

### 2. 修改配置文件

编辑 `plugins/AIModelIntegration/config.toml`：

```toml
[AIModelIntegration]
enable = true

[AIModelIntegration.basic]
# 是否启用@触发（只适用于群聊）
enable_at_trigger = true
# 是否启用唤醒词触发
enable_wakeup_trigger = true
# 唤醒后回复的次数（默认3次）
wakeup_reply_count = 3
# 唤醒词列表
wakeup_words = ["ting", "ai机器人", "AI机器人"]
# 立即休眠指令
sleep_commands = ["休眠", "睡觉", "拜拜"]

# 豆包模型配置
[AIModelIntegration.models.doubao]
enable = true
name = "豆包"
cookies = "sessionid=你的豆包sessionid值"  # 从cookie中获取完整的cookie字符串
assistant_id = "497858"
supported_features = ["text", "image_gen", "image_recognition"]
price = 1

# 默认模型
default_model = "doubao"
```

## 使用说明

### 基础对话

#### 激活AI助手
```
# 群聊中@机器人
@机器人

# 或发送唤醒词
ting
ai机器人
AI机器人
```

#### 文本对话
```
# 激活后直接发送消息
你好，今天天气怎么样？

# 或使用指令
聊天 帮我写一首诗
问问 Python怎么学习
AI 解释一下量子计算
```

### 图片功能

#### 文生图
```
画图 一只可爱的小猫咪
生成图片 夕阳下的海滩
文生图 科幻风格的城市
```

#### 图片识别
```
# 引用回复图片消息
引用图片 -> 识图
引用图片 -> 看图
引用图片 -> 图片识别
引用图片 -> 图像识别
引用图片 -> 识别图片
引用图片 -> 分析图片
引用图片 -> 这是什么
```

#### 图生图
```
# 引用回复图片消息并提供修改描述
引用图片 -> 图生图 把这张图片改成卡通风格
引用图片 -> 修改图片 添加彩虹背景
```

### 管理功能

#### 切换模型
```
换模型 豆包
使用模型 doubao
```

#### 清空会话
```
清空会话
重置对话
新对话
```

#### 休眠助手
```
休眠
睡觉
拜拜
```

#### 查看帮助
```
AI帮助
模型帮助
使用说明
```

## 会话机制

### 激活状态
- 发送唤醒词或@机器人后，AI助手进入激活状态
- 激活后无需重复@或发送唤醒词，直接对话即可
- 默认连续回复3次后自动休眠（可配置）

### 会话管理
- 每个用户在每个群聊/私聊中都有独立的会话
- 会话包含对话历史，支持上下文理解
- 超过最大消息数量时自动清理旧消息
- 会话超时后自动清理（默认60分钟）

### 模型切换
- 用户可在对话中随时切换AI模型
- 切换后保留对话历史
- 不同模型支持的功能可能不同

## 技术特性

### 异步处理
- 所有AI请求都是异步处理，不阻塞其他功能
- 支持并发处理多个用户的请求

### 错误处理
- 完善的错误处理和重试机制
- API失败时自动重试，超过次数后返回错误信息

### 内存管理
- 智能的会话清理机制，避免内存泄漏
- 定期清理过期会话和消息

### 安全性
- Token状态检查，确保API凭证有效
- 请求参数验证，防止恶意输入

## 故障排除

### 常见问题

#### 1. AI模型不响应
- 检查配置文件中的token是否正确
- 确认模型是否启用（enable = true）
- 查看日志中的错误信息

#### 2. 图片功能不可用
- 确认当前模型是否支持图片功能
- 检查图片URL是否可访问
- 查看supported_features配置

#### 3. 会话状态异常
- 发送"清空会话"重置对话历史
- 发送"休眠"后重新激活
- 重启机器人程序

### 日志查看
插件会输出详细的日志信息，包括：
- 会话创建和管理
- API请求和响应
- 错误信息和堆栈跟踪

## 开发说明

### 文件结构
```
plugins/AIModelIntegration/
├── __init__.py          # 插件初始化
├── main.py              # 主逻辑文件
├── config.toml          # 配置文件
├── doubao_client.py     # 豆包API客户端
├── session_manager.py   # 会话管理器
└── README.md           # 说明文档
```

### 扩展新模型
1. 创建新的客户端类（参考doubao_client.py）
2. 在config.toml中添加模型配置
3. 在main.py中初始化新模型
4. 实现对应的API调用方法

### 添加新功能
1. 在commands配置中添加新指令
2. 在_extract_command_and_content方法中处理新指令
3. 实现对应的处理方法
4. 更新帮助信息

## 版本历史

### v2.0.0
- 重构版本发布
- 移除失效的千问模型支持
- 重新实现豆包模型功能
- 支持豆包文本对话、图片生成、图片识别
- 优化API调用和错误处理
- 更新配置格式为cookies方式

## 许可证

本插件由老王开发，遵循项目整体许可证。

## 联系方式

如有问题或建议，请联系老王。

---

**注意：使用本插件需要有效的AI模型API凭证，请确保遵守相关服务条款。**
