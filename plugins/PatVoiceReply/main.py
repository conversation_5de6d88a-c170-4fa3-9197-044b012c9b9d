import os
import re
import time
import random
import tomllib
import hashlib
import aiohttp
from pathlib import Path
from typing import Optional

from loguru import logger
from WechatAPI import WechatAPIClient
from utils.decorators import on_pat_message, on_text_message
from utils.plugin_base import PluginBase


class PatVoiceReply(PluginBase):
    description = "拍一拍语音回复插件 - 当收到拍一拍消息时随机回复语音（文字转语音）"
    author = "老王"
    version = "2.0.0"

    def __init__(self):
        super().__init__()

        # 获取配置文件路径
        config_path = os.path.join(os.path.dirname(__file__), "config.toml")

        try:
            with open(config_path, "rb") as f:
                config = tomllib.load(f)

            # 读取基本配置
            basic_config = config.get("basic", {})
            self.enable = basic_config.get("enable", False)

            # 读取设置配置
            settings_config = config.get("settings", {})
            self.cooldown = settings_config.get("cooldown", 30)
            self.word_file = settings_config.get("word_file", "word.txt")
            self.tts_api_url = settings_config.get("tts_api_url", "http://www.yx520.ltd/API/wzzyy/api.php")
            self.voice_id_min = settings_config.get("voice_id_min", 1)
            self.voice_id_max = settings_config.get("voice_id_max", 541)
            self.fallback_voice_file = settings_config.get("fallback_voice_file", "voice/test.mp3")
            self.cache_dir = settings_config.get("cache_dir", "cache")
            self.max_cache_size = settings_config.get("max_cache_size", 50)

            # 构建词库文件路径
            self.word_file_path = os.path.join(os.path.dirname(__file__), self.word_file)

            # 构建备选语音文件路径
            self.fallback_voice_path = os.path.join(os.path.dirname(__file__), self.fallback_voice_file)

            # 构建缓存目录路径
            self.cache_path = os.path.join(os.path.dirname(__file__), self.cache_dir)
            os.makedirs(self.cache_path, exist_ok=True)

            # 用户冷却时间记录
            self.user_last_request = {}

            # 机器人wxid将在运行时获取
            self.bot_wxid = None

            logger.info("PatVoiceReply插件初始化完成（文字转语音版本）")

        except Exception as e:
            logger.error(f"加载PatVoiceReply配置文件失败: {str(e)}")
            self.enable = False

    async def async_init(self):
        """异步初始化"""
        if self.enable:
            # 检查词库文件
            if not os.path.exists(self.word_file_path):
                logger.warning(f"词库文件不存在: {self.word_file_path}")
                self.enable = False
                return

            # 加载词库内容
            word_list = self._load_word_list()
            if not word_list:
                logger.warning(f"词库文件为空或加载失败: {self.word_file_path}")
                self.enable = False
                return

            logger.info(f"PatVoiceReply加载了 {len(word_list)} 条词库内容")

    def _load_word_list(self) -> list:
        """加载词库文件，返回词库内容列表"""
        word_list = []

        if not os.path.exists(self.word_file_path):
            return word_list

        try:
            with open(self.word_file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line:  # 跳过空行
                        word_list.append(line)

            logger.debug(f"成功加载词库，共 {len(word_list)} 条内容")
            return word_list

        except Exception as e:
            logger.error(f"加载词库文件失败: {e}")
            return []

    def _extract_patted_wxid(self, message: dict) -> Optional[str]:
        """从拍一拍消息中提取被拍者的wxid"""
        try:
            content = message.get("Content", "")
            
            # 解析XML格式的拍一拍消息
            # 提取 pattedusername 标签中的内容
            pattern = r'<pattedusername>(.*?)</pattedusername>'
            match = re.search(pattern, content)
            
            if match:
                patted_wxid = match.group(1).strip()
                logger.debug(f"解析到被拍者: {patted_wxid}")
                return patted_wxid
            
            # 备用方法：如果XML解析失败，尝试其他方式
            if self.bot_wxid and self.bot_wxid in content:
                logger.debug(f"在消息内容中找到机器人用户: {self.bot_wxid}")
                return self.bot_wxid
            
            logger.debug("无法从消息中解析被拍者")
            return None
            
        except Exception as e:
            logger.error(f"解析被拍者信息异常: {e}")
            return None

    def _check_user_limit(self, from_wxid: str, sender_wxid: str) -> float:
        """检查用户冷却时间"""
        user_key = f"{from_wxid}_{sender_wxid}"
        last_request = self.user_last_request.get(user_key, 0)
        current_time = time.time()
        elapsed = current_time - last_request
        self.user_last_request[user_key] = current_time
        
        if elapsed < self.cooldown:
            return self.cooldown - elapsed
        
        return 0

    async def _call_tts_api(self, text: str, voice_id: int) -> str:
        """调用文字转语音API"""
        try:
            params = {
                'text': text,
                'voice': voice_id
            }

            async with aiohttp.ClientSession() as session:
                async with session.get(self.tts_api_url, params=params, timeout=30) as response:
                    if response.status == 200:
                        result = await response.json()
                        if result.get("code") == "200":
                            return result.get("url")
                        else:
                            logger.error(f"TTS API返回错误: {result}")
                            return None
                    else:
                        logger.error(f"TTS API请求失败: HTTP {response.status}")
                        return None

        except Exception as e:
            logger.error(f"调用TTS API失败: {e}")
            return None

    async def _download_audio(self, url: str) -> bytes:
        """下载音频文件"""
        try:
            # 生成缓存文件名
            url_hash = hashlib.md5(url.encode()).hexdigest()
            cache_file = os.path.join(self.cache_path, f"{url_hash}.mp3")

            # 检查缓存
            if os.path.exists(cache_file):
                with open(cache_file, "rb") as f:
                    logger.debug(f"使用缓存音频文件: {cache_file}")
                    return f.read()

            # 下载音频
            async with aiohttp.ClientSession() as session:
                async with session.get(url, timeout=30) as response:
                    if response.status == 200:
                        audio_data = await response.read()

                        # 保存到缓存
                        with open(cache_file, "wb") as f:
                            f.write(audio_data)

                        # 清理旧缓存
                        await self._cleanup_cache()

                        logger.debug(f"音频下载成功: {len(audio_data)} 字节")
                        return audio_data
                    else:
                        logger.error(f"音频下载失败: HTTP {response.status}")
                        return None

        except Exception as e:
            logger.error(f"下载音频失败: {e}")
            return None

    async def _cleanup_cache(self):
        """清理缓存文件"""
        try:
            cache_files = []
            for filename in os.listdir(self.cache_path):
                if filename.endswith('.mp3'):
                    filepath = os.path.join(self.cache_path, filename)
                    cache_files.append((filepath, os.path.getmtime(filepath)))

            # 按修改时间排序，删除最旧的文件
            if len(cache_files) > self.max_cache_size:
                cache_files.sort(key=lambda x: x[1])
                files_to_delete = cache_files[:-self.max_cache_size]

                for filepath, _ in files_to_delete:
                    try:
                        os.remove(filepath)
                        logger.debug(f"删除缓存文件: {filepath}")
                    except Exception as e:
                        logger.warning(f"删除缓存文件失败: {e}")

        except Exception as e:
            logger.warning(f"清理缓存失败: {e}")

    async def _send_fallback_voice(self, bot: WechatAPIClient, to_wxid: str) -> bool:
        """发送备选语音文件"""
        try:
            if not os.path.exists(self.fallback_voice_path):
                logger.error(f"备选语音文件不存在: {self.fallback_voice_path}")
                return False

            # 读取备选语音文件
            with open(self.fallback_voice_path, "rb") as f:
                voice_data = f.read()

            logger.info(f"使用备选语音文件: {self.fallback_voice_path}")

            # 发送语音消息
            result = await bot.send_voice_message(to_wxid, voice=voice_data, format="mp3")
            if result:
                logger.info("备选语音发送成功")
                return True
            else:
                logger.error("备选语音发送失败")
                return False

        except Exception as e:
            logger.error(f"发送备选语音异常: {e}")
            return False

    async def _send_random_voice(self, bot: WechatAPIClient, to_wxid: str) -> bool:
        """发送随机语音回复（文字转语音）"""
        try:
            # 获取词库内容
            word_list = self._load_word_list()
            if not word_list:
                logger.error("没有找到可用的词库内容")
                await bot.send_text_message(to_wxid, "🎵 收到拍一拍~")
                return False

            # 随机选择一句话
            selected_text = random.choice(word_list)

            # 随机选择音色ID
            voice_id = random.randint(self.voice_id_min, self.voice_id_max)

            logger.info(f"PatVoiceReply选择文本: {selected_text[:20]}{'...' if len(selected_text) > 20 else ''}, 音色ID: {voice_id}")

            # 检查文本长度
            if len(selected_text) > 500:
                logger.warning(f"选中的文本过长({len(selected_text)}字符)，截取前500字符")
                selected_text = selected_text[:500]

            # 尝试TTS转换
            tts_success = False
            try:
                # 调用TTS API
                audio_url = await self._call_tts_api(selected_text, voice_id)
                if audio_url:
                    # 下载音频
                    audio_data = await self._download_audio(audio_url)
                    if audio_data:
                        # 发送语音消息
                        result = await bot.send_voice_message(to_wxid, voice=audio_data, format="mp3")
                        if result:
                            logger.info(f"PatVoiceReply TTS语音发送成功，文本: {selected_text}, 音色: {voice_id}")
                            return True
                        else:
                            logger.warning("TTS语音发送失败")
                    else:
                        logger.error("TTS音频下载失败")
                else:
                    logger.error("TTS API调用失败")
            except Exception as tts_error:
                logger.error(f"TTS转换过程异常: {tts_error}")

            # TTS失败，尝试备选语音文件
            logger.warning("TTS转换失败，尝试发送备选语音文件")
            if await self._send_fallback_voice(bot, to_wxid):
                return True

            # 备选语音也失败，发送文本回复
            logger.warning("所有语音发送方式都失败，发送文本回复")
            await bot.send_text_message(to_wxid, "🎵 收到拍一拍~")
            return False

        except Exception as e:
            logger.error(f"发送语音回复异常: {e}")
            await bot.send_text_message(to_wxid, "🎵 收到拍一拍~")
            return False

    @on_text_message(priority=99)
    async def handle_test_command(self, bot: WechatAPIClient, message: dict):
        """处理测试命令"""
        if not self.enable:
            return True

        content = message.get("Content", "").strip()
        from_wxid = message.get("FromWxid", "")

        # 测试拍一拍语音回复
        if content == "测试拍一拍语音":
            word_list = self._load_word_list()
            if not word_list:
                await bot.send_text_message(from_wxid, "❌ 没有找到可用的词库内容")
                return False

            await bot.send_text_message(from_wxid, f"🎵 找到 {len(word_list)} 条词库内容，正在发送测试语音...")
            await self._send_random_voice(bot, from_wxid)
            return False

        # 详细测试语音发送
        if content == "详细测试拍一拍语音":
            word_list = self._load_word_list()
            if not word_list:
                await bot.send_text_message(from_wxid, "❌ 没有找到可用的词库内容")
                return False

            selected_text = random.choice(word_list)
            test_voice_id = random.randint(self.voice_id_min, self.voice_id_max)

            debug_info = (
                f"🔍 语音转换调试信息:\n"
                f"词库文件: {self.word_file_path}\n"
                f"词库条数: {len(word_list)}\n"
                f"选中文本: {selected_text[:50]}{'...' if len(selected_text) > 50 else ''}\n"
                f"文本长度: {len(selected_text)} 字符\n"
                f"TTS API: {self.tts_api_url}\n"
                f"音色范围: {self.voice_id_min}-{self.voice_id_max}\n"
                f"测试音色ID: {test_voice_id}\n"
                f"备选语音: {self.fallback_voice_path}\n"
                f"备选文件存在: {'是' if os.path.exists(self.fallback_voice_path) else '否'}\n"
                f"机器人wxid: {self.bot_wxid or '未获取'}"
            )

            await bot.send_text_message(from_wxid, debug_info)

            # 尝试发送语音
            await bot.send_text_message(from_wxid, "正在尝试转换并发送语音...")
            success = await self._send_random_voice(bot, from_wxid)

            if success:
                await bot.send_text_message(from_wxid, "✅ 语音转换发送成功！")
            else:
                await bot.send_text_message(from_wxid, "❌ 语音转换发送失败，请查看日志")

            return False

        # 测试备选语音文件
        if content == "测试备选语音":
            if not os.path.exists(self.fallback_voice_path):
                await bot.send_text_message(from_wxid, f"❌ 备选语音文件不存在: {self.fallback_voice_path}")
                return False

            file_size = os.path.getsize(self.fallback_voice_path)
            await bot.send_text_message(from_wxid, f"🎵 备选语音文件存在，大小: {file_size} 字节，正在发送...")

            success = await self._send_fallback_voice(bot, from_wxid)
            if success:
                await bot.send_text_message(from_wxid, "✅ 备选语音发送成功！")
            else:
                await bot.send_text_message(from_wxid, "❌ 备选语音发送失败，请查看日志")

            return False

        return True

    @on_pat_message(priority=10)
    async def handle_pat(self, bot: WechatAPIClient, message: dict):
        """处理拍一拍消息"""
        if not self.enable:
            return True

        # 获取机器人wxid（如果还没有获取）
        if not self.bot_wxid:
            self.bot_wxid = bot.wxid
            logger.info(f"PatVoiceReply获取到机器人wxid: {self.bot_wxid}")

        logger.debug(f"收到拍一拍消息，被拍者检查中...")

        try:
            # 解析被拍者信息
            patted_wxid = self._extract_patted_wxid(message)
            if not patted_wxid:
                return True

            # 检查被拍者是否是机器人自己
            if patted_wxid != self.bot_wxid:
                return True  # 不是拍机器人，静默返回

            from_wxid = message.get("FromWxid", "")
            sender_wxid = message.get("ActualUserWxid", from_wxid)

            # 限流检查
            wait_time = self._check_user_limit(from_wxid, sender_wxid)
            if wait_time > 0:
                return True  # 冷却期间静默返回

            # 发送随机语音回复
            await self._send_random_voice(bot, from_wxid)
            return False

        except Exception as e:
            logger.error(f"处理拍一拍消息异常: {e}")
            return True
