# 拍一拍语音回复插件

## 功能描述

当机器人被拍一拍时，插件会随机选择一个语音文件进行回复。

## 主要特性

- 🎵 **随机语音回复**：从voice文件夹中随机选择mp3/wav/amr格式的音频文件回复
- 🎯 **精准触发**：只有机器人被拍一拍时才会触发回复
- ⏰ **冷却机制**：防止频繁触发，可配置冷却时间，冷却期间静默处理
- 🔧 **灵活配置**：支持自定义冷却时间等参数
- 📁 **多格式支持**：支持mp3、wav、amr等多种音频格式

## 配置说明

### config.toml

```toml
[basic]
# 是否启用插件
enable = true

[settings]
# 冷却时间（秒）
cooldown = 3

# 语音文件目录（相对于插件目录）
voice_dir = "voice"

# 支持的音频格式
supported_formats = ["mp3", "wav", "amr"]
```

### 配置项说明

- **enable**: 插件开关，设置为false可禁用插件
- **cooldown**: 冷却时间（秒），防止同一用户频繁触发
- **voice_dir**: 语音文件存放目录，相对于插件目录的路径
- **supported_formats**: 支持的音频文件格式列表

## 使用方法

1. **安装插件**：将插件文件放置在 `plugins/PatVoiceReply/` 目录下
2. **添加语音文件**：将mp3、wav或amr格式的语音文件放入 `voice/` 目录
3. **启用插件**：确保 `config.toml` 中 `enable = true`
4. **重启机器人**：重启机器人使插件生效

### 测试命令

- **测试拍一拍语音**：发送 `测试拍一拍语音` 可以测试语音文件是否正常工作
- **详细测试拍一拍语音**：发送 `详细测试拍一拍语音` 可以查看详细的调试信息

## 语音文件管理

### 添加语音文件

1. 将音频文件复制到 `plugins/PatVoiceReply/voice/` 目录
2. 支持的格式：mp3、wav、amr
3. 插件会自动扫描该目录下的所有支持格式的音频文件
4. 重启插件或机器人后生效

### 文件命名

- 文件名建议使用简单的英文字母和数字
- 避免使用特殊字符、中文或过长的文件名
- 推荐格式：`voice1.mp3`、`hello.wav`、`reply.amr`
- 避免使用包含GUID或特殊符号的文件名

## 工作原理

1. **消息监听**：使用 `@on_pat_message` 装饰器监听拍一拍消息
2. **目标识别**：解析拍一拍消息的XML内容，提取被拍者的wxid
3. **条件判断**：检查被拍者是否为机器人自己
4. **冷却检查**：验证用户是否在冷却时间内，冷却期间静默处理
5. **随机选择**：从voice目录随机选择一个音频文件
6. **语音发送**：调用微信API发送语音消息

## 注意事项

- 确保voice目录中至少有一个支持格式的音频文件
- 音频文件不宜过大，建议控制在3MB以内
- 插件会自动获取机器人的wxid，无需手动配置
- 冷却时间建议设置为3秒以上，避免刷屏

## 故障排除

### 插件不工作

1. 检查 `config.toml` 中 `enable` 是否为 `true`
2. 检查voice目录是否存在且包含音频文件
3. 确认机器人已正常登录
4. 查看日志输出是否有错误信息

### 语音发送失败

1. 检查音频文件格式是否支持
2. 确认音频文件没有损坏
3. 检查文件大小是否过大（建议<3MB）
4. 检查文件名是否包含特殊字符
5. 尝试重命名文件为简单的英文名称
6. 使用 `详细测试拍一拍语音` 命令查看详细错误信息

## 版本历史

### v1.0.2
- ✅ 优化日志输出：减少冗余日志，避免输出过长信息
- ✅ 改进日志级别：调试信息使用debug级别
- ✅ 简化错误提示：静默处理非目标情况

### v1.0.1
- ✅ 修复语音发送问题：改用字节数据发送而非文件路径
- ✅ 参考YujieSajiao插件的语音发送方式
- ✅ 增强错误处理和调试功能

### v1.0.0
- ✅ 初始版本发布
- ✅ 支持拍一拍消息监听
- ✅ 随机语音文件选择
- ✅ 冷却机制实现
- ✅ 多音频格式支持
- ✅ 完整的配置系统

## 作者

老王 - 专业代码修复师，键盘敲击专家

## 许可证

本插件遵循项目主许可证
