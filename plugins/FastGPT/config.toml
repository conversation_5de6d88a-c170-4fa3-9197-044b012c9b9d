[FastGPT]
enable = true
# FastGPT API配置
api-key = "" # 替换为你的API密钥
base-url = "https://api.fastgpt.in/api" # FastGPT API基础URL
app-id = "" # 替换为您的FastGPT应用ID

# 命令配置
commands = ["FastGPT", "fastgpt", "知识库"] # 触发插件的命令
command-tip = """-----FastGPT-----
💬知识库问答指令：
@机器人 知识库 你的问题
例如：@机器人 知识库 什么是FastGPT?
"""

# 功能配置
detail = false # 是否返回详细信息
max-tokens = 2000 # 最大Token数
http-proxy = "" # HTTP代理设置，如果需要

# 积分系统
price = 0 # 每次使用消耗的积分
admin_ignore = true # 管理员是否免费使用
whitelist_ignore = true # 白名单用户是否免费使用