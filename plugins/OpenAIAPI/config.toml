[OpenAIAPI]
enable = true                           # 是否启用此功能
api-key = ""                            # 后端API密钥，如果后端需要
base-url = "https://api.openai.com/v1"  # 后端API地址，可以是OpenAI或兼容的API服务

# 模型配置
default-model = "gpt-3.5-turbo"         # 默认使用的模型
available-models = [                    # 可用模型列表
    "gpt-3.5-turbo",
    "gpt-4",
    "gpt-4-turbo"
]

# 插件配置
port = 8100                             # API服务端口
host = "0.0.0.0"                        # API服务主机
command-tip = """-----OpenAI API-----
💬OpenAI API服务已启动
访问地址: http://你的IP:8100
支持标准OpenAI API格式
可用于接入各种支持OpenAI API的应用
"""

# 积分系统
price = 0                               # 每次使用扣除的积分，0表示不扣除
admin_ignore = true                     # 管理员是否忽略积分扣除
whitelist_ignore = true                 # 白名单用户是否忽略积分扣除

# Http代理设置
# 格式: http://用户名:密码@代理地址:代理端口
# 例如：http://127.0.0.1:7890
http-proxy = ""

# 高级设置
max_tokens = 4096                       # 最大token数
temperature = 0.7                       # 温度参数
top_p = 1.0                             # Top-p采样
frequency_penalty = 0.0                 # 频率惩罚
presence_penalty = 0.0                  # 存在惩罚
