import asyncio
import json
import os
import time
import tomllib
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from urllib.parse import urljoin

import aiohttp
import feedparser
import requests
import urllib3
from lxml import etree
from loguru import logger

from WechatAPI import WechatAPIClient
from database.XYBotDB import XYBotDB
from utils.decorators import *
from utils.plugin_base import PluginBase


class SecurityNews(PluginBase):
    description = "安全资讯推送插件"
    author = "老王"
    version = "1.0.0"

    def __init__(self):
        super().__init__()
        
        # 忽略HTTPS告警
        urllib3.disable_warnings()
        
        # 加载配置
        with open("plugins/SecurityNews/config.toml", "rb") as f:
            plugin_config = tomllib.load(f)
        
        with open("main_config.toml", "rb") as f:
            main_config = tomllib.load(f)
        
        config = plugin_config["SecurityNews"]
        main_config = main_config["XYBot"]
        
        # 基础配置
        self.enable = config["enable"]
        self.commands = config["commands"]
        self.command_format = config["command_format"]
        
        # 定时推送配置
        self.enable_schedule_push = config["enable_schedule_push"]
        self.schedule_hours = config["schedule_hours"]
        
        # 推送范围配置
        self.push_scope = config["push_scope"]
        
        # 重试配置
        self.max_retry_count = config["max_retry_count"]
        self.retry_delay = config["retry_delay"]
        
        # 缓存配置
        self.cache_expire_hours = config["cache_expire_hours"]
        
        # 资讯源配置
        self.news_sources = config["news_sources"]
        
        # 消息格式配置
        self.message_prefix = config["message_prefix"]
        self.system_copyright = config["system_copyright"]
        
        # 管理员配置
        self.admins = main_config["admins"]
        
        # 数据库
        self.db = XYBotDB()
        
        # 缓存文件路径
        self.cache_dir = "plugins/SecurityNews/cache"
        os.makedirs(self.cache_dir, exist_ok=True)
        self.cache_file = os.path.join(self.cache_dir, "security_news.json")
        
        # 白名单文件路径
        self.whitelist_file = "plugins/SecurityNews/whitelist.txt"
        
        logger.success("SecurityNews插件初始化完成")

    def _load_whitelist(self) -> List[str]:
        """加载白名单"""
        try:
            if os.path.exists(self.whitelist_file):
                with open(self.whitelist_file, 'r', encoding='utf-8') as f:
                    return [line.strip() for line in f.readlines() if line.strip()]
            return []
        except Exception as e:
            logger.error(f"加载白名单失败: {e}")
            return []

    def _save_whitelist(self, whitelist: List[str]):
        """保存白名单"""
        try:
            os.makedirs(os.path.dirname(self.whitelist_file), exist_ok=True)
            with open(self.whitelist_file, 'w', encoding='utf-8') as f:
                for wxid in whitelist:
                    f.write(f"{wxid}\n")
        except Exception as e:
            logger.error(f"保存白名单失败: {e}")

    def _is_cache_valid(self) -> bool:
        """检查缓存是否有效"""
        try:
            if not os.path.exists(self.cache_file):
                return False
            
            with open(self.cache_file, 'r', encoding='utf-8') as f:
                cache_data = json.load(f)
            
            cache_time = cache_data.get('timestamp', 0)
            expire_time = cache_time + (self.cache_expire_hours * 3600)
            
            return time.time() < expire_time
        except Exception as e:
            logger.error(f"检查缓存失败: {e}")
            return False

    def _load_cache(self) -> Optional[str]:
        """加载缓存的安全资讯"""
        try:
            if self._is_cache_valid():
                with open(self.cache_file, 'r', encoding='utf-8') as f:
                    cache_data = json.load(f)
                return cache_data.get('content', '')
            return None
        except Exception as e:
            logger.error(f"加载缓存失败: {e}")
            return None

    def _save_cache(self, content: str):
        """保存安全资讯到缓存"""
        try:
            cache_data = {
                'content': content,
                'timestamp': time.time()
            }
            with open(self.cache_file, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存缓存失败: {e}")

    async def _get_freebuf_news(self) -> str:
        """获取FreeBuf安全资讯"""
        try:
            logger.info("正在获取FreeBuf安全资讯...")
            yesterday = (datetime.now() + timedelta(-1))
            target_time = yesterday.strftime("%a, %d %b %Y")
            news_content = "#FreeBuf安全资讯\n"
            
            rs1 = feedparser.parse('https://www.freebuf.com/feed')
            found_articles = False
            
            for ent in rs1['entries']:
                if target_time in ent['published']:
                    title = ent['title']
                    link = ent['link']
                    news_content += f'\n{title}\n{link}\n'
                    found_articles = True
            
            if not found_articles:
                news_content += '\n今日暂无文章\n'
                
            return news_content
            
        except Exception as e:
            logger.error(f"获取FreeBuf资讯失败: {e}")
            return "#FreeBuf安全资讯\n今日暂无文章\n"

    async def _get_xianzhishequ_news(self) -> str:
        """获取先知社区安全资讯"""
        try:
            logger.info("正在获取先知社区安全资讯...")
            yesterday = (datetime.now() + timedelta(-1))
            target_time = str(yesterday.strftime('%Y-%m-%d'))
            news_content = "#先知社区\n"
            
            rs1 = feedparser.parse('https://xz.aliyun.com/feed')
            found_articles = False
            
            for ent in rs1['entries']:
                if target_time in ent['published']:
                    title = ent['title']
                    link = ent['link']
                    news_content += f'\n{title}\n{link}\n'
                    found_articles = True
            
            if not found_articles:
                news_content += '\n今日暂无文章\n'
                
            return news_content
            
        except Exception as e:
            logger.error(f"获取先知社区资讯失败: {e}")
            return "#先知社区\n今日暂无文章\n"

    async def _get_qianxin_news(self) -> str:
        """获取奇安信攻防社区安全资讯"""
        try:
            logger.info("正在获取奇安信攻防社区安全资讯...")
            yesterday = (datetime.now() + timedelta(-1))
            target_time = str(yesterday.strftime('%Y-%m-%d'))
            news_content = "#奇安信攻防社区\n"
            
            resp = requests.get(url='https://forum.butian.net/community?page=1', verify=True, timeout=10)
            tree = etree.HTML(resp.text)
            sections = tree.xpath('//div[@class="stream-list blog-stream"]/section')
            found_articles = False
            
            for section in sections:
                try:
                    title = section.xpath('./div/h2/a/text()')[0].strip()
                    href = section.xpath('./div/h2/a/@href')[0]
                    date_time = section.xpath('./div/ul/li[4]/text()')[0].strip('发布于 ').strip()
                    
                    if target_time in date_time:
                        news_content += f'\n{title}\n{href}\n'
                        found_articles = True
                except:
                    continue
            
            if not found_articles:
                news_content += '\n今日暂无文章\n'
                
            return news_content
            
        except Exception as e:
            logger.error(f"获取奇安信攻防社区资讯失败: {e}")
            return "#奇安信攻防社区\n今日暂无文章\n"

    async def _get_anquanke_news(self) -> str:
        """获取安全客安全资讯"""
        try:
            logger.info("正在获取安全客安全资讯...")
            yesterday = (datetime.now() + timedelta(-1))
            target_time = str(yesterday.strftime('%Y-%m-%d'))
            news_content = "#安全客\n"
            
            resp = requests.get('https://www.anquanke.com/news', timeout=10, verify=True)
            tree = etree.HTML(resp.text)
            divs = tree.xpath('//div[@id="post-list"]/div')
            found_articles = False
            
            for div in divs:
                try:
                    title = div.xpath('./div/div[2]/div/div[@class="title"]/a/text()')[0].strip().replace(' ', '')
                    href = urljoin('https://www.anquanke.com/news',
                                   div.xpath('./div/div[2]/div/div[@class="title"]/a/@href')[0])
                    date_time = div.xpath('./div/div[2]/div/div[@class="info"]/div[1]/span[@class="date"]/span/text()')[1]
                    
                    if target_time in date_time:
                        news_content += f'\n{title}\n{href}\n'
                        found_articles = True
                except:
                    continue
            
            if not found_articles:
                news_content += '\n今日暂无文章\n'
                
            return news_content
            
        except Exception as e:
            logger.error(f"获取安全客资讯失败: {e}")
            return "#安全客\n今日暂无文章\n"

    async def _get_security_news(self, retry_count: int = 0) -> str:
        """获取完整的安全资讯，支持重试机制"""
        try:
            logger.info(f"开始获取安全资讯 (尝试次数: {retry_count + 1})")

            # 检查缓存
            cached_content = self._load_cache()
            if cached_content:
                logger.info("使用缓存的安全资讯")
                return cached_content

            # 获取各个源的资讯
            news_parts = []

            if "freebuf" in self.news_sources:
                freebuf_news = await self._get_freebuf_news()
                news_parts.append(freebuf_news)

            if "xianzhishequ" in self.news_sources:
                xianzhishequ_news = await self._get_xianzhishequ_news()
                news_parts.append(xianzhishequ_news)

            if "qianxin" in self.news_sources:
                qianxin_news = await self._get_qianxin_news()
                news_parts.append(qianxin_news)

            if "anquanke" in self.news_sources:
                anquanke_news = await self._get_anquanke_news()
                news_parts.append(anquanke_news)

            # 组合所有资讯
            full_content = f"{self.message_prefix}\n\n"
            full_content += "\n\n".join(news_parts)
            full_content += f"\n\n{self.system_copyright}整理分享\n{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

            # 保存到缓存
            self._save_cache(full_content)

            logger.success("安全资讯获取成功")
            return full_content

        except Exception as e:
            logger.error(f"获取安全资讯失败 (尝试次数: {retry_count + 1}): {e}")

            # 重试机制
            if retry_count < self.max_retry_count:
                logger.info(f"等待{self.retry_delay}秒后重试...")
                await asyncio.sleep(self.retry_delay)
                return await self._get_security_news(retry_count + 1)
            else:
                logger.error("达到最大重试次数，获取安全资讯失败")
                return f"{self.message_prefix}\n\n获取安全资讯失败，请稍后再试"

    async def _get_all_chatrooms(self, bot: WechatAPIClient) -> List[Dict[str, str]]:
        """获取所有群聊列表 - 参考admin的完整实现"""
        try:
            logger.info("正在获取群聊列表...")

            # 首先尝试从数据库获取
            try:
                from database.contacts_db import get_contacts_from_db
                cached_contacts = get_contacts_from_db()
                if cached_contacts:
                    chatrooms = []
                    for contact in cached_contacts:
                        if contact.get('wxid', '').endswith("@chatroom"):
                            chatrooms.append({
                                "wxid": contact['wxid'],
                                "nickname": contact.get('nickname') or contact.get('name') or contact['wxid']
                            })
                    if chatrooms:
                        logger.info(f"从数据库获取到{len(chatrooms)}个群聊")
                        return chatrooms
            except Exception as e:
                logger.warning(f"从数据库获取群聊失败: {e}")

            # 如果数据库没有数据，从微信API获取
            logger.info("从微信API获取群聊列表...")

            # 尝试使用新的get_total_contract_list API
            all_contacts_data = None
            try:
                if hasattr(bot, 'get_total_contract_list'):
                    all_contacts_data = await bot.get_total_contract_list(wx_seq=0, chatroom_seq=0)
                    logger.info("成功使用get_total_contract_list API")
                else:
                    raise AttributeError("Bot没有get_total_contract_list方法")
            except Exception as e:
                logger.warning(f"使用新API失败: {e}，回退到旧方法")

                # 回退到递归获取方法
                id_list = []
                wx_seq, chatroom_seq = 0, 0
                iteration = 0

                while True:
                    iteration += 1
                    logger.debug(f"获取联系人批次 {iteration}，wx_seq={wx_seq}, chatroom_seq={chatroom_seq}")

                    try:
                        contact_list = await bot.get_contract_list(wx_seq, chatroom_seq)
                        if not contact_list or 'ContactUsernameList' not in contact_list:
                            logger.warning(f"批次 {iteration} 返回数据无效")
                            break

                        batch_contacts = contact_list["ContactUsernameList"]
                        if not batch_contacts:
                            logger.info(f"批次 {iteration} 没有更多联系人")
                            break

                        id_list.extend(batch_contacts)
                        wx_seq = contact_list.get("CurrentWxcontactSeq", wx_seq)
                        chatroom_seq = contact_list.get("CurrentChatRoomContactSeq", chatroom_seq)

                        if contact_list.get("CountinueFlag") != 1:
                            logger.info(f"获取完成，共{iteration}个批次，{len(id_list)}个联系人")
                            break

                        # 避免无限循环
                        if iteration > 100:
                            logger.warning("达到最大迭代次数，停止获取")
                            break

                    except Exception as batch_e:
                        logger.error(f"批次 {iteration} 获取失败: {batch_e}")
                        break

                all_contacts_data = {"ContactUsernameList": id_list}

            # 处理获取到的联系人数据
            if not all_contacts_data or 'ContactUsernameList' not in all_contacts_data:
                logger.error("未获取到有效的联系人数据")
                return []

            contact_usernames = all_contacts_data["ContactUsernameList"]
            logger.info(f"获取到{len(contact_usernames)}个联系人")

            # 筛选出群聊
            chatroom_ids = [wxid for wxid in contact_usernames if wxid.endswith("@chatroom")]
            logger.info(f"筛选出{len(chatroom_ids)}个群聊")

            if not chatroom_ids:
                return []

            # 批量获取群聊昵称
            chatrooms = []
            batch_size = 20

            for i in range(0, len(chatroom_ids), batch_size):
                batch_ids = chatroom_ids[i:i + batch_size]
                logger.debug(f"获取群聊昵称批次 {i//batch_size + 1}，{len(batch_ids)}个群聊")

                for wxid in batch_ids:
                    try:
                        nickname = await bot.get_nickname(wxid)
                        chatrooms.append({
                            "wxid": wxid,
                            "nickname": nickname if nickname else "未知群聊"
                        })
                    except Exception as e:
                        logger.warning(f"获取群聊{wxid}昵称失败: {e}")
                        chatrooms.append({
                            "wxid": wxid,
                            "nickname": "未知群聊"
                        })

                # 避免请求过快
                if i + batch_size < len(chatroom_ids):
                    await asyncio.sleep(0.5)

            logger.success(f"成功获取到{len(chatrooms)}个群聊信息")
            return chatrooms

        except Exception as e:
            logger.error(f"获取群聊列表失败: {e}")
            return []

    async def _push_to_chatrooms(self, bot: WechatAPIClient, content: str):
        """推送安全资讯到群聊"""
        try:
            target_chatrooms = []

            if self.push_scope == "all":
                # 推送到所有群聊
                logger.info("获取所有群聊进行推送...")
                all_chatrooms = await self._get_all_chatrooms(bot)
                target_chatrooms = [room["wxid"] for room in all_chatrooms]
                logger.info(f"找到{len(target_chatrooms)}个群聊")
            elif self.push_scope == "whitelist":
                # 推送到白名单群聊
                logger.info("使用白名单群聊进行推送...")
                whitelist = self._load_whitelist()
                # 验证白名单中的群聊是否有效
                valid_chatrooms = []
                for wxid in whitelist:
                    if wxid.endswith("@chatroom"):
                        valid_chatrooms.append(wxid)
                    else:
                        logger.warning(f"白名单中的{wxid}不是有效的群聊ID")
                target_chatrooms = valid_chatrooms
                logger.info(f"白名单中有{len(target_chatrooms)}个有效群聊")

            if not target_chatrooms:
                logger.warning("没有找到目标群聊，跳过推送")
                return

            logger.info(f"开始推送安全资讯到{len(target_chatrooms)}个群聊")

            success_count = 0
            failed_count = 0
            failed_list = []

            for i, wxid in enumerate(target_chatrooms, 1):
                try:
                    logger.debug(f"推送到群聊 {i}/{len(target_chatrooms)}: {wxid}")
                    await bot.send_text_message(wxid, content)
                    success_count += 1
                    logger.debug(f"成功推送到群聊: {wxid}")

                    # 避免发送过快，每5个群聊后稍作停顿
                    if i % 5 == 0:
                        await asyncio.sleep(3)
                    else:
                        await asyncio.sleep(1)

                except Exception as e:
                    failed_count += 1
                    failed_list.append(wxid)
                    logger.error(f"推送到群聊{wxid}失败: {e}")

            # 推送结果统计
            result_msg = f"安全资讯推送完成！成功: {success_count}, 失败: {failed_count}, 总计: {len(target_chatrooms)}"
            logger.success(result_msg)

            if failed_list:
                logger.warning(f"推送失败的群聊: {failed_list}")

        except Exception as e:
            logger.error(f"推送安全资讯失败: {e}")

    @on_text_message
    async def handle_text(self, bot: WechatAPIClient, message: dict):
        if not self.enable:
            return

        content = str(message["Content"]).strip()
        command = content.split(" ")
        sender_wxid = message["SenderWxid"]
        from_wxid = message["FromWxid"]

        if not command or command[0] not in self.commands:
            return

        # 获取安全资讯
        if command[0] in ["安全资讯", "安全新闻", "获取安全资讯"]:
            news_content = await self._get_security_news()
            await bot.send_text_message(from_wxid, news_content)
            return

        # 以下功能需要管理员权限
        if sender_wxid not in self.admins:
            await bot.send_text_message(from_wxid, "-----XYBot-----\n❌你配用这个指令吗？😡")
            return

        # 立即推送安全资讯
        if command[0] == "立即推送安全资讯":
            news_content = await self._get_security_news()
            await self._push_to_chatrooms(bot, news_content)
            await bot.send_text_message(from_wxid, "-----XYBot-----\n✅安全资讯推送完成！")
            return

        # 添加白名单
        if command[0] == "添加安全资讯白名单":
            target_wxid = None

            if len(command) >= 2:
                # 方式1: 直接指定群聊ID
                target_wxid = command[1]
                if not target_wxid.endswith("@chatroom"):
                    await bot.send_text_message(from_wxid, "-----XYBot-----\n❌请输入有效的群聊ID（必须以@chatroom结尾）")
                    return
            else:
                # 方式2: 在群聊中使用指令，添加当前群聊
                if from_wxid.endswith("@chatroom"):
                    target_wxid = from_wxid
                else:
                    await bot.send_text_message(from_wxid,
                        "-----XYBot-----\n❌请指定群聊ID或在群聊中使用此指令\n\n使用方法：\n1. 添加安全资讯白名单 群聊ID\n2. 在群聊中直接发送：添加安全资讯白名单")
                    return

            whitelist = self._load_whitelist()
            if target_wxid not in whitelist:
                whitelist.append(target_wxid)
                self._save_whitelist(whitelist)

                nickname = await bot.get_nickname(target_wxid)
                display_name = nickname if nickname else "未知群聊"

                if len(command) >= 2:
                    # 指定群聊ID的情况
                    await bot.send_text_message(from_wxid,
                        f"-----XYBot-----\n✅成功添加群聊到安全资讯推送白名单\n群聊：{display_name}\nID：{target_wxid}")
                else:
                    # 当前群聊的情况
                    await bot.send_text_message(from_wxid,
                        f"-----XYBot-----\n✅成功添加当前群聊到安全资讯推送白名单\n群聊：{display_name}")
            else:
                await bot.send_text_message(from_wxid, "-----XYBot-----\n⚠️该群聊已在白名单中")
            return

        # 删除白名单
        if command[0] == "删除安全资讯白名单":
            target_wxid = None

            if len(command) >= 2:
                # 方式1: 直接指定群聊ID
                target_wxid = command[1]
                if not target_wxid.endswith("@chatroom"):
                    await bot.send_text_message(from_wxid, "-----XYBot-----\n❌请输入有效的群聊ID（必须以@chatroom结尾）")
                    return
            else:
                # 方式2: 在群聊中使用指令，删除当前群聊
                if from_wxid.endswith("@chatroom"):
                    target_wxid = from_wxid
                else:
                    await bot.send_text_message(from_wxid,
                        "-----XYBot-----\n❌请指定群聊ID或在群聊中使用此指令\n\n使用方法：\n1. 删除安全资讯白名单 群聊ID\n2. 在群聊中直接发送：删除安全资讯白名单")
                    return

            whitelist = self._load_whitelist()
            if target_wxid in whitelist:
                whitelist.remove(target_wxid)
                self._save_whitelist(whitelist)

                nickname = await bot.get_nickname(target_wxid)
                display_name = nickname if nickname else "未知群聊"

                if len(command) >= 2:
                    # 指定群聊ID的情况
                    await bot.send_text_message(from_wxid,
                        f"-----XYBot-----\n✅成功从安全资讯推送白名单移除群聊\n群聊：{display_name}\nID：{target_wxid}")
                else:
                    # 当前群聊的情况
                    await bot.send_text_message(from_wxid,
                        f"-----XYBot-----\n✅成功从安全资讯推送白名单移除当前群聊\n群聊：{display_name}")
            else:
                await bot.send_text_message(from_wxid, "-----XYBot-----\n⚠️该群聊不在白名单中")
            return

        # 查看白名单列表
        if command[0] == "安全资讯白名单列表":
            whitelist = self._load_whitelist()
            if not whitelist:
                await bot.send_text_message(from_wxid, "-----XYBot-----\n📋安全资讯推送白名单为空")
                return

            whitelist_info = []
            for wxid in whitelist:
                nickname = await bot.get_nickname(wxid)
                whitelist_info.append(f"{wxid} {nickname if nickname else '未知群聊'}")

            whitelist_text = "\n".join(whitelist_info)
            await bot.send_text_message(from_wxid, f"-----XYBot-----\n📋安全资讯推送白名单：\n{whitelist_text}")
            return

        # 获取群聊列表
        if command[0] == "获取群聊列表":
            logger.info(f"管理员{sender_wxid}请求获取群聊列表")
            chatrooms = await self._get_all_chatrooms(bot)
            if not chatrooms:
                await bot.send_text_message(from_wxid, "-----XYBot-----\n❌获取群聊列表失败，请稍后重试")
                return

            # 按群聊名称排序
            chatrooms.sort(key=lambda x: x['nickname'])

            # 分页显示，每页显示15个
            page_size = 15
            total_count = len(chatrooms)
            total_pages = (total_count + page_size - 1) // page_size

            # 检查是否指定了页码
            page_num = 1
            if len(command) > 1 and command[1].isdigit():
                page_num = int(command[1])
                if page_num < 1:
                    page_num = 1
                elif page_num > total_pages:
                    page_num = total_pages

            # 计算显示范围
            start_idx = (page_num - 1) * page_size
            end_idx = min(start_idx + page_size, total_count)
            page_chatrooms = chatrooms[start_idx:end_idx]

            # 构建显示内容
            chatroom_info = []
            for i, room in enumerate(page_chatrooms, start_idx + 1):
                # 检查是否在白名单中
                whitelist = self._load_whitelist()
                in_whitelist = "✅" if room['wxid'] in whitelist else "⭕"
                chatroom_info.append(f"{i:2d}. {in_whitelist} {room['nickname']}\n    {room['wxid']}")

            chatroom_text = "\n\n".join(chatroom_info)

            # 构建完整消息
            header = f"📋 群聊列表 (第{page_num}/{total_pages}页，共{total_count}个)\n"
            header += "✅=已在白名单 ⭕=未在白名单\n"
            header += "=" * 30 + "\n"

            footer = "\n" + "=" * 30
            if total_pages > 1:
                footer += f"\n💡 查看其他页面: 获取群聊列表 [页码]"
                if page_num < total_pages:
                    footer += f"\n📄 下一页: 获取群聊列表 {page_num + 1}"

            full_message = f"-----XYBot-----\n{header}{chatroom_text}{footer}"

            await bot.send_text_message(from_wxid, full_message)
            logger.info(f"已向管理员发送群聊列表第{page_num}页")
            return

        # 查看设置
        if command[0] == "安全资讯设置":
            whitelist = self._load_whitelist()
            settings_info = f"""-----XYBot-----
🔧 安全资讯推送设置：

📰 资讯源：{', '.join(self.news_sources)}
⏰ 定时推送：{'开启' if self.enable_schedule_push else '关闭'}
🕐 推送时间：{', '.join([f'{h}:00' for h in self.schedule_hours])}
📡 推送范围：{'所有群聊' if self.push_scope == 'all' else '白名单群聊'}
📋 白名单数量：{len(whitelist)}个群聊
🔄 最大重试次数：{self.max_retry_count}次
⏱️ 缓存过期时间：{self.cache_expire_hours}小时"""

            await bot.send_text_message(from_wxid, settings_info)
            return

        # 显示帮助
        await bot.send_text_message(from_wxid, self.command_format)

    # 定时推送任务
    @schedule('cron', minute=0)
    async def scheduled_push(self, bot: WechatAPIClient):
        """定时推送安全资讯"""
        if not self.enable or not self.enable_schedule_push:
            return

        current_hour = datetime.now().hour
        if current_hour not in self.schedule_hours:
            return

        logger.info(f"开始定时推送安全资讯 (时间: {current_hour}:00)")

        try:
            news_content = await self._get_security_news()
            await self._push_to_chatrooms(bot, news_content)
            logger.success("定时推送安全资讯完成")
        except Exception as e:
            logger.error(f"定时推送安全资讯失败: {e}")
