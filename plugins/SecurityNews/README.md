# SecurityNews 安全资讯推送插件

## 功能介绍

这个插件可以自动获取多个安全资讯源的最新内容，支持定时推送和手动获取，提供完善的白名单管理功能。

## 主要特性

### 📰 多源安全资讯
- **FreeBuf** - 知名安全媒体平台
- **先知社区** - 阿里云安全社区
- **奇安信攻防社区** - 专业攻防技术分享
- **安全客** - 安全行业资讯平台

### ⏰ 定时推送
- 支持自定义推送时间（默认9点和18点）
- 可配置推送范围（所有群聊/白名单群聊）
- 智能缓存机制，避免重复获取

### 🛡️ 稳定可靠
- 自动重试机制，最大重试3次
- 本地缓存，提高响应速度
- 详细的错误日志记录

### 👥 白名单管理
- 支持添加/删除群聊到推送白名单
- 可查看当前白名单状态
- 支持@群聊或直接输入群聊ID

## 使用指令

### 普通用户指令
```
安全资讯          # 立即获取最新安全资讯
安全新闻          # 同上
获取安全资讯      # 同上
```

### 管理员指令
```
立即推送安全资讯                    # 手动触发推送
添加安全资讯白名单 [群聊ID]         # 添加群聊到白名单
删除安全资讯白名单 [群聊ID]         # 从白名单移除群聊
安全资讯白名单列表                  # 查看当前白名单
获取群聊列表 [页码]                 # 获取所有群聊信息（支持分页）
安全资讯设置                        # 查看当前配置
```

### 白名单操作详解
```
# 方式1：指定群聊ID
添加安全资讯白名单 12345678@chatroom
删除安全资讯白名单 12345678@chatroom

# 方式2：在群聊中直接操作（推荐）
添加安全资讯白名单    # 在群聊中发送，添加当前群聊
删除安全资讯白名单    # 在群聊中发送，移除当前群聊

# 获取群聊ID的方法
获取群聊列表 1        # 查看第1页群聊列表，复制需要的群聊ID
```

## 配置说明

### 基础配置
- `enable`: 是否启用插件
- `enable_schedule_push`: 是否启用定时推送
- `schedule_hours`: 推送时间（24小时制）
- `push_scope`: 推送范围（"all"=所有群聊，"whitelist"=白名单）

### 重试配置
- `max_retry_count`: 最大重试次数（默认3次）
- `retry_delay`: 重试间隔（默认5秒）

### 缓存配置
- `cache_expire_hours`: 缓存过期时间（默认1小时）

## 文件结构
```
plugins/SecurityNews/
├── __init__.py          # 插件初始化
├── main.py              # 主要逻辑
├── config.toml          # 配置文件
├── whitelist.txt        # 推送白名单
├── cache/               # 缓存目录
│   └── security_news.json
└── README.md            # 说明文档
```

## 注意事项

1. **管理员权限**：白名单管理、推送设置等功能需要管理员权限
2. **网络依赖**：需要稳定的网络连接获取安全资讯
3. **推送频率**：为避免被限制，推送间隔设置为2秒
4. **缓存机制**：相同时间段内多次请求会使用缓存，提高效率

## 作者信息

- **作者**: 老王
- **版本**: 1.0.0
- **描述**: 专业的安全资讯推送插件，支持多源获取和智能推送
