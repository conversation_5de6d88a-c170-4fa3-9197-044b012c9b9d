[SecurityNews]
enable = true

# 基础配置
commands = [
    "安全资讯", "安全新闻", "获取安全资讯", "立即推送安全资讯",
    "添加安全资讯白名单", "删除安全资讯白名单", "安全资讯白名单列表",
    "获取群聊列表", "安全资讯设置"
]

# 定时推送配置
enable_schedule_push = true
# 推送时间配置 (24小时制)
schedule_hours = [9, 18]  # 每天9点和18点推送

# 推送范围配置
# "all" = 所有群聊, "whitelist" = 仅白名单群聊
push_scope = "whitelist"

# 重试配置
max_retry_count = 3  # 最大重试次数
retry_delay = 5      # 重试间隔(秒)

# 缓存配置
cache_expire_hours = 1  # 缓存过期时间(小时)

# 安全资讯源配置
news_sources = [
    "freebuf",      # FreeBuf
    "xianzhishequ", # 先知社区
    "qianxin",      # 奇安信攻防社区
    "anquanke"      # 安全客
]

# 消息格式配置
message_prefix = "🔒 安全资讯推送"
system_copyright = "XYBot安全资讯"

# 指令格式提示
command_format = """🔒 安全资讯推送插件

📰 获取资讯：
安全资讯 - 立即获取最新安全资讯
立即推送安全资讯 - 手动触发推送到配置的群聊

⚙️ 白名单管理（仅管理员）：
添加安全资讯白名单 [群聊ID] - 添加群聊到推送白名单
删除安全资讯白名单 [群聊ID] - 从推送白名单移除群聊
安全资讯白名单列表 - 查看当前白名单

📝 白名单操作说明：
• 方式1：添加安全资讯白名单 12345678@chatroom
• 方式2：在群聊中直接发送"添加安全资讯白名单"（添加当前群聊）
• 删除操作同理，支持指定ID或在群聊中直接操作

📋 群聊管理（仅管理员）：
获取群聊列表 [页码] - 获取所有群聊名称和ID（支持分页）
示例：获取群聊列表 2 - 查看第2页群聊

🔧 设置查看：
安全资讯设置 - 查看当前推送配置

💡 使用提示：
• 定时推送时间可在config.toml中配置
• 推送范围支持"所有群聊"和"白名单群聊"两种模式
• 群聊列表中✅表示已在白名单，⭕表示未在白名单"""
