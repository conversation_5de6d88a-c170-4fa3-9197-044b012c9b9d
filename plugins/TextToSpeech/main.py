import os
import re
import hashlib
import aiohttp
import tomllib
from loguru import logger
from urllib.parse import urlparse

from WechatAPI import WechatAPIClient
from utils.decorators import on_text_message, on_quote_message
from utils.plugin_base import PluginBase
from .voice_menu import VoiceMenuGenerator


class TextToSpeech(PluginBase):
    description = "文字转语音插件"
    author = "Augment Agent"
    version = "1.0.0"

    def __init__(self):
        super().__init__()

        # 获取配置文件路径
        config_path = os.path.join(os.path.dirname(__file__), "config.toml")
        
        try:
            with open(config_path, "rb") as f:
                config = tomllib.load(f)
                
            # 读取基本配置
            basic_config = config.get("basic", {})
            self.enable = basic_config.get("enable", False)
            
            # 读取设置配置
            settings_config = config.get("settings", {})
            self.trigger_keyword = settings_config.get("trigger_keyword", "语音")
            self.menu_keyword = settings_config.get("menu_keyword", "语音菜单")
            self.api_url = settings_config.get("api_url", "http://www.yx520.ltd/API/wzzyy/api.php")
            self.default_voice_id = settings_config.get("default_voice_id", 1)
            self.max_cache_size = settings_config.get("max_cache_size", 100)
            
            # 创建缓存目录
            self.cache_dir = os.path.join(os.path.dirname(__file__), 
                                        settings_config.get("cache_dir", "cache"))
            os.makedirs(self.cache_dir, exist_ok=True)
            
        except Exception as e:
            logger.error(f"加载TextToSpeech配置文件失败: {str(e)}")
            self.enable = False
            self.trigger_keyword = "语音"
            self.menu_keyword = "语音菜单"
            self.api_url = "http://www.yx520.ltd/API/wzzyy/api.php"
            self.default_voice_id = 1
            self.max_cache_size = 100
            self.cache_dir = os.path.join(os.path.dirname(__file__), "cache")
            os.makedirs(self.cache_dir, exist_ok=True)

        # 初始化音色菜单生成器
        self.voice_menu = VoiceMenuGenerator()
        
        logger.info(f"TextToSpeech插件初始化完成，触发关键词: {self.trigger_keyword}")

    async def async_init(self):
        """异步初始化"""
        return

    @on_text_message(priority=30)
    async def handle_text(self, bot: WechatAPIClient, message: dict):
        """处理文本消息"""
        if not self.enable:
            return True

        content = message.get("Content", "").strip()
        from_wxid = message.get("FromWxid")
        
        # 检查是否是菜单关键词
        if content == self.menu_keyword:
            await self.send_voice_menu(bot, from_wxid)
            return False
        
        # 检查是否是直接指令触发
        if await self.handle_direct_command(bot, message, content):
            return False
            
        return True

    @on_quote_message(priority=30)
    async def handle_quote(self, bot: WechatAPIClient, message: dict):
        """处理引用消息"""
        if not self.enable:
            return True

        content = message.get("Content", "").strip()
        quote_info = message.get("Quote", {})
        quoted_content = quote_info.get("Content", "").strip()
        
        # 检查是否是转语音指令
        if await self.handle_quote_command(bot, message, content, quoted_content):
            return False
            
        return True

    async def handle_direct_command(self, bot: WechatAPIClient, message: dict, content: str) -> bool:
        """处理直接指令"""
        # 检查是否以触发关键词开头
        if not content.startswith(self.trigger_keyword):
            return False
        
        # 移除触发关键词
        command_content = content[len(self.trigger_keyword):].strip()
        
        # 解析指令格式：<音色id或名称>#<内容> 或 <音色id或名称> <内容>
        voice_spec = None
        text_to_convert = None
        
        # 尝试#分割
        if '#' in command_content:
            parts = command_content.split('#', 1)
            if len(parts) == 2:
                voice_spec = parts[0].strip()
                text_to_convert = parts[1].strip()
        # 尝试空格分割
        elif ' ' in command_content:
            parts = command_content.split(' ', 1)
            if len(parts) == 2:
                voice_spec = parts[0].strip()
                text_to_convert = parts[1].strip()
        
        if not voice_spec or not text_to_convert:
            await bot.send_text_message(
                message["FromWxid"],
                f"指令格式错误！\n正确格式：\n{self.trigger_keyword}<音色ID或名称>#<要转换的文字>\n或\n{self.trigger_keyword}<音色ID或名称> <要转换的文字>\n\n发送 '{self.menu_keyword}' 查看音色列表"
            )
            return True
        
        # 获取音色ID
        voice_id = self.voice_menu.get_voice_id_by_name(voice_spec)
        if voice_id is None:
            await bot.send_text_message(
                message["FromWxid"],
                f"音色 '{voice_spec}' 不存在！\n发送 '{self.menu_keyword}' 查看可用音色列表"
            )
            return True
        
        # 执行文字转语音
        await self.convert_text_to_speech(bot, message["FromWxid"], text_to_convert, voice_id)
        return True

    async def handle_quote_command(self, bot: WechatAPIClient, message: dict, content: str, quoted_content: str) -> bool:
        """处理引用消息指令"""
        # 检查是否是转语音指令：转<音色id或名称>
        if not content.startswith("转"):
            return False
        
        voice_spec = content[1:].strip()
        if not voice_spec:
            await bot.send_text_message(
                message["FromWxid"],
                f"请指定音色！\n格式：转<音色ID或名称>\n发送 '{self.menu_keyword}' 查看音色列表"
            )
            return True
        
        # 获取音色ID
        voice_id = self.voice_menu.get_voice_id_by_name(voice_spec)
        if voice_id is None:
            await bot.send_text_message(
                message["FromWxid"],
                f"音色 '{voice_spec}' 不存在！\n发送 '{self.menu_keyword}' 查看可用音色列表"
            )
            return True
        
        # 检查引用的内容
        if not quoted_content:
            await bot.send_text_message(
                message["FromWxid"],
                "引用的消息内容为空，无法转换为语音！"
            )
            return True
        
        # 执行文字转语音
        await self.convert_text_to_speech(bot, message["FromWxid"], quoted_content, voice_id)
        return True

    async def send_voice_menu(self, bot: WechatAPIClient, to_wxid: str):
        """发送音色菜单"""
        try:
            # 生成菜单图片
            menu_image = self.voice_menu.generate_menu_image()
            if menu_image:
                await bot.send_image_message(to_wxid, menu_image)
            else:
                # 如果图片生成失败，发送文本版本
                await self.send_text_menu(bot, to_wxid)
        except Exception as e:
            logger.error(f"发送音色菜单失败: {e}")
            await bot.send_text_message(to_wxid, f"发送音色菜单失败: {str(e)}")

    async def send_text_menu(self, bot: WechatAPIClient, to_wxid: str):
        """发送文本版音色菜单"""
        menu_text = "🎵 文字转语音音色列表 🎵\n\n"
        
        # 分批发送，避免消息过长
        batch_size = 50
        voice_items = list(self.voice_menu.voice_list.items())
        
        for i in range(0, len(voice_items), batch_size):
            batch = voice_items[i:i + batch_size]
            batch_text = menu_text if i == 0 else ""
            
            for voice_id, voice_name in batch:
                batch_text += f"{voice_id}:{voice_name}\n"
            
            if i == 0:
                batch_text += f"\n使用方法：\n1. 发送 '{self.trigger_keyword}<音色ID或名称>#<要转换的文字>'\n2. 引用消息回复 '转<音色ID或名称>'"
            
            await bot.send_text_message(to_wxid, batch_text)

    async def convert_text_to_speech(self, bot: WechatAPIClient, to_wxid: str, text: str, voice_id: int):
        """执行文字转语音转换"""
        try:
            # 检查文本长度
            if len(text) > 500:
                await bot.send_text_message(to_wxid, "文本过长！请控制在500字符以内。")
                return
            
            # 发送处理中提示
            voice_name = self.voice_menu.get_voice_name_by_id(voice_id)
            await bot.send_text_message(
                to_wxid, 
                f"🎵 正在使用音色 '{voice_name}({voice_id})' 转换语音...\n文本：{text[:50]}{'...' if len(text) > 50 else ''}"
            )
            
            # 调用API
            audio_url = await self.call_tts_api(text, voice_id)
            if not audio_url:
                await bot.send_text_message(to_wxid, "❌ 语音转换失败，请稍后重试。")
                return
            
            # 下载并发送语音
            audio_data = await self.download_audio(audio_url)
            if not audio_data:
                await bot.send_text_message(to_wxid, "❌ 语音下载失败，请稍后重试。")
                return
            
            # 发送语音消息
            await bot.send_voice_message(to_wxid, audio_data, format="mp3")
            logger.info(f"成功发送语音消息: 音色{voice_id}, 文本长度{len(text)}")
            
        except Exception as e:
            logger.error(f"文字转语音失败: {e}")
            await bot.send_text_message(to_wxid, f"❌ 语音转换失败: {str(e)}")

    async def call_tts_api(self, text: str, voice_id: int) -> str:
        """调用文字转语音API"""
        try:
            params = {
                'text': text,
                'voice': voice_id
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(self.api_url, params=params, timeout=30) as response:
                    if response.status == 200:
                        result = await response.json()
                        if result.get("code") == "200":
                            return result.get("url")
                        else:
                            logger.error(f"TTS API返回错误: {result}")
                            return None
                    else:
                        logger.error(f"TTS API请求失败: HTTP {response.status}")
                        return None
                        
        except Exception as e:
            logger.error(f"调用TTS API失败: {e}")
            return None

    async def download_audio(self, url: str) -> bytes:
        """下载音频文件"""
        try:
            # 生成缓存文件名
            url_hash = hashlib.md5(url.encode()).hexdigest()
            cache_file = os.path.join(self.cache_dir, f"{url_hash}.mp3")
            
            # 检查缓存
            if os.path.exists(cache_file):
                with open(cache_file, "rb") as f:
                    logger.info(f"使用缓存音频文件: {cache_file}")
                    return f.read()
            
            # 下载音频
            async with aiohttp.ClientSession() as session:
                async with session.get(url, timeout=30) as response:
                    if response.status == 200:
                        audio_data = await response.read()
                        
                        # 保存到缓存
                        with open(cache_file, "wb") as f:
                            f.write(audio_data)
                        
                        # 清理旧缓存
                        await self.cleanup_cache()
                        
                        logger.info(f"音频下载成功: {len(audio_data)} 字节")
                        return audio_data
                    else:
                        logger.error(f"音频下载失败: HTTP {response.status}")
                        return None
                        
        except Exception as e:
            logger.error(f"下载音频失败: {e}")
            return None

    async def cleanup_cache(self):
        """清理缓存文件"""
        try:
            cache_files = []
            for filename in os.listdir(self.cache_dir):
                if filename.endswith('.mp3'):
                    filepath = os.path.join(self.cache_dir, filename)
                    cache_files.append((filepath, os.path.getmtime(filepath)))
            
            # 按修改时间排序，删除最旧的文件
            if len(cache_files) > self.max_cache_size:
                cache_files.sort(key=lambda x: x[1])
                files_to_delete = cache_files[:-self.max_cache_size]
                
                for filepath, _ in files_to_delete:
                    try:
                        os.remove(filepath)
                        logger.debug(f"删除缓存文件: {filepath}")
                    except Exception as e:
                        logger.warning(f"删除缓存文件失败: {e}")
                        
        except Exception as e:
            logger.warning(f"清理缓存失败: {e}")
