# 文字转语音插件 (TextToSpeech)

一个功能强大的文字转语音插件，支持多种音色和触发方式。

## 功能特性

- 🎵 **多种音色**：支持358种不同的音色选择
- 🔄 **多种触发方式**：支持引用消息和直接指令两种触发方式
- 🖼️ **可视化菜单**：自动生成美观的音色列表图片
- 💾 **智能缓存**：自动缓存下载的语音文件，提高响应速度
- ⚙️ **灵活配置**：支持自定义触发关键词和各种参数

## 使用方法

### 1. 查看音色列表

发送关键词 `语音菜单` 即可获取完整的音色列表图片。

### 2. 引用消息转语音

1. 引用任意一条消息
2. 回复 `转<音色ID或名称>`

**示例：**
- `转1` - 使用音色ID 1（TVB女）
- `转懒羊羊` - 使用懒羊羊音色
- `转TVB女` - 使用TVB女音色

### 3. 直接指令转语音

发送 `语音<音色ID或名称>#<要转换的文字>` 或 `语音<音色ID或名称> <要转换的文字>`

**示例：**
- `语音1#你好世界` - 使用音色1转换"你好世界"
- `语音懒羊羊#测试语音` - 使用懒羊羊音色转换"测试语音"
- `语音1 你好世界` - 使用空格分隔的格式
- `语音TVB女 测试语音` - 使用音色名称

## 音色列表

插件支持358种音色，包括：

### 热门音色
- **1**: TVB女
- **2**: 懒羊羊  
- **3**: 磁性男生
- **4**: 孙悟空
- **5**: 咆哮哥
- **9**: 甜美女声
- **10**: 小姐姐

### 动漫角色
- **7**: 熊二
- **24**: 八戒
- **28**: 动漫小新
- **33**: 动漫海绵
- **47**: 唐小鸭
- **48**: 佩奇猪

### 专业播音
- **63**: 朗诵男声
- **64**: 沉稳男声
- **69**: 纪录片解说
- **70**: 新闻男主播
- **89**: 舌尖解说

### 方言音色
- **314**: 京腔小爷
- **315**: 广州德哥
- **325**: 东北老铁
- **327**: 台湾男生
- **339**: 粤语男声

*完整音色列表请发送 `语音菜单` 查看*

## 配置说明

插件配置文件位于 `plugins/TextToSpeech/config.toml`：

```toml
[basic]
# 是否启用插件
enable = true

[settings]
# 触发关键词（用于直接指令）
trigger_keyword = "语音"

# 菜单关键词（显示音色列表）
menu_keyword = "语音菜单"

# API设置
api_url = "http://www.yx520.ltd/API/wzzyy/api.php"

# 缓存设置
cache_dir = "cache"
max_cache_size = 100  # 最大缓存文件数量

# 默认音色ID（当用户输入无效音色时使用）
default_voice_id = 1
```

### 配置项说明

- `enable`: 插件开关，设置为 `false` 可禁用插件
- `trigger_keyword`: 直接指令的触发关键词，可自定义
- `menu_keyword`: 显示音色菜单的关键词，可自定义
- `api_url`: 文字转语音API地址
- `cache_dir`: 缓存目录名称
- `max_cache_size`: 最大缓存文件数量，超过会自动清理旧文件
- `default_voice_id`: 默认音色ID

## 注意事项

1. **文本长度限制**：单次转换的文本长度不能超过500字符
2. **音色识别**：支持音色ID（数字）和音色名称两种方式
3. **缓存机制**：相同的文本和音色组合会使用缓存，提高响应速度
4. **网络依赖**：需要网络连接来调用API和下载语音文件

## 错误处理

- 如果音色不存在，会提示用户查看音色列表
- 如果文本过长，会提示用户缩短文本
- 如果API调用失败，会提示用户稍后重试
- 如果语音下载失败，会提示用户稍后重试

## 技术特性

- **异步处理**：所有网络请求都是异步的，不会阻塞其他功能
- **错误恢复**：具备完善的错误处理和恢复机制
- **资源管理**：自动管理缓存文件，防止磁盘空间占用过多
- **高性能**：支持并发处理多个转换请求

## 更新日志

### v1.0.0
- 初始版本发布
- 支持358种音色
- 支持引用消息和直接指令两种触发方式
- 支持可视化音色菜单
- 支持智能缓存机制

## 开发者信息

- **作者**: Augment Agent
- **版本**: 1.0.0
- **许可**: 遵循项目主许可协议

## 支持与反馈

如果在使用过程中遇到问题或有改进建议，请通过以下方式联系：

1. 在项目仓库提交Issue
2. 加入官方交流群反馈
3. 通过私信联系开发者

---

*享受文字转语音的乐趣吧！🎵*
