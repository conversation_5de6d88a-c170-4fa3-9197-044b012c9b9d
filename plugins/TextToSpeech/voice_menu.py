"""
音色菜单图片生成模块
"""
import io

try:
    from PIL import Image, ImageDraw, ImageFont
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

try:
    from loguru import logger
except ImportError:
    # 如果loguru不可用，使用print作为替代
    class Logger:
        def info(self, msg):
            print(f"INFO: {msg}")
        def error(self, msg):
            print(f"ERROR: {msg}")
        def debug(self, msg):
            print(f"DEBUG: {msg}")
    logger = Logger()


class VoiceMenuGenerator:
    """音色菜单图片生成器"""
    
    def __init__(self):
        self.voice_list = {
            1: "TVB女", 2: "懒羊羊", 3: "磁性男生", 4: "孙悟空",
            5: "咆哮哥", 6: "如来佛祖", 7: "熊二", 8: "讲解旁白",
            9: "甜美女声", 10: "小姐姐", 11: "云龙哥", 12: "紫薇",
            13: "四郎", 14: "猴哥说唱", 15: "拽拽馒头", 16: "佛系馒头",
            17: "九小月", 18: "电子馒头", 19: "小品艺术家",
            20: "唐山猴哥", 21: "娇羞猴哥", 22: "ASMR男声",
            23: "直率英子", 24: "八戒", 25: "紫薇说唱",
            26: "威猛震天", 27: "黛玉", 28: "动漫小新", 29: "顾姐",
            30: "容嬷嬷", 31: "华妃", 32: "米老哥", 33: "动漫海绵",
            34: "春日部姐姐", 35: "美小羊", 36: "神厨老佛爷",
            37: "周天王", 38: "暗夜萝莉", 39: "宝宝冯",
            40: "宝青狐妖", 41: "东厂公公", 42: "蜡笔小妮",
            43: "樱桃爷爷", 44: "龅牙珍", 45: "派星星",
            46: "章鱼哥哥", 47: "唐小鸭", 48: "佩奇猪",
            49: "东北能哥", 50: "樱桃丸子", 51: "女儿国王",
            52: "天线宝宝", 53: "小青", 54: "和大人", 55: "商务殷语",
            56: "王小也", 57: "林萧", 58: "小魔童", 59: "大耳小图",
            60: "低吼男生", 61: "娱乐扒妹", 62: "温婉女声",
            63: "朗诵男声", 64: "沉稳男声", 65: "刚毅男子",
            66: "磁性男声", 67: "清凉男声Pro", 68: "乒乓解说",
            69: "纪录片解说", 70: "新闻男主播", 71: "沉稳解说",
            72: "抑制片男2", 73: "活力解释", 74: "娱乐播报",
            75: "清凉女声", 76: "正气男声", 77: "辣评娱记",
            78: "科普男声", 79: "深情诉说", 80: "雅痞大叔",
            81: "亲爽男声", 82: "百科解说", 83: "扒小编",
            84: "新闻女主播2", 85: "赛事解说", 86: "宣传男声",
            87: "娱乐播报", 88: "悬疑解说", 89: "舌尖解说",
            90: "广告男音", 91: "童话解说", 92: "讲解男声",
            93: "刘语熙", 94: "心灵鸡汤", 95: "新闻女声",
            96: "知识讲解", 97: "八卦娱记2", 98: "新闻锐探",
            99: "温和女声", 100: "萌娃百科", 101: "电竞解说",
            102: "游戏解说", 103: "动漫解说", 104: "网文解说",
            105: "清润女声", 106: "安全哥", 107: "新闻男生",
            108: "甜妹解说", 109: "娱乐扒妹", 110: "抑制片男",
            111: "解说小帅", 112: "声控弟弟", 113: "恐怖电影",
            114: "北京小爷", 115: "懒小羊", 116: "霸道彻总",
            117: "清甜女声", 118: "低哑男声", 119: "英勇将军",
            120: "机灵少年", 121: "高冷男声", 122: "乖软童声",
            123: "萌娃", 124: "沉稳男声", 125: "元气少女",
            126: "乖巧女孩", 127: "任性女友", 128: "嚣张男子",
            129: "奶萌男宝", 130: "暴躁男", 131: "慈爱姥爷",
            132: "任性少女", 133: "乖巧萝莉", 134: "爽朗大姐",
            135: "幽默老哥", 136: "老爷子", 137: "老婆婆",
            138: "创世女神", 139: "柔弱男子", 140: "稚气少女",
            141: "撒娇学妹", 142: "温软少女", 143: "冰山霸总",
            144: "少女甜嗓", 145: "严厉老太", 146: "正义小英雄",
            147: "英勇武将", 148: "热血战士", 149: "清亮男声Pro", 150: "娇弱女",
            151: "粗旷男子", 152: "硬朗大爷", 153: "可爱女生", 154: "和善哥哥",
            155: "病弱少女", 156: "病娇少女", 157: "甜美软妹",
            158: "冷冽总裁", 159: "冷漠厌世女", 160: "稳重学长",
            161: "稳重男声", 162: "活泼女孩", 163: "冷酷哥哥",
            164: "温柔姐姐", 165: "调皮公主", 166: "霸气帝王",
            167: "严肃大叔", 168: "渊博小叔", 169: "松弛男声",
            170: "随侍太监", 171: "和蔼奶奶", 172: "洒脱学姐",
            173: "阳光青年", 174: "阳光少年", 175: "强势大佬",
            176: "奶萌少女", 177: "智慧老者", 178: "錘子哥",
            179: "幵朗学長", 180: "正乂勇士", 181: "平万大叔",
            182: "甜軟萌妹", 183: "逗趣男声", 184: "侠客",
            185: "直爽女大", 186: "温柔女友", 187: "清爽男大",
            188: "魅力女友", 189: "冰山捜男", 190: "豪放男子",
            191: "自信男声", 192: "心机御姐", 193: "柔美女友",
            194: "乖巧男孩", 195: "酷捜女", 196: "剛毅男子",
            197: "正乂女使者", 198: "和藹苓苓", 199: "御前公公",
            200: "武則", 201: "錘子哥", 202: "正能量姐姐",
            203: "沉稳大叔", 204: "亲切阿姨", 205: "女少侠",
            206: "俊朗男友", 207: "正直哥哥", 208: "暖心学姐",
            209: "天真萌宝", 210: "文静学姐", 211: "温婉姐姐",
            212: "天真妹妹", 213: "爽朗男音", 214: "亲切伯伯",
            215: "爽朗男声", 216: "优柔公子", 217: "随性男声",
            218: "亲切婶婶", 219: "爽朗男友", 220: "腹黑小姨",
            221: "爽朗学长", 222: "邻居阿姨", 223: "和善婶婶",
            224: "康定情歌", 225: "做作夹子音", 226: "娱乐扒妹II", 227: "顾姐",
            228: "新闻女主播", 229: "亲切女声", 230: "甜美解说", 231: "知性女声",
            232: "英语女王", 233: "玲玲姐姐", 234: "小萝莉", 235: "官方客服",
            236: "生活主播", 237: "情感语录", 238: "温柔淑女",
            239: "随性女声", 240: "少儿故事", 241: "生活小妙招",
            242: "旅游资讯", 243: "ASMR女声", 244: "吴敏霞",
            245: "少儿百科", 246: "星闻锐探", 247: "直播一姐",
            248: "清亮女声", 249: "谄媚女声", 250: "军事解说",
            251: "女雷神", 252: "奶音欣欣子", 253: "活力解说",
            254: "语音助手", 255: "龅牙珍珍", 256: "厄运少女",
            257: "文艺女声", 258: "AI旁白", 259: "甜美悦悦",
            260: "强势妹", 261: "酷拽女", 262: "清冷女声",
            263: "正义女使者", 264: "电台广播", 265: "钓系美女", 266: "硬妹",
            267: "感冒电音", 268: "武则天", 269: "理智姐", 270: "林潇",
            271: "傲娇大小姐", 272: "垪解旁白", 273: "磁性男声", 274: "电视广告",
            275: "霸道总裁", 276: "逢制片男", 277: "解说小仲",
            278: "阳光男生", 279: "古凡男主", 280: "慈爰姥爷",
            281: "葛大爷", 282: "新闻男声", 283: "清爽男声",
            284: "广告男声", 285: "游戏解说男", 286: "恐怖电影", 287: "播音旁白",
            288: "茶啊王强", 289: "译制片男II", 290: "唐少", 291: "温柔男主",
            292: "抱怨男声", 293: "开朗弟弟", 294: "渊博小叔", 295: "和蔼爷爷",
            296: "有声小说", 297: "翩翩公子", 298: "单口相声",
            299: "清亮男声", 300: "傲娇男声", 301: "严厉大叔",
            302: "柜哥", 303: "开朗学长", 304: "正义勇士",
            305: "快板", 306: "促销男声", 307: "锤子哥",
            308: "港配男声", 309: "文艺男声", 310: "质感男声",
            311: "广告男声2", 312: "温柔男声", 313: "冰山拽男",
            314: "京腔小爷", 315: "广州德哥", 316: "豆蔻少女",
            317: "综艺萝莉", 318: "甜软萌妹", 319: "樱花小哥",
            320: "幺妹", 321: "天津姐姐", 322: "京腔", 323: "大丫",
            324: "湘普甜甜", 325: "东北老铁", 326: "广西表哥",
            327: "台湾男生", 328: "西安掌柜", 329: "台湾女生",
            330: "青岛小哥", 331: "靓女", 332: "潮汕大叔",
            333: "上海阿姨", 334: "广普", 335: "港普男声2",
            336: "川妹子", 337: "重庆小伙", 338: "河南大叔",
            339: "粤语男声", 340: "天津小哥", 341: "港普男声",
            342: "太白", 343: "狐狸姐姐", 344: "温迪迪",
            345: "太乙", 346: "鲁班八号", 347: "说唱小哥",
            348: "康康舞曲", 349: "甜美女孩", 350: "水果舞曲",
            351: "歌唱达人", 352: "春日甜妹", 353: "春节甜妹",
            354: "歌唱女王", 355: "情歌王", 356: "摇滚男生",
            357: "清新歌手", 358: "激扬男声", 359: "小爱同学",
            360: "雷军", 361: "黑神话悟空", 362: "萧炎",
            363: "石昊", 364: "蔡徐坤", 365: "丁真",
            366: "豆包-温柔姐姐", 367: "豆包-幼教姐姐", 368: "豆包-阳光甜妹",
            369: "豆包-成熟姐姐", 370: "小何", 371: "小阿姨",
            372: "验证码", 373: "叶修", 374: "晶宝",
            375: "诗雅", 376: "吉高里", 377: "黑神话杨戬",
            378: "奶酷小宇", 379: "沉稳皓轩", 380: "暖阳阿晨",
            381: "低音小北", 382: "男闺蜜俊熙", 383: "沪上傲女",
            384: "多情阔少", 385: "霸道总裁", 386: "妩媚御姐",
            387: "冷酷哥哥", 388: "活泼女孩", 389: "甜心小美",
            390: "可爱女生", 391: "成熟姐姐", 392: "恶魔女仆",
            393: "小丑", 394: "蛇发女妖", 395: "小精灵",
            396: "奶气萌娃", 397: "魔法小黑", 398: "白骨精",
            399: "病弱少女", 400: "孤傲公子", 401: "刘语熙",
            402: "黄健翔", 403: "调皮公主", 404: "潇洒诗人",
        }
    
    def generate_menu_image(self) -> bytes:
        """生成音色菜单图片"""
        if not PIL_AVAILABLE:
            logger.error("PIL库不可用，无法生成图片")
            return None

        try:
            # 图片尺寸设置
            img_width = 800
            items_per_row = 4
            item_height = 40
            padding = 20
            header_height = 80
            
            # 计算行数
            total_items = len(self.voice_list)
            rows = (total_items + items_per_row - 1) // items_per_row
            img_height = header_height + rows * item_height + padding * 2
            
            # 创建图片
            img = Image.new('RGB', (img_width, img_height), color='#f0f8ff')
            draw = ImageDraw.Draw(img)
            
            # 尝试加载支持中文的字体
            title_font = None
            item_font = None

            # 支持中文的字体路径（按优先级排序）
            import platform
            system = platform.system()

            font_paths = []

            if system == "Windows":
                # Windows系统中常见的中文字体路径
                font_paths = [
                    # 微软雅黑（推荐）
                    "C:/Windows/Fonts/msyh.ttc",
                    "C:/Windows/Fonts/msyh.ttf",
                    "msyh.ttc",
                    "msyh.ttf",
                    # 宋体
                    "C:/Windows/Fonts/simsun.ttc",
                    "C:/Windows/Fonts/simsun.ttf",
                    "simsun.ttc",
                    "simsun.ttf",
                    # 黑体
                    "C:/Windows/Fonts/simhei.ttf",
                    "simhei.ttf",
                    # 楷体
                    "C:/Windows/Fonts/simkai.ttf",
                    "simkai.ttf",
                    # Arial Unicode MS (支持中文)
                    "C:/Windows/Fonts/ARIALUNI.TTF",
                    "ARIALUNI.TTF"
                ]
            elif system == "Darwin":  # macOS
                font_paths = [
                    # macOS中文字体
                    "/System/Library/Fonts/PingFang.ttc",
                    "/System/Library/Fonts/Hiragino Sans GB.ttc",
                    "/System/Library/Fonts/STHeiti Light.ttc",
                    "/System/Library/Fonts/STHeiti Medium.ttc",
                    "PingFang.ttc",
                    "Hiragino Sans GB.ttc"
                ]
            else:  # Linux和其他系统
                font_paths = [
                    # Linux中文字体
                    "/usr/share/fonts/truetype/droid/DroidSansFallbackFull.ttf",
                    "/usr/share/fonts/truetype/wqy/wqy-microhei.ttc",
                    "/usr/share/fonts/truetype/wqy/wqy-zenhei.ttc",
                    "/usr/share/fonts/opentype/noto/NotoSansCJK-Regular.ttc",
                    "/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf",
                    "DroidSansFallbackFull.ttf",
                    "wqy-microhei.ttc",
                    "wqy-zenhei.ttc"
                ]

            # 尝试加载字体
            for font_path in font_paths:
                try:
                    title_font = ImageFont.truetype(font_path, 24)
                    item_font = ImageFont.truetype(font_path, 14)
                    logger.info(f"成功加载字体: {font_path}")
                    break
                except Exception as e:
                    logger.debug(f"无法加载字体 {font_path}: {e}")
                    continue

            # 如果所有字体都加载失败，尝试使用默认字体
            if title_font is None or item_font is None:
                try:
                    title_font = ImageFont.load_default()
                    item_font = ImageFont.load_default()
                    logger.warning("使用默认字体，可能无法正确显示中文")
                except Exception as e:
                    logger.error(f"加载默认字体失败: {e}")
                    title_font = None
                    item_font = None
            
            # 绘制标题
            title = "🎵 文字转语音音色列表 🎵"
            if title_font:
                title_bbox = draw.textbbox((0, 0), title, font=title_font)
                title_width = title_bbox[2] - title_bbox[0]
                title_x = (img_width - title_width) // 2
                draw.text((title_x, 20), title, fill='#2c3e50', font=title_font)
            else:
                draw.text((img_width//2 - 100, 20), title, fill='#2c3e50')
            
            # 绘制音色列表
            current_row = 0
            current_col = 0
            
            for voice_id, voice_name in self.voice_list.items():
                x = padding + current_col * (img_width - 2 * padding) // items_per_row
                y = header_height + current_row * item_height
                
                # 绘制背景框
                box_width = (img_width - 2 * padding) // items_per_row - 10
                draw.rectangle([x, y, x + box_width, y + item_height - 5], 
                             fill='#ffffff', outline='#bdc3c7', width=1)
                
                # 绘制文本
                text = f"{voice_id}:{voice_name}"
                if item_font:
                    draw.text((x + 5, y + 10), text, fill='#34495e', font=item_font)
                else:
                    draw.text((x + 5, y + 10), text, fill='#34495e')
                
                current_col += 1
                if current_col >= items_per_row:
                    current_col = 0
                    current_row += 1
            
            # 添加使用说明
            usage_text = "使用方法：发送 '语音<音色ID或名称>#<要转换的文字>' 或引用消息回复 '转<音色ID或名称>'"
            if item_font:
                draw.text((padding, img_height - 30), usage_text, fill='#7f8c8d', font=item_font)
            else:
                draw.text((padding, img_height - 30), usage_text, fill='#7f8c8d')
            
            # 转换为字节
            img_bytes = io.BytesIO()
            img.save(img_bytes, format='PNG', quality=95)
            img_bytes.seek(0)
            
            logger.info("音色菜单图片生成成功")
            return img_bytes.getvalue()
            
        except Exception as e:
            logger.error(f"生成音色菜单图片失败: {e}")
            return None
    
    def get_voice_id_by_name(self, name: str) -> int:
        """根据音色名称获取音色ID"""
        name = name.strip()
        
        # 如果是数字，直接返回
        if name.isdigit():
            voice_id = int(name)
            if voice_id in self.voice_list:
                return voice_id
        
        # 根据名称查找
        for voice_id, voice_name in self.voice_list.items():
            if voice_name == name:
                return voice_id
        
        return None
    
    def get_voice_name_by_id(self, voice_id: int) -> str:
        """根据音色ID获取音色名称"""
        return self.voice_list.get(voice_id, None)
