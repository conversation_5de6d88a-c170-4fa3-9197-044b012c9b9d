# DoubaoChat 豆包AI对话插件

## 插件简介

艹，老王我把原来的DoubaoChat插件完整移植过来了！这个插件实现了与字节跳动豆包AI的对话功能，支持连续对话模式，让你的微信机器人变得更智能。

## 主要功能

- **豆包AI对话**：与豆包AI进行智能对话
- **连续对话模式**：支持进入连续对话模式，无需重复输入命令
- **频率限制**：防止用户频繁请求，保护API稳定性
- **关键词过滤**：避免与其他插件功能冲突
- **错误处理**：完善的错误处理和用户提示

## 安装配置

### 1. 获取豆包Cookies

1. 打开浏览器，访问 [豆包官网](https://www.doubao.com/)
2. 登录你的豆包账号
3. 按F12打开开发者工具
4. 切换到Network（网络）标签
5. 在豆包页面发送一条消息
6. 在Network中找到请求，复制完整的Cookie值

### 2. 配置插件

编辑 `plugins/DoubaoChat/config.toml` 文件：

```toml
[basic]
# 插件启用状态
enable = true

[doubao]
# 豆包API配置 - 必须填写！
cookies = "你的豆包cookies"  # 从浏览器复制的完整cookies

# 触发关键词配置
keywords = ["豆包"]  # 可以添加更多触发词

# 会话模式配置
conversation_timeout = 300  # 会话模式超时时间（秒）
min_request_interval = 2.0  # 最小请求间隔（秒）
```

### 3. 重启机器人

配置完成后重启微信机器人，插件即可生效。

## 使用方法

### 基本对话

发送消息格式：`豆包#你的问题`

示例：
- `豆包#你好`
- `豆包#今天天气怎么样`
- `豆包#帮我写一段Python代码`

### 连续对话模式

1. **进入连续对话模式**：
   ```
   豆包#对话
   ```

2. **在连续对话模式中**：
   - 直接发送消息即可，无需再输入"豆包#"
   - 例如：`你好` → 豆包回复
   - 例如：`帮我解释一下Python` → 豆包回复

3. **退出连续对话模式**：
   ```
   结束
   ```
   或
   ```
   豆包 结束
   ```

### 使用示例

```
用户: 豆包#你好
豆包: 你好！我是豆包，很高兴为你服务！

用户: 豆包#对话
豆包: 已进入连续对话模式，直接发送消息即可对话，发送'结束'退出

用户: 帮我写一个Python函数
豆包: 好的，我来帮你写一个Python函数...

用户: 结束
豆包: 已退出连续对话模式
```

## 配置说明

### 基本配置

| 配置项 | 说明 | 默认值 | 必填 |
|--------|------|--------|------|
| `enable` | 是否启用插件 | `true` | 是 |
| `cookies` | 豆包网站cookies | `""` | 是 |

### 高级配置

| 配置项 | 说明 | 默认值 |
|--------|------|--------|
| `keywords` | 触发关键词列表 | `["豆包"]` |
| `conversation_timeout` | 连续对话超时时间（秒） | `300` |
| `min_request_interval` | 最小请求间隔（秒） | `2.0` |
| `exclude_keywords` | 排除关键词（避免冲突） | 见配置文件 |

### API配置（一般不需要修改）

| 配置项 | 说明 | 默认值 |
|--------|------|--------|
| `device_id` | 设备ID | `"7468716989062841895"` |
| `tea_uuid` | Tea UUID | `"7468716986638386703"` |
| `web_id` | Web ID | `"7468716986638386703"` |
| `api_base_url` | API基础URL | `"https://www.doubao.com/samantha/chat/completion"` |

## 注意事项

1. **Cookies必须有效**：请确保从豆包官网获取的cookies是有效的
2. **请求频率**：插件内置了请求频率限制，避免过于频繁的请求
3. **网络环境**：确保服务器能够访问豆包官网
4. **关键词冲突**：插件已配置排除关键词，避免与画图、视频等插件冲突

## 故障排除

### 常见问题

1. **插件不响应**
   - 检查 `enable` 是否为 `true`
   - 检查cookies是否正确配置
   - 查看日志是否有错误信息

2. **API调用失败**
   - 检查网络连接
   - 检查cookies是否过期
   - 重新获取cookies

3. **频率限制提示**
   - 等待指定时间后再试
   - 可以调整 `min_request_interval` 配置

### 日志查看

插件运行时会输出详细日志，可以通过日志排查问题：

```
[INFO] DoubaoChat处理消息: 豆包#你好
[INFO] [DoubaoChat] API调用成功
[ERROR] [DoubaoChat] API请求失败: 状态码=401
```

## 更新日志

### v1.0.0
- 完整移植DoubaoChat功能
- 支持基本对话和连续对话模式
- 添加频率限制和错误处理
- 完善配置文件和文档

## 技术支持

如果遇到问题，可以：
1. 查看插件日志
2. 检查配置文件
3. 确认网络连接
4. 重新获取cookies

---

**老王提醒**：这个插件移植得很完整，功能和原版一模一样，配置好cookies就能用了！有问题就看日志，别来烦老王我！
