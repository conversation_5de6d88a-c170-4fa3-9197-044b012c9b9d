from loguru import logger
import tomllib
import os
import json
import time
import uuid
import httpx
import asyncio
from typing import Optional, Dict, Any
from urllib.parse import quote

from WechatAPI import WechatAPIClient
from utils.decorators import *
from utils.plugin_base import PluginBase


class HttpResponse:
    """HTTP响应类，用于兼容旧插件代码"""
    def __init__(self, status_code, body, headers=None):
        self.code = status_code
        self.status_code = status_code
        self.body = body
        self.headers = headers or {}
        self._text = None

    def json(self):
        """解析 JSON 响应体"""
        if not self.body:
            return {}
        try:
            return json.loads(self.body)
        except (json.JSONDecodeError, TypeError):
            return {}


class DoubaoChat(PluginBase):
    """豆包AI对话插件 - 从DoubaoChat完整移植"""
    description = "豆包AI对话插件，支持连续对话模式"
    author = "老王移植版"
    version = "1.0.0"

    def __init__(self):
        super().__init__()
        
        # 获取配置文件路径
        config_path = os.path.join(os.path.dirname(__file__), "config.toml")
        
        try:
            with open(config_path, "rb") as f:
                config = tomllib.load(f)
                
            # 读取基本配置
            basic_config = config.get("basic", {})
            self.enable = basic_config.get("enable", False)
            
            # 读取豆包配置
            doubao_config = config.get("doubao", {})
            self.cookies = doubao_config.get("cookies", "")
            self.keywords = doubao_config.get("keywords", ["豆包"])
            self.conversation_timeout = doubao_config.get("conversation_timeout", 300)
            self.min_request_interval = doubao_config.get("min_request_interval", 2.0)
            self.exclude_keywords = doubao_config.get("exclude_keywords", [])
            self.max_history_messages = doubao_config.get("max_history_messages", 20)
            
            # 豆包API配置
            self.api_base_url = doubao_config.get("api_base_url", "https://www.doubao.com/samantha/chat/completion")
            self.device_id = doubao_config.get("device_id", "7468716989062841895")
            self.tea_uuid = doubao_config.get("tea_uuid", "7468716986638386703")
            self.web_id = doubao_config.get("web_id", "7468716986638386703")
            
        except Exception as e:
            logger.error(f"加载DoubaoChat配置文件失败: {str(e)}")
            self.enable = False
            
        # 会话模式配置
        self._conversation_mode = {}  # 群组ID -> 是否在会话模式
        self._conversation_users = {}  # 群组ID -> 用户ID
        self._last_conversation_time = {}  # 群组ID -> 最后会话时间

        # 会话记忆管理 - 参考DoubaoWebChat实现
        self._conversation_sessions = {}  # 会话ID -> 消息历史

        # httpx客户端管理
        self._client = None
        self._client_lock = asyncio.Lock()

        # 用户级别的请求限制
        self._user_limits = {}

    async def async_init(self):
        """异步初始化"""
        return

    async def get_httpx_client(self):
        """创建httpx客户端"""
        return httpx.AsyncClient(
            timeout=httpx.Timeout(connect=10.0, read=20.0, write=20.0, pool=5.0),
            verify=False,  # 禁用SSL验证
            follow_redirects=True,
            limits=httpx.Limits(max_connections=20, max_keepalive_connections=10),
        )

    async def get_session(self):
        """获取或创建httpx异步客户端 - 参考DoubaoWebChat，每次创建新客户端避免连接池问题"""
        # 不再复用客户端，每次创建新的，避免连接池超时问题
        return await self.get_httpx_client()

    async def close_session(self):
        """关闭httpx客户端"""
        async with self._client_lock:
            if self._client and not self._client.is_closed:
                await self._client.aclose()
                self._client = None

    def _check_user_limit(self, target_id: str, user_id: str) -> float:
        """检查用户请求限制，返回需要等待的时间"""
        current_time = time.time()
        user_key = f"{target_id}_{user_id}"
        
        if user_key in self._user_limits:
            last_request_time = self._user_limits[user_key]
            time_diff = current_time - last_request_time
            
            if time_diff < self.min_request_interval:
                return self.min_request_interval - time_diff
        
        self._user_limits[user_key] = current_time
        return 0

    def _generate_headers(self) -> Dict[str, str]:
        """生成请求头 - 参考DoubaoWebChat的完整实现"""
        # 生成随机的x-flow-trace，豆包用这个追踪请求
        flow_trace = f"04-{uuid.uuid4().hex[:16]}-{uuid.uuid4().hex[:16]}-01"

        return {
            "Accept": "*/*",
            "Accept-Encoding": "gzip, deflate",
            "Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
            "Connection": "keep-alive",
            "Content-Type": "application/json; charset=utf-8",
            "Cookie": self.cookies,
            "Host": "www.doubao.com",
            "Origin": "https://www.doubao.com",
            "Referer": "https://www.doubao.com/chat/",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "x-flow-trace": flow_trace,
            "Agw-Js-Conv": "str",
            "X-Requested-With": "mark.via",
            "last-event-id": "undefined",
            "Sec-Fetch-Site": "same-origin",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Dest": "empty"
        }

    def _is_in_conversation_mode(self, target_id: str, user_id: str) -> bool:
        """检查是否在会话模式"""
        if target_id not in self._conversation_mode:
            return False
            
        # 检查超时
        current_time = time.time()
        last_time = self._last_conversation_time.get(target_id, 0)
        if current_time - last_time > self.conversation_timeout:
            # 超时，自动退出会话模式
            self._exit_conversation_mode_internal(target_id)
            return False
            
        # 检查用户是否匹配
        return (self._conversation_mode.get(target_id, False) and 
                self._conversation_users.get(target_id) == user_id)

    async def _enter_conversation_mode(self, target_id: str, user_id: str):
        """进入会话模式"""
        self._conversation_mode[target_id] = True
        self._conversation_users[target_id] = user_id
        self._last_conversation_time[target_id] = time.time()

    async def _exit_conversation_mode(self, target_id: str):
        """退出会话模式"""
        self._exit_conversation_mode_internal(target_id)

    def _exit_conversation_mode_internal(self, target_id: str):
        """内部退出会话模式"""
        # 获取用户ID并清除会话历史
        user_id = self._conversation_users.get(target_id)
        if user_id:
            self._clear_session_history(target_id, user_id)

        self._conversation_mode.pop(target_id, None)
        self._conversation_users.pop(target_id, None)
        self._last_conversation_time.pop(target_id, None)

    async def _handle_conversation_message(self, bot: WechatAPIClient, message: dict, content: str):
        """处理会话消息"""
        # 更新最后会话时间
        target_id = self._get_target_id(message)
        self._last_conversation_time[target_id] = time.time()
        
        # 处理对话
        await self._handle_chat_request(bot, message, content)

    def _get_target_id(self, message: dict) -> str:
        """获取目标ID（群聊ID或私聊用户ID）"""
        if message.get("IsGroup", False):
            return message.get("FromWxid", "")
        else:
            return message.get("SenderWxid", message.get("FromWxid", ""))

    def _get_sender_id(self, message: dict) -> str:
        """获取发送者ID"""
        return message.get("SenderWxid", message.get("FromWxid", ""))

    async def _handle_chat_request(self, bot: WechatAPIClient, message: dict, query: str):
        """处理对话请求"""
        try:
            target_id = self._get_target_id(message)
            sender_id = self._get_sender_id(message)

            # 检查用户限制
            wait_time = self._check_user_limit(target_id, sender_id)
            if wait_time > 0:
                await bot.send_text_message(target_id, f"请求过于频繁，请等待 {wait_time:.1f} 秒后再试")
                return

            # 添加用户消息到会话历史
            self._add_message_to_session(target_id, sender_id, "user", query)

            # 调用豆包API（传入会话历史）
            result = await self.call_doubao_api_with_history(target_id, sender_id, query)

            logger.info(f"[DoubaoChat] API返回结果: {result}")

            if result and result.get("type") == "text":
                text = result.get("text", "")
                logger.info(f"[DoubaoChat] 提取到文本: '{text}'")
                if text:
                    # 添加AI回复到会话历史
                    self._add_message_to_session(target_id, sender_id, "assistant", text)
                    await bot.send_text_message(target_id, text)
                    logger.info(f"[DoubaoChat] 已发送回复")
                else:
                    logger.warning(f"[DoubaoChat] 文本为空")
            else:
                logger.warning(f"[DoubaoChat] 结果格式异常: {result}")

        except Exception as e:
            logger.error(f"处理对话请求失败: {e}", exc_info=True)
            await bot.send_text_message(target_id, "抱歉，处理请求时出现错误，请稍后再试")

    def _extract_query(self, content: str) -> str:
        """提取查询内容"""
        for keyword in self.keywords:
            if content.startswith(keyword):
                return content[len(keyword):].strip()
        return content.strip()

    def _should_exclude_query(self, query: str) -> bool:
        """检查查询是否包含排除关键词"""
        query_lower = query.lower()
        for exclude_keyword in self.exclude_keywords:
            if exclude_keyword.lower() in query_lower:
                return True
        return False

    def _get_session_key(self, target_id: str, user_id: str) -> str:
        """生成会话键"""
        return f"{target_id}_{user_id}"

    def _add_message_to_session(self, target_id: str, user_id: str, role: str, content: str):
        """添加消息到会话历史"""
        session_key = self._get_session_key(target_id, user_id)

        if session_key not in self._conversation_sessions:
            self._conversation_sessions[session_key] = []

        # 添加消息
        message = {
            "role": role,
            "content": content,
            "timestamp": time.time()
        }
        self._conversation_sessions[session_key].append(message)

        # 限制历史记录长度
        if len(self._conversation_sessions[session_key]) > self.max_history_messages:
            self._conversation_sessions[session_key] = self._conversation_sessions[session_key][-self.max_history_messages:]

    def _get_session_history(self, target_id: str, user_id: str) -> list:
        """获取会话历史"""
        session_key = self._get_session_key(target_id, user_id)
        return self._conversation_sessions.get(session_key, [])

    def _clear_session_history(self, target_id: str, user_id: str):
        """清除会话历史"""
        session_key = self._get_session_key(target_id, user_id)
        if session_key in self._conversation_sessions:
            del self._conversation_sessions[session_key]

    @on_text_message(priority=50)
    async def handle_text(self, bot: WechatAPIClient, message: dict):
        """处理文本消息"""
        try:
            # 如果插件未启用，直接返回
            if not self.enable:
                return
                
            content = message.get("Content", "").strip()
            target_id = self._get_target_id(message)
            sender_id = self._get_sender_id(message)
            
            # 检查会话模式
            if self._is_in_conversation_mode(target_id, sender_id):
                # 在会话模式中，处理所有消息
                if content == "结束" or content == "豆包 结束" or content == "拜拜" or content == "退下":
                    await self._exit_conversation_mode(target_id)
                    await bot.send_text_message(target_id, "已退出连续对话模式")
                    return
                else:
                    # 处理会话消息
                    await self._handle_conversation_message(bot, message, content)
                    return
            
            # 检查关键词匹配：豆包+#开头
            if not content.startswith("豆包"):
                return

            # 提取豆包后的内容
            after_doubao = content[2:].strip()  # 移除"豆包"
            if not after_doubao.startswith("#"):
                return

            # 提取实际查询内容，必须有提示词
            query = after_doubao[1:].strip()  # 移除"#"
            if not query:
                return

            # 检查排除关键词（避免与其他插件冲突）
            if self._should_exclude_query(query):
                logger.debug(f"DoubaoChat跳过排除关键词: {query}")
                return

            logger.info(f"DoubaoChat处理消息: {content}")

            # 检查特殊命令
            if query == "对话":
                await self._enter_conversation_mode(target_id, sender_id)
                await bot.send_text_message(target_id, "已进入连续对话模式，直接发送消息即可对话，发送'结束'退出")
                return

            # 处理普通对话
            await self._handle_chat_request(bot, message, query)
            
        except Exception as e:
            logger.error(f"DoubaoChat处理消息时出错: {e}", exc_info=True)

    async def call_doubao_api_with_history(self, target_id: str, user_id: str, query: str) -> Optional[Dict[str, Any]]:
        """调用豆包API（带会话历史）"""
        # 获取会话历史
        history = self._get_session_history(target_id, user_id)

        # 构建消息列表
        messages = []
        for msg in history[:-1]:  # 排除刚添加的当前消息
            if msg["role"] == "user":
                messages.append({
                    "content": json.dumps({"text": msg["content"]}),
                    "content_type": 2001,
                    "attachments": [],
                    "references": []
                })
            elif msg["role"] == "assistant":
                messages.append({
                    "content": json.dumps({"text": msg["content"]}),
                    "content_type": 2001,
                    "attachments": [],
                    "references": []
                })

        # 添加当前消息
        messages.append({
            "content": json.dumps({"text": query}),
            "content_type": 2001,
            "attachments": [],
            "references": []
        })

        return await self._call_doubao_api_internal(messages)

    async def call_doubao_api(self, query: str) -> Optional[Dict[str, Any]]:
        """调用豆包API（单次对话，无历史记录）"""
        messages = [{
            "content": json.dumps({"text": query}),
            "content_type": 2001,
            "attachments": [],
            "references": []
        }]
        return await self._call_doubao_api_internal(messages)

    async def _call_doubao_api_internal(self, messages: list) -> Optional[Dict[str, Any]]:
        """内部API调用方法"""
        try:
            # 构造请求URL参数
            params = {
                "aid": "497858",
                "device_id": self.device_id,
                "device_platform": "web",
                "language": "zh",
                "pc_version": "2.16.4",
                "pkg_type": "release_version",
                "real_aid": "497858",
                "region": "CN",
                "samantha_web": "1",
                "sys_region": "CN",
                "tea_uuid": self.tea_uuid,
                "use-olympus-account": "1",
                "version_code": "20800",
                "web_id": self.web_id
            }

            # 创建随机的会话ID和消息ID
            conversation_id = f"38{int(time.time() * 10000)}8"
            section_id = f"{conversation_id[:10]}4{conversation_id[10:]}"
            message_id = f"ba0d6520-{uuid.uuid4().hex[:4]}-11f0-a188-0d4d{uuid.uuid4().hex[:8]}"

            # 构造请求数据
            request_data = {
                "messages": messages,
                "completion_option": {
                    "is_regen": False,
                    "with_suggest": True,
                    "need_create_conversation": True,
                    "launch_stage": 1,
                    "is_replace": False,
                    "is_delete": False,
                    "message_from": 0,
                    "use_deep_think": False,
                    "use_auto_cot": False,
                    "event_id": "0"
                },
                "section_id": section_id,
                "conversation_id": conversation_id,
                "local_message_id": message_id
            }

            # 获取httpx客户端
            client = await self.get_session()

            try:
                # 设置请求头
                headers = self._generate_headers()

                # 构造完整URL
                url = f"{self.api_base_url}?" + "&".join([f"{k}={quote(str(v))}" for k,v in params.items()])

                logger.info(f"[DoubaoChat] 发送豆包API请求，消息数量: {len(messages)}")

                # 发送POST请求
                response = await client.post(
                    url,
                    json=request_data,
                    headers=headers,
                    timeout=httpx.Timeout(connect=10.0, read=300.0, write=60.0, pool=30.0)
                )

                if response.status_code != 200:
                    error_text = response.content.decode('utf-8', errors='ignore') if response.content else "无响应内容"
                    logger.error(f"[DoubaoChat] API请求失败: 状态码={response.status_code}")
                    logger.error(f"[DoubaoChat] 响应内容: {error_text[:500]}...")
                    return {"type": "text", "text": f"API请求失败，状态码: {response.status_code}"}

                # 处理响应
                logger.info(f"[DoubaoChat] 开始处理响应，状态码: {response.status_code}")
                result = await self._process_stream_response(response.content)
                logger.info(f"[DoubaoChat] 响应处理完成: {result}")

                if result:
                    return result

                return {"type": "text", "text": "抱歉，没有收到有效响应"}

            finally:
                # 确保每次请求后都关闭客户端，避免连接池问题
                if client and not client.is_closed:
                    await client.aclose()

        except Exception as e:
            logger.error(f"[DoubaoChat] API调用失败: {e}")
            return {"type": "text", "text": "抱歉，豆包服务暂时不可用，请稍后再试"}

    async def _process_stream_response(self, response_body) -> Optional[Dict[str, Any]]:
        """处理SSE响应流 - 参考DoubaoWebChat的实现"""
        result_text = ""
        result_data = {"type": "text", "text": ""}

        try:
            # 解码响应数据
            if isinstance(response_body, str):
                chunk = response_body.encode('utf-8')
            else:
                chunk = response_body

            try:
                decoded_chunk = chunk.decode('utf-8', errors='ignore')
                buffer = decoded_chunk
            except UnicodeDecodeError:
                logger.error("[DoubaoChat] 响应解码失败")
                return None

            # 处理完整的SSE事件 - 参考DoubaoWebChat的实现
            while "\n\n" in buffer:
                parts = buffer.split("\n\n", 1)
                event = parts[0]
                buffer = parts[1]

                if not event.strip():
                    continue

                # 提取"data:"行
                data_line = None
                for line in event.split("\n"):
                    if line.startswith("data:"):
                        data_line = line[5:].strip()
                        break

                if not data_line:
                    continue

                # 解析数据
                try:
                    event_data = json.loads(data_line)
                    if not isinstance(event_data, dict):
                        continue

                    if "event_type" not in event_data:
                        continue

                    event_type = event_data["event_type"]

                    # 处理结束事件
                    if event_type == 2003:
                        if "tts_content" in event_data:
                            full_text = event_data["tts_content"]
                            if full_text and len(full_text) > len(result_text):
                                result_text = full_text
                                result_data["text"] = result_text

                        result_data["text"] = result_text
                        return result_data

                    # 处理正常消息事件
                    if event_type == 2001 and "event_data" in event_data:
                        try:
                            inner_data = json.loads(event_data["event_data"])

                            if "message" not in inner_data:
                                continue

                            message = inner_data["message"]

                            if "content_type" not in message or "content" not in message:
                                continue

                            content_type = message["content_type"]
                            if content_type == 2001:  # 文本消息
                                try:
                                    content_obj = json.loads(message["content"])
                                    if "text" in content_obj:
                                        text = content_obj["text"]
                                        if text:
                                            result_text += text
                                except:
                                    pass

                        except json.JSONDecodeError:
                            continue

                except json.JSONDecodeError:
                    continue

            # 返回结果
            if result_text.strip():
                result_data["text"] = result_text.strip()
                return result_data
            else:
                return {"type": "text", "text": "豆包没有返回有效回复"}

        except Exception as e:
            logger.error(f"处理豆包响应流时出错: {e}")
            return {"type": "text", "text": "处理回复时出现错误"}
