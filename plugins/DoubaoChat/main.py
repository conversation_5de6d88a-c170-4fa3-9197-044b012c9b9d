from loguru import logger
import tomllib
import os
import json
import time
import uuid
import httpx
import asyncio
from typing import Optional, Dict, Any
from urllib.parse import quote

from WechatAPI import WechatAPIClient
from utils.decorators import *
from utils.plugin_base import PluginBase


class HttpResponse:
    """HTTP响应类，用于兼容旧插件代码"""
    def __init__(self, status_code, body, headers=None):
        self.code = status_code
        self.status_code = status_code
        self.body = body
        self.headers = headers or {}
        self._text = None

    def json(self):
        """解析 JSON 响应体"""
        if not self.body:
            return {}
        try:
            return json.loads(self.body)
        except (json.JSONDecodeError, TypeError):
            return {}


class DoubaoChat(PluginBase):
    """豆包AI对话插件 - 从DoubaoChat完整移植"""
    description = "豆包AI对话插件，支持连续对话模式"
    author = "老王移植版"
    version = "1.0.0"

    def __init__(self):
        super().__init__()
        
        # 获取配置文件路径
        config_path = os.path.join(os.path.dirname(__file__), "config.toml")
        
        try:
            with open(config_path, "rb") as f:
                config = tomllib.load(f)
                
            # 读取基本配置
            basic_config = config.get("basic", {})
            self.enable = basic_config.get("enable", False)
            
            # 读取豆包配置
            doubao_config = config.get("doubao", {})
            self.cookies = doubao_config.get("cookies", "")
            self.keywords = doubao_config.get("keywords", ["豆包"])
            self.conversation_timeout = doubao_config.get("conversation_timeout", 300)
            self.min_request_interval = doubao_config.get("min_request_interval", 2.0)
            self.exclude_keywords = doubao_config.get("exclude_keywords", [])
            
            # 豆包API配置
            self.api_base_url = doubao_config.get("api_base_url", "https://www.doubao.com/samantha/chat/completion")
            self.device_id = doubao_config.get("device_id", "7468716989062841895")
            self.tea_uuid = doubao_config.get("tea_uuid", "7468716986638386703")
            self.web_id = doubao_config.get("web_id", "7468716986638386703")
            
        except Exception as e:
            logger.error(f"加载DoubaoChat配置文件失败: {str(e)}")
            self.enable = False
            
        # 会话模式配置
        self._conversation_mode = {}  # 群组ID -> 是否在会话模式
        self._conversation_users = {}  # 群组ID -> 用户ID
        self._last_conversation_time = {}  # 群组ID -> 最后会话时间
        
        # httpx客户端管理
        self._client = None
        self._client_lock = asyncio.Lock()
        
        # 用户级别的请求限制
        self._user_limits = {}

    async def async_init(self):
        """异步初始化"""
        return

    async def get_httpx_client(self):
        """创建httpx客户端"""
        return httpx.AsyncClient(
            timeout=httpx.Timeout(connect=10.0, read=20.0, write=20.0, pool=5.0),
            verify=False,  # 禁用SSL验证
            follow_redirects=True,
            limits=httpx.Limits(max_connections=20, max_keepalive_connections=10),
        )

    async def get_session(self):
        """获取或创建httpx异步客户端"""
        async with self._client_lock:
            if self._client is None or self._client.is_closed:
                self._client = await self.get_httpx_client()
            return self._client

    async def close_session(self):
        """关闭httpx客户端"""
        async with self._client_lock:
            if self._client and not self._client.is_closed:
                await self._client.aclose()
                self._client = None

    def _check_user_limit(self, target_id: str, user_id: str) -> float:
        """检查用户请求限制，返回需要等待的时间"""
        current_time = time.time()
        user_key = f"{target_id}_{user_id}"
        
        if user_key in self._user_limits:
            last_request_time = self._user_limits[user_key]
            time_diff = current_time - last_request_time
            
            if time_diff < self.min_request_interval:
                return self.min_request_interval - time_diff
        
        self._user_limits[user_key] = current_time
        return 0

    def _generate_headers(self) -> Dict[str, str]:
        """生成请求头"""
        return {
            "accept": "*/*",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8",
            "content-type": "application/json",
            "origin": "https://www.doubao.com",
            "referer": "https://www.doubao.com/",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36",
            "cookie": self.cookies
        }

    def _is_in_conversation_mode(self, target_id: str, user_id: str) -> bool:
        """检查是否在会话模式"""
        if target_id not in self._conversation_mode:
            return False
            
        # 检查超时
        current_time = time.time()
        last_time = self._last_conversation_time.get(target_id, 0)
        if current_time - last_time > self.conversation_timeout:
            # 超时，自动退出会话模式
            self._exit_conversation_mode_internal(target_id)
            return False
            
        # 检查用户是否匹配
        return (self._conversation_mode.get(target_id, False) and 
                self._conversation_users.get(target_id) == user_id)

    async def _enter_conversation_mode(self, target_id: str, user_id: str):
        """进入会话模式"""
        self._conversation_mode[target_id] = True
        self._conversation_users[target_id] = user_id
        self._last_conversation_time[target_id] = time.time()

    async def _exit_conversation_mode(self, target_id: str):
        """退出会话模式"""
        self._exit_conversation_mode_internal(target_id)

    def _exit_conversation_mode_internal(self, target_id: str):
        """内部退出会话模式"""
        self._conversation_mode.pop(target_id, None)
        self._conversation_users.pop(target_id, None)
        self._last_conversation_time.pop(target_id, None)

    async def _handle_conversation_message(self, bot: WechatAPIClient, message: dict, content: str):
        """处理会话消息"""
        # 更新最后会话时间
        target_id = self._get_target_id(message)
        self._last_conversation_time[target_id] = time.time()
        
        # 处理对话
        await self._handle_chat_request(bot, message, content)

    def _get_target_id(self, message: dict) -> str:
        """获取目标ID（群聊ID或私聊用户ID）"""
        if message.get("IsGroup", False):
            return message.get("FromWxid", "")
        else:
            return message.get("SenderWxid", message.get("FromWxid", ""))

    def _get_sender_id(self, message: dict) -> str:
        """获取发送者ID"""
        return message.get("SenderWxid", message.get("FromWxid", ""))

    async def _handle_chat_request(self, bot: WechatAPIClient, message: dict, query: str):
        """处理对话请求"""
        try:
            target_id = self._get_target_id(message)
            sender_id = self._get_sender_id(message)

            # 检查用户限制
            wait_time = self._check_user_limit(target_id, sender_id)
            if wait_time > 0:
                await bot.send_text_message(target_id, f"请求过于频繁，请等待 {wait_time:.1f} 秒后再试")
                return

            # 调用豆包API
            result = await self.call_doubao_api(query)

            if result and result.get("type") == "text":
                text = result.get("text", "")
                if text:
                    await bot.send_text_message(target_id, text)

        except Exception as e:
            logger.error(f"处理对话请求失败: {e}", exc_info=True)
            await bot.send_text_message(target_id, "抱歉，处理请求时出现错误，请稍后再试")

    def _extract_query(self, content: str) -> str:
        """提取查询内容"""
        for keyword in self.keywords:
            if content.startswith(keyword):
                return content[len(keyword):].strip()
        return content.strip()

    def _should_exclude_query(self, query: str) -> bool:
        """检查查询是否包含排除关键词"""
        query_lower = query.lower()
        for exclude_keyword in self.exclude_keywords:
            if exclude_keyword.lower() in query_lower:
                return True
        return False

    @on_text_message(priority=50)
    async def handle_text(self, bot: WechatAPIClient, message: dict):
        """处理文本消息"""
        try:
            # 如果插件未启用，直接返回
            if not self.enable:
                return
                
            content = message.get("Content", "").strip()
            target_id = self._get_target_id(message)
            sender_id = self._get_sender_id(message)
            
            # 检查会话模式
            if self._is_in_conversation_mode(target_id, sender_id):
                # 在会话模式中，处理所有消息
                if content == "结束" or content == "豆包 结束":
                    await self._exit_conversation_mode(target_id)
                    await bot.send_text_message(target_id, "已退出连续对话模式")
                    return
                else:
                    # 处理会话消息
                    await self._handle_conversation_message(bot, message, content)
                    return
            
            # 检查关键词匹配：豆包+#开头
            if not content.startswith("豆包"):
                return

            # 提取豆包后的内容
            after_doubao = content[2:].strip()  # 移除"豆包"
            if not after_doubao.startswith("#"):
                return

            # 提取实际查询内容，必须有提示词
            query = after_doubao[1:].strip()  # 移除"#"
            if not query:
                return

            # 检查排除关键词（避免与其他插件冲突）
            if self._should_exclude_query(query):
                logger.debug(f"DoubaoChat跳过排除关键词: {query}")
                return

            logger.info(f"DoubaoChat处理消息: {content}")

            # 检查特殊命令
            if query == "对话":
                await self._enter_conversation_mode(target_id, sender_id)
                await bot.send_text_message(target_id, "已进入连续对话模式，直接发送消息即可对话，发送'结束'退出")
                return

            # 处理普通对话
            await self._handle_chat_request(bot, message, query)
            
        except Exception as e:
            logger.error(f"DoubaoChat处理消息时出错: {e}", exc_info=True)

    async def call_doubao_api(self, query: str) -> Optional[Dict[str, Any]]:
        """调用豆包API"""
        try:
            # 构造请求URL参数
            params = {
                "aid": "497858",
                "device_id": self.device_id,
                "device_platform": "web",
                "language": "zh",
                "pc_version": "2.16.4",
                "pkg_type": "release_version",
                "real_aid": "497858",
                "region": "CN",
                "samantha_web": "1",
                "sys_region": "CN",
                "tea_uuid": self.tea_uuid,
                "use-olympus-account": "1",
                "version_code": "20800",
                "web_id": self.web_id
            }

            # 创建随机的会话ID和消息ID
            conversation_id = f"38{int(time.time() * 10000)}8"
            section_id = f"{conversation_id[:10]}4{conversation_id[10:]}"
            message_id = f"ba0d6520-{uuid.uuid4().hex[:4]}-11f0-a188-0d4d{uuid.uuid4().hex[:8]}"

            # 构造请求数据
            request_data = {
                "messages": [{
                    "content": json.dumps({"text": query}),
                    "content_type": 2001,
                    "attachments": [],
                    "references": []
                }],
                "completion_option": {
                    "is_regen": False,
                    "with_suggest": True,
                    "need_create_conversation": True,
                    "launch_stage": 1,
                    "is_replace": False,
                    "is_delete": False,
                    "message_from": 0,
                    "use_deep_think": False,
                    "use_auto_cot": False,
                    "event_id": "0"
                },
                "section_id": section_id,
                "conversation_id": conversation_id,
                "local_message_id": message_id
            }

            # 获取httpx客户端
            client = await self.get_session()

            # 设置请求头
            headers = self._generate_headers()

            # 构造完整URL
            url = f"{self.api_base_url}?" + "&".join([f"{k}={quote(str(v))}" for k,v in params.items()])

            # 发送POST请求
            response = await client.post(
                url,
                json=request_data,
                headers=headers,
                timeout=httpx.Timeout(connect=10.0, read=300.0, write=60.0, pool=10.0)
            )

            if response.status_code != 200:
                error_text = response.content
                logger.error(f"[DoubaoChat] API请求失败: 状态码={response.status_code}, 响应内容={error_text}")
                raise ValueError(f"API请求失败: {response.status_code}")

            # 创建自定义响应对象
            http_response = HttpResponse(
                status_code=response.status_code,
                body=response.content,
                headers=dict(response.headers)
            )

            # 处理响应
            result = await self._process_stream(http_response)

            if result:
                return result

            return {"type": "text", "text": ""}

        except Exception as e:
            logger.error(f"[DoubaoChat] API调用失败: {e}")
            return {"type": "text", "text": "抱歉，豆包服务暂时不可用，请稍后再试"}

    async def _process_stream(self, response) -> Optional[Dict[str, Any]]:
        """处理SSE响应流"""
        result_text = ""
        result_data = {"type": "text", "text": ""}

        try:
            # 解码响应数据
            if isinstance(response.body, str):
                chunk = response.body.encode('utf-8')
            else:
                chunk = response.body

            try:
                decoded_chunk = chunk.decode('utf-8', errors='ignore')
                buffer = decoded_chunk
            except UnicodeDecodeError:
                return None

            # 按行处理数据
            lines = buffer.split('\n')

            for line in lines:
                line = line.strip()
                if not line:
                    continue

                # 处理SSE格式的数据
                if line.startswith('data: '):
                    data_content = line[6:]  # 移除 'data: ' 前缀

                    if data_content == '[DONE]':
                        break

                    try:
                        # 解析JSON数据
                        json_data = json.loads(data_content)

                        # 提取文本内容
                        if 'event_data' in json_data:
                            event_data_str = json_data['event_data']
                            try:
                                event_data = json.loads(event_data_str)

                                # 检查是否有消息内容
                                if 'message' in event_data:
                                    message = event_data['message']
                                    if 'content' in message:
                                        content_str = message['content']
                                        try:
                                            content_obj = json.loads(content_str)
                                            if 'text' in content_obj:
                                                text = content_obj['text']
                                                if text:
                                                    result_text += text
                                        except:
                                            pass

                                # 检查TTS内容（完整文本）
                                if 'tts_content' in event_data:
                                    text = event_data['tts_content']
                                    if text and text.strip():
                                        result_text = text  # 使用完整文本替换

                            except json.JSONDecodeError:
                                pass

                    except json.JSONDecodeError:
                        continue

            # 返回结果
            if result_text.strip():
                result_data["text"] = result_text.strip()
                return result_data
            else:
                return {"type": "text", "text": "豆包没有返回有效回复"}

        except Exception as e:
            logger.error(f"处理豆包响应流时出错: {e}")
            return {"type": "text", "text": "处理回复时出现错误"}
