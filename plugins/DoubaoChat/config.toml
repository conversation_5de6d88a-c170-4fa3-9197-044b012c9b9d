# DoubaoChat插件配置文件

[basic]
# 插件启用状态
enable = true

[doubao]
# 豆包API配置
cookies = ""  # 必需：豆包网站的cookies，用于API认证

# 触发关键词配置
keywords = ["豆包"]  # 触发关键词列表，消息以"豆包#"开头才会触发

# 会话模式配置
conversation_timeout = 300  # 会话模式超时时间（秒），默认5分钟
min_request_interval = 2.0  # 最小请求间隔（秒），防止频繁请求

# 豆包API常量（一般不需要修改）
device_id = "7468716989062841895"
tea_uuid = "7468716986638386703"
web_id = "7468716986638386703"
api_base_url = "https://www.doubao.com/samantha/chat/completion"

# 排除关键词（避免与其他插件冲突）
exclude_keywords = [
    # 画图相关
    "画一", "画个", "画图", "绘制", "生成图片", "创建图像", "创建图片",
    "生成一张", "生成一个", "生成一幅", "作画", "画出", "生成一套",
    "分镜", "插画", "漫画", "海报", "封面", "图像", "图集",
    # 视频相关
    "找点", "视频", "生成视频", "图生视频", "豆包视频",
    # 识图相关
    "解释", "识别", "这是什么", "分析", "看看", "这张图", "图里有什么"
]
