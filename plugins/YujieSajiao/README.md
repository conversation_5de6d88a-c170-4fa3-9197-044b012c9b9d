# 御姐撒娇插件

这是一个简单的微信机器人插件，可以随机返回一条御姐撒娇语音。

## 功能介绍

- 当用户发送包含触发词的消息时，插件会从API获取一条随机御姐撒娇语音并发送
- 支持自定义触发词
- 支持HTTP代理配置

## 安装方法

1. 将插件文件夹放入`plugins`目录
2. 重启机器人

## 配置说明

配置文件位于`plugins/YujieSajiao/config.toml`，包含以下配置项：

```toml
[YujieSajiao]
# 插件开关
enable = true

# API地址
api_url = "https://api.317ak.com/API/yy/yjsj.php"

# 触发词列表
trigger_words = [
    "御姐撒娇",
    "御姐语音",
    "来点御姐",
    "撒个娇"
]

# HTTP代理（可选）
# http_proxy = "http://127.0.0.1:7890"
```

## 使用方法

在聊天中发送包含触发词的消息，例如：

- "来点御姐撒娇"
- "御姐语音"
- "来点御姐"
- "撒个娇"

机器人会回复一条随机御姐撒娇语音。

## API说明

本插件使用的API来自：https://api.317ak.com/API/yy/yjsj.php

- 请求方式：GET
- 返回格式：MP3
- 无需参数

## 注意事项

- 请确保网络环境能够访问API
- 如果需要使用代理，请在配置文件中设置`http_proxy`
- 语音文件可能较大，请确保网络环境良好
