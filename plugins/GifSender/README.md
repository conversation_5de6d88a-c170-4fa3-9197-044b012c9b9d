# GifSender 动图发送插件

这个插件用于测试发送GIF动图和视频文件功能。

## 功能

1. 列出可用的动图文件
2. 发送GIF动图或视频文件

## 使用方法

1. **动图列表** - 列出 `gifs` 目录中的所有动图文件
2. **发送动图 [文件名]** - 发送指定的动图文件
   - 如果不指定文件名，则使用目录中的第一个动图文件

## 支持的文件类型

- GIF (.gif)
- 视频 (.mp4)
- WebP (.webp)

## 文件存放

将要发送的动图文件放在以下目录中：
```
plugins/GifSender/gifs/
```

插件会自动创建这个目录，您可以直接将文件放入其中，然后使用上述命令进行测试。

## 注意事项

1. 发送大型动图或视频文件可能需要较长时间
2. 某些格式的动图可能在微信中无法正常显示
3. 视频发送功能使用了默认的3秒时长，实际视频可能更长或更短
