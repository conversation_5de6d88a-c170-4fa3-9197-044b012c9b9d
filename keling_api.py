import json
import os
import uuid
import requests
from pathlib import Path
from kling import ImageGen, VideoGen


# 从JSON文件读取cookie
def load_cookie_from_json(json_path):
    with open(json_path, 'r') as f:
        config = json.load(f)
    return config.get('kling_cookie')


# 使用配置文件路径
config_path = 'config.json'  # 修改为你的配置文件路径
cookie = load_cookie_from_json(config_path)

# 确保输出目录存在
output_dir = './output'
os.makedirs(output_dir, exist_ok=True)


# 生成唯一文件名
def generate_unique_filename(extension):
    unique_id = str(uuid.uuid4())
    return f"{unique_id}.{extension}"


# 下载并保存文件
def download_file(url, filepath):
    response = requests.get(url, stream=True)
    if response.status_code == 200:
        with open(filepath, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)
        return True
    return False


# 示例1：生成图像
def generate_image(prompt):
    image_gen = ImageGen(cookie)

    # 生成图像
    result = image_gen.save_images(prompt, output_dir)

    # 处理结果，检查API是否已经保存了文件
    # 如果API返回了图像URL但没有保存到本地，我们需要下载
    if isinstance(result, dict) and 'url' in result:
        # 确定图像扩展名
        extension = 'png'  # 默认为png，可以从URL中解析
        if '.' in result['url'].split('/')[-1]:
            extension = result['url'].split('/')[-1].split('.')[-1]

        # 生成唯一文件名
        filename = generate_unique_filename(extension)
        filepath = os.path.join(output_dir, filename)

        # 检查文件是否已经存在，如果不存在则下载
        if not os.path.exists(filepath):
            download_success = download_file(result['url'], filepath)
            if not download_success:
                print(f"警告：无法下载图像，但API调用成功")

        # 将文件路径添加到结果中
        result['local_filepath'] = filepath

    print(f"图像已生成: {result}")
    if isinstance(result, dict) and 'local_filepath' in result:
        print(f"本地文件保存在: {result['local_filepath']}")

    return result


# 示例2：基于图像生成新图像
def generate_image_from_image(prompt, base_image_path):
    image_gen = ImageGen(cookie)

    # 基于已有图像生成新图像
    result = image_gen.save_images(prompt, output_dir, image_url=base_image_path)

    # 处理结果
    if isinstance(result, dict) and 'url' in result:
        # 确定图像扩展名
        extension = 'png'  # 默认为png
        if '.' in result['url'].split('/')[-1]:
            extension = result['url'].split('/')[-1].split('.')[-1]

        # 生成唯一文件名
        filename = generate_unique_filename(extension)
        filepath = os.path.join(output_dir, filename)

        # 检查文件是否已经存在，如果不存在则下载
        if not os.path.exists(filepath):
            download_success = download_file(result['url'], filepath)
            if not download_success:
                print(f"警告：无法下载图像，但API调用成功")

        # 将文件路径添加到结果中
        result['local_filepath'] = filepath

    print(f"基于图像的新图像已生成: {result}")
    if isinstance(result, dict) and 'local_filepath' in result:
        print(f"本地文件保存在: {result['local_filepath']}")

    return result


# 示例3：生成视频
def generate_video(prompt, high_quality=True):
    video_gen = VideoGen(cookie)

    # 生成高质量视频
    result = video_gen.save_video(prompt, output_dir, is_high_quality=high_quality)

    # 处理结果
    if isinstance(result, dict) and 'url' in result:
        # 确定视频扩展名
        extension = 'mp4'  # 默认为mp4
        if '.' in result['url'].split('/')[-1]:
            extension = result['url'].split('/')[-1].split('.')[-1]

        # 生成唯一文件名
        filename = generate_unique_filename(extension)
        filepath = os.path.join(output_dir, filename)

        # 检查文件是否已经存在，如果不存在则下载
        if not os.path.exists(filepath):
            download_success = download_file(result['url'], filepath)
            if not download_success:
                print(f"警告：无法下载视频，但API调用成功")

        # 将文件路径添加到结果中
        result['local_filepath'] = filepath

    print(f"视频已生成: {result}")
    if isinstance(result, dict) and 'local_filepath' in result:
        print(f"本地文件保存在: {result['local_filepath']}")

    return result


# 示例4：基于图像生成视频并自动延长
def generate_video_from_image(prompt, image_path):
    video_gen = VideoGen(cookie)

    # 基于图像生成视频，使用高质量设置，并自动延长到10秒
    result = video_gen.save_video(
        prompt,
        output_dir,
        image_url=image_path,
        is_high_quality=True,
        auto_extend=True
    )

    # 处理结果
    if isinstance(result, dict) and 'url' in result:
        # 确定视频扩展名
        extension = 'mp4'  # 默认为mp4
        if '.' in result['url'].split('/')[-1]:
            extension = result['url'].split('/')[-1].split('.')[-1]

        # 生成唯一文件名
        filename = generate_unique_filename(extension)
        filepath = os.path.join(output_dir, filename)

        # 检查文件是否已经存在，如果不存在则下载
        if not os.path.exists(filepath):
            download_success = download_file(result['url'], filepath)
            if not download_success:
                print(f"警告：无法下载视频，但API调用成功")

        # 将文件路径添加到结果中
        result['local_filepath'] = filepath

    print(f"基于图像的视频已生成: {result}")
    if isinstance(result, dict) and 'local_filepath' in result:
        print(f"本地文件保存在: {result['local_filepath']}")

    return result


# 示例5：使用2.0模型生成视频
def generate_video_with_model(prompt):
    video_gen = VideoGen(cookie)

    # 使用2.0模型生成视频（注意必须使用高质量选项）
    result = video_gen.save_video(
        prompt,
        output_dir,
        is_high_quality=True,
        model_name="2.0"
    )

    # 处理结果
    if isinstance(result, dict) and 'url' in result:
        # 确定视频扩展名
        extension = 'mp4'  # 默认为mp4
        if '.' in result['url'].split('/')[-1]:
            extension = result['url'].split('/')[-1].split('.')[-1]

        # 生成唯一文件名
        filename = generate_unique_filename(extension)
        filepath = os.path.join(output_dir, filename)

        # 检查文件是否已经存在，如果不存在则下载
        if not os.path.exists(filepath):
            download_success = download_file(result['url'], filepath)
            if not download_success:
                print(f"警告：无法下载视频，但API调用成功")

        # 将文件路径添加到结果中
        result['local_filepath'] = filepath

    print(f"使用2.0模型的视频已生成: {result}")
    if isinstance(result, dict) and 'local_filepath' in result:
        print(f"本地文件保存在: {result['local_filepath']}")

    return result


# 主函数
if __name__ == "__main__":
    # 调用示例函数
    image_result = generate_image("日落时分的海滩,真实感，16:9尺寸")
    print("--------------------")
    print(image_result)

    # 如果需要其他功能，取消下面的注释
    # video_result = generate_video("日落时分的海滩", high_quality=True)
    # image_from_image_result = generate_image_from_image("戴上红色帽子", "path/to/your/image.jpg")
    # video_from_image_result = generate_video_from_image("使这张照片栩栩如生", "path/to/your/image.jpg")
    # video_with_model_result = generate_video_with_model("繁华都市的夜景")