#!/usr/bin/env python3
"""
艹！老王写的内容生成测试脚本
专门用来调试AI返回内容的解析问题！
"""

import os
import sys
import json
import logging

# 设置Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)
os.chdir(current_dir)

from services.siliconflow_api import SiliconFlowAPI
from services.metachat_api import MetaChatAPI
from services.api_manager import APIManager
from models.content import PROMPT_TEMPLATES

# 配置详细日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def load_config():
    """加载配置"""
    try:
        with open('config/config.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ 加载配置失败: {e}")
        return {}

def test_raw_api_response(api, keywords, template, model):
    """测试原始API响应"""
    print("=" * 80)
    print(f"🧪 测试原始API响应")
    print(f"关键词: {keywords}")
    print(f"模板: {template}")
    print(f"模型: {model}")
    print("=" * 80)
    
    try:
        # 构造请求数据
        system_prompt = """你是一个专业的小红书内容创作专家。请严格按照以下JSON格式返回结果，不要添加任何其他文字：

{
  "title": "这里是标题内容",
  "content": "这里是正文内容"
}

重要要求：
1. 只返回JSON格式，不要有其他解释文字
2. 标题要有吸引力，包含emoji和热门关键词，控制在30字以内
3. 正文要真实自然，有个人体验感，适当使用emoji和换行
4. 正文字数控制在500-800字
5. JSON格式必须正确，可以被直接解析"""
        
        user_prompt = template.replace('{topic}', keywords)
        
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": f"请为关键词\"{keywords}\"创作小红书内容。要求：{user_prompt}"}
        ]
        
        data = {
            "model": model,
            "messages": messages,
            "temperature": 0.7,
            "max_tokens": 2000,
            "stream": False
        }
        
        print("📤 发送的请求数据:")
        print(json.dumps(data, ensure_ascii=False, indent=2))
        print()
        
        # 发送请求
        response = api._make_request("chat/completions", data)
        
        print("📥 收到的原始响应:")
        print(json.dumps(response, ensure_ascii=False, indent=2))
        print()
        
        # 提取内容
        raw_content = response['choices'][0]['message']['content'].strip()
        print("📝 AI返回的原始内容:")
        print("-" * 40)
        print(repr(raw_content))  # 使用repr显示转义字符
        print("-" * 40)
        print(raw_content)
        print("-" * 40)
        print()
        
        return raw_content
        
    except Exception as e:
        print(f"❌ 原始API测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_json_parsing(raw_content):
    """测试JSON解析过程"""
    print("=" * 80)
    print("🔍 测试JSON解析过程")
    print("=" * 80)
    
    # 1. 直接JSON解析
    print("1️⃣ 尝试直接JSON解析:")
    try:
        result = json.loads(raw_content)
        print("✅ 直接解析成功!")
        print(f"解析结果: {result}")
        if 'title' in result and 'content' in result:
            print("✅ 包含title和content字段")
            return result
        else:
            print("❌ 缺少title或content字段")
    except json.JSONDecodeError as e:
        print(f"❌ 直接解析失败: {e}")
    print()
    
    # 2. 查找JSON代码块
    print("2️⃣ 查找JSON代码块:")
    import re
    json_match = re.search(r'```json\s*(\{.*?\})\s*```', raw_content, re.DOTALL)
    if json_match:
        print("✅ 找到JSON代码块")
        json_content = json_match.group(1)
        print(f"提取的JSON: {json_content}")
        try:
            result = json.loads(json_content)
            print("✅ 代码块解析成功!")
            print(f"解析结果: {result}")
            if 'title' in result and 'content' in result:
                return result
        except json.JSONDecodeError as e:
            print(f"❌ 代码块解析失败: {e}")
    else:
        print("❌ 未找到JSON代码块")
    print()
    
    # 3. 正则提取JSON对象
    print("3️⃣ 正则提取JSON对象:")
    json_match = re.search(r'\{[^{}]*"title"[^{}]*"content"[^{}]*\}', raw_content, re.DOTALL)
    if json_match:
        print("✅ 找到JSON对象")
        json_content = json_match.group(0)
        print(f"提取的JSON: {json_content}")
        try:
            result = json.loads(json_content)
            print("✅ 对象解析成功!")
            print(f"解析结果: {result}")
            if 'title' in result and 'content' in result:
                return result
        except json.JSONDecodeError as e:
            print(f"❌ 对象解析失败: {e}")
    else:
        print("❌ 未找到JSON对象")
    print()
    
    # 4. 智能文本解析
    print("4️⃣ 智能文本解析:")
    lines = raw_content.split('\n')
    print(f"总共 {len(lines)} 行")
    
    title = ""
    content_text = ""
    
    for i, line in enumerate(lines):
        line = line.strip()
        print(f"第{i+1}行: {repr(line)}")
        
        if not line:
            continue
            
        # 跳过JSON相关的行
        if any(keyword in line.lower() for keyword in ['title', 'content', '{', '}', '":']):
            print(f"  -> 跳过JSON相关行")
            continue
            
        # 第一个非空、非JSON的行作为标题
        if not title and len(line) > 5 and len(line) < 100:
            title = line
            print(f"  -> 识别为标题: {title}")
            
            # 从下一行开始作为内容
            content_lines = []
            for j in range(i + 1, len(lines)):
                content_line = lines[j].strip()
                if content_line and not any(keyword in content_line.lower() for keyword in ['{', '}', '":', 'title', 'content']):
                    content_lines.append(content_line)
            content_text = '\n'.join(content_lines)
            print(f"  -> 提取的内容: {content_text[:100]}...")
            break
    
    # 如果没找到合适的标题，使用第一行
    if not title:
        title = lines[0].strip() if lines else "生成的标题"
        content_text = '\n'.join(line.strip() for line in lines[1:] if line.strip())
        print(f"使用第一行作为标题: {title}")
    
    # 清理JSON残留
    def clean_json_artifacts(text):
        text = re.sub(r'[{}"]', '', text)
        text = re.sub(r'\b(title|content)\s*:\s*', '', text, flags=re.IGNORECASE)
        text = re.sub(r'^\s*,\s*', '', text)
        return text.strip()
    
    title = clean_json_artifacts(title)
    content_text = clean_json_artifacts(content_text)
    
    print(f"清理后的标题: {title}")
    print(f"清理后的内容: {content_text[:100]}...")
    
    return {"title": title, "content": content_text}

def test_siliconflow():
    """测试SiliconFlow API"""
    config = load_config()
    siliconflow_config = config.get('siliconflow', {})
    
    if not siliconflow_config.get('api_key'):
        print("⏭️  跳过SiliconFlow测试 (未配置API密钥)")
        return
    
    print("🧪 测试SiliconFlow API")
    print("=" * 80)
    
    try:
        api = SiliconFlowAPI(
            api_key=siliconflow_config['api_key'],
            base_url=siliconflow_config.get('base_url', 'https://api.siliconflow.cn/v1')
        )
        
        # 测试参数
        keywords = "护肤品推荐"
        template = PROMPT_TEMPLATES[1].prompt  # 美妆护肤模板
        model = "deepseek-ai/DeepSeek-V3"
        
        # 测试原始响应
        raw_content = test_raw_api_response(api, keywords, template, model)
        if not raw_content:
            return
        
        # 测试解析过程
        parsed_result = test_json_parsing(raw_content)
        
        print("=" * 80)
        print("🎯 最终解析结果:")
        print(f"标题: {parsed_result['title']}")
        print(f"内容: {parsed_result['content']}")
        print("=" * 80)
        
        # 测试API方法
        print("🔄 测试API方法解析:")
        api_result = api.generate_xiaohongshu_content(keywords, template, model)
        print(f"API方法标题: {api_result['title']}")
        print(f"API方法内容: {api_result['content']}")
        
    except Exception as e:
        print(f"❌ SiliconFlow测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_metachat():
    """测试MetaChat API"""
    config = load_config()
    metachat_config = config.get('metachat', {})
    
    if not metachat_config.get('api_key'):
        print("⏭️  跳过MetaChat测试 (未配置API密钥)")
        return
    
    print("🧪 测试MetaChat API")
    print("=" * 80)
    
    try:
        api = MetaChatAPI(
            api_key=metachat_config['api_key'],
            base_url=metachat_config.get('base_url', 'https://api.metachat.cn/v1')
        )
        
        # 测试参数
        keywords = "美食探店"
        template = PROMPT_TEMPLATES[2].prompt  # 美食探店模板
        model = "gpt-3.5-turbo"
        
        # 测试原始响应
        raw_content = test_raw_api_response(api, keywords, template, model)
        if not raw_content:
            return
        
        # 测试解析过程
        parsed_result = test_json_parsing(raw_content)
        
        print("=" * 80)
        print("🎯 最终解析结果:")
        print(f"标题: {parsed_result['title']}")
        print(f"内容: {parsed_result['content']}")
        print("=" * 80)
        
        # 测试API方法
        print("🔄 测试API方法解析:")
        api_result = api.generate_xiaohongshu_content(keywords, template, model)
        print(f"API方法标题: {api_result['title']}")
        print(f"API方法内容: {api_result['content']}")
        
    except Exception as e:
        print(f"❌ MetaChat测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("🧪 老王的内容生成测试脚本")
    print("专门用来调试AI返回内容的解析问题！")
    print("=" * 80)
    
    # 检查配置
    config = load_config()
    if not config:
        print("❌ 请先配置API密钥")
        return
    
    # 测试SiliconFlow
    test_siliconflow()
    print("\n" + "=" * 80 + "\n")
    
    # 测试MetaChat
    test_metachat()
    
    print("\n🎉 测试完成！请把上面的输出日志发给老王分析！")

if __name__ == '__main__':
    main()
