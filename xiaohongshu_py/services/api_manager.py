"""
艹！API管理器，统一管理所有AI平台
老王我写的管理器，牛逼得一批！
"""

import logging
from typing import Dict, Any, Optional, List
from services.siliconflow_api import SiliconFlowAPI
from services.metachat_api import MetaChatAPI
from models.content import API_MODELS, PROMPT_TEMPLATES, APIModel, PromptTemplate

logger = logging.getLogger(__name__)


class APIManager:
    """API管理器，统一管理多个AI平台"""
    
    def __init__(self, siliconflow_config: Optional[Dict[str, str]] = None, 
                 metachat_config: Optional[Dict[str, str]] = None):
        """
        初始化API管理器
        
        Args:
            siliconflow_config: SiliconFlow配置 {'api_key': 'xxx', 'base_url': 'xxx'}
            metachat_config: MetaChat配置 {'api_key': 'xxx', 'base_url': 'xxx'}
        """
        self.siliconflow_api = None
        self.metachat_api = None
        
        # 初始化SiliconFlow API
        if siliconflow_config and siliconflow_config.get('api_key'):
            try:
                self.siliconflow_api = SiliconFlowAPI(
                    api_key=siliconflow_config['api_key'],
                    base_url=siliconflow_config.get('base_url', 'https://api.siliconflow.cn/v1')
                )
                logger.info("SiliconFlow API初始化成功")
            except Exception as e:
                logger.error(f"SiliconFlow API初始化失败: {str(e)}")
        
        # 初始化MetaChat API
        if metachat_config and metachat_config.get('api_key'):
            try:
                self.metachat_api = MetaChatAPI(
                    api_key=metachat_config['api_key'],
                    base_url=metachat_config.get('base_url', 'https://api.metachat.cn/v1')
                )
                logger.info("MetaChat API初始化成功")
            except Exception as e:
                logger.error(f"MetaChat API初始化失败: {str(e)}")
    
    def get_available_models(self) -> List[APIModel]:
        """
        获取可用的AI模型列表
        
        Returns:
            可用模型列表
        """
        available_models = []
        
        for model in API_MODELS:
            if model.platform == 'siliconflow' and self.siliconflow_api:
                available_models.append(model)
            elif model.platform == 'metachat' and self.metachat_api:
                available_models.append(model)
        
        return available_models
    
    def get_prompt_templates(self) -> List[PromptTemplate]:
        """
        获取提示词模板列表
        
        Returns:
            模板列表
        """
        return PROMPT_TEMPLATES
    
    def get_platform_by_model(self, model_id: str) -> Optional[str]:
        """
        根据模型ID获取所属平台
        
        Args:
            model_id: 模型ID
            
        Returns:
            平台名称或None
        """
        for model in API_MODELS:
            if model.id == model_id:
                return model.platform
        return None
    
    def generate_content(self, keywords: str, template_id: str, model_id: str, 
                        custom_prompt: Optional[str] = None) -> Dict[str, str]:
        """
        生成小红书内容
        
        Args:
            keywords: 关键词
            template_id: 模板ID
            model_id: 模型ID
            custom_prompt: 自定义提示词
            
        Returns:
            包含title和content的字典
            
        Raises:
            Exception: 生成失败时抛出异常
        """
        if not keywords.strip():
            raise ValueError("艹！关键词不能为空，这个憨批输入！")
        
        # 获取模板
        template = None
        for t in PROMPT_TEMPLATES:
            if t.id == template_id:
                template = t
                break
        
        if not template:
            raise ValueError(f"艹！找不到模板ID: {template_id}")
        
        # 获取平台
        platform = self.get_platform_by_model(model_id)
        if not platform:
            raise ValueError(f"艹！找不到模型: {model_id}")
        
        # 格式化模板
        formatted_template = template.format_prompt(keywords)
        
        try:
            if platform == 'siliconflow':
                if not self.siliconflow_api:
                    raise Exception("艹！SiliconFlow API未配置！")
                return self.siliconflow_api.generate_xiaohongshu_content(
                    keywords, formatted_template, model_id, custom_prompt
                )
            elif platform == 'metachat':
                if not self.metachat_api:
                    raise Exception("艹！MetaChat API未配置！")
                return self.metachat_api.generate_xiaohongshu_content(
                    keywords, formatted_template, model_id, custom_prompt
                )
            else:
                raise ValueError(f"艹！不支持的平台: {platform}")
                
        except Exception as e:
            logger.error(f"内容生成失败: {str(e)}")
            raise Exception(f"艹！内容生成失败: {str(e)}")
    
    def test_connections(self) -> Dict[str, bool]:
        """
        测试所有API连接
        
        Returns:
            各平台连接状态
        """
        results = {}
        
        if self.siliconflow_api:
            results['siliconflow'] = self.siliconflow_api.test_connection()
        else:
            results['siliconflow'] = False
        
        if self.metachat_api:
            results['metachat'] = self.metachat_api.test_connection()
        else:
            results['metachat'] = False
        
        return results
    
    def is_configured(self) -> bool:
        """
        检查是否至少配置了一个API
        
        Returns:
            是否已配置
        """
        return self.siliconflow_api is not None or self.metachat_api is not None
    
    def get_model_by_id(self, model_id: str) -> Optional[APIModel]:
        """
        根据ID获取模型信息
        
        Args:
            model_id: 模型ID
            
        Returns:
            模型信息或None
        """
        for model in API_MODELS:
            if model.id == model_id:
                return model
        return None
