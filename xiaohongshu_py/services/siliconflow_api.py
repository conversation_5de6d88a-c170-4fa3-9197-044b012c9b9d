"""
艹！SiliconFlow API服务
老王我写的API调用，稳得一批！
"""

import requests
import json
import logging
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)


class SiliconFlowAPI:
    """SiliconFlow API客户端"""
    
    def __init__(self, api_key: str, base_url: str = "https://api.siliconflow.cn/v1"):
        """
        初始化SiliconFlow API客户端
        
        Args:
            api_key: API密钥
            base_url: API基础URL
        """
        if not api_key:
            raise ValueError("艹！API密钥不能为空，这个憨批配置！")
        
        self.api_key = api_key
        self.base_url = base_url.rstrip('/')
        self.headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }
    
    def _make_request(self, endpoint: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        发送API请求
        
        Args:
            endpoint: API端点
            data: 请求数据
            
        Returns:
            API响应数据
            
        Raises:
            Exception: 请求失败时抛出异常
        """
        url = f"{self.base_url}/{endpoint}"
        
        try:
            logger.info(f"发送请求到SiliconFlow: {url}")
            response = requests.post(url, headers=self.headers, json=data, timeout=60)
            
            if response.status_code != 200:
                error_msg = f"艹！SiliconFlow API请求失败: {response.status_code} - {response.text}"
                logger.error(error_msg)
                raise Exception(error_msg)
            
            result = response.json()
            logger.info("SiliconFlow API请求成功")
            return result
            
        except requests.exceptions.Timeout:
            error_msg = "艹！SiliconFlow API请求超时，这个破网络！"
            logger.error(error_msg)
            raise Exception(error_msg)
        except requests.exceptions.RequestException as e:
            error_msg = f"艹！SiliconFlow API网络请求失败: {str(e)}"
            logger.error(error_msg)
            raise Exception(error_msg)
        except json.JSONDecodeError:
            error_msg = "艹！SiliconFlow API返回的不是有效JSON，这个SB接口！"
            logger.error(error_msg)
            raise Exception(error_msg)
    
    def generate_xiaohongshu_content(self, keywords: str, template: str, model: str, 
                                   custom_prompt: Optional[str] = None) -> Dict[str, str]:
        """
        生成小红书内容
        
        Args:
            keywords: 关键词
            template: 模板提示词
            model: 使用的模型
            custom_prompt: 自定义提示词
            
        Returns:
            包含title和content的字典
        """
        system_prompt = """你是一个专业的小红书内容创作专家。请严格按照以下JSON格式返回结果，不要添加任何其他文字：

{
  "title": "这里是标题内容",
  "content": "这里是正文内容"
}

重要要求：
1. 只返回JSON格式，不要有其他解释文字
2. 标题要有吸引力，包含emoji和热门关键词，控制在30字以内
3. 正文要真实自然，有个人体验感，适当使用emoji和换行
4. 正文字数控制在800-1000字
5. JSON格式必须正确，可以被直接解析"""
        
        user_prompt = custom_prompt or template.replace('{topic}', keywords)
        
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": f"请为关键词\"{keywords}\"创作小红书内容。要求：{user_prompt}"}
        ]
        
        data = {
            "model": model,
            "messages": messages,
            "temperature": 0.7,
            "max_tokens": 2000,
            "stream": False
        }
        
        try:
            response = self._make_request("chat/completions", data)
            
            if 'choices' not in response or not response['choices']:
                raise Exception("艹！SiliconFlow API返回格式错误，没有choices字段！")
            
            content = response['choices'][0]['message']['content'].strip()

            # 尝试解析JSON
            try:
                # 先尝试直接解析（处理控制字符）
                import json
                result = json.loads(content, strict=False)
                if 'title' in result and 'content' in result:
                    return result
                else:
                    raise ValueError("JSON格式不正确")
            except (json.JSONDecodeError, ValueError):
                # JSON解析失败，尝试修复控制字符后再解析
                logger.warning("SiliconFlow返回的JSON包含控制字符，尝试修复")

                try:
                    # 修复常见的控制字符问题
                    import re
                    # 保留必要的换行符，移除其他控制字符
                    fixed_content = content.encode('utf-8').decode('unicode_escape')
                    result = json.loads(fixed_content, strict=False)
                    if 'title' in result and 'content' in result:
                        return result
                except:
                    pass

                # 尝试手动提取JSON内容
                try:
                    # 查找完整的JSON对象
                    json_match = re.search(r'\{.*"title".*"content".*\}', content, re.DOTALL)
                    if json_match:
                        json_str = json_match.group(0)
                        # 尝试修复JSON字符串
                        result = json.loads(json_str, strict=False)
                        if 'title' in result and 'content' in result:
                            return result
                except:
                    pass

                # 如果都失败了，使用正则表达式提取
                logger.warning("JSON解析完全失败，使用正则表达式提取")
                return self._extract_with_regex(content)
                
        except Exception as e:
            logger.error(f"SiliconFlow内容生成失败: {str(e)}")
            raise Exception(f"艹！SiliconFlow内容生成失败: {str(e)}")

    def _extract_with_regex(self, content: str) -> Dict[str, str]:
        """
        使用正则表达式从JSON字符串中提取title和content

        Args:
            content: 包含JSON的原始内容

        Returns:
            包含title和content的字典
        """
        import re

        title = ""
        content_text = ""

        # 提取title
        title_match = re.search(r'"title"\s*:\s*"([^"]*(?:\\.[^"]*)*)"', content, re.DOTALL)
        if title_match:
            title = title_match.group(1)
            # 处理转义字符
            title = title.encode().decode('unicode_escape')

        # 提取content
        content_match = re.search(r'"content"\s*:\s*"([^"]*(?:\\.[^"]*)*)"', content, re.DOTALL)
        if content_match:
            content_text = content_match.group(1)
            # 处理转义字符
            content_text = content_text.encode().decode('unicode_escape')

        # 如果没有提取到，使用备用方案
        if not title or not content_text:
            logger.warning("正则表达式提取失败，使用智能文本解析")
            return self._smart_parse_content(content)

        return {"title": title, "content": content_text}

    def _smart_parse_content(self, content: str) -> Dict[str, str]:
        """
        智能解析内容，从非JSON格式中提取标题和内容

        Args:
            content: 原始内容

        Returns:
            包含title和content的字典
        """
        lines = content.split('\n')
        title = ""
        content_text = ""

        # 查找标题
        for i, line in enumerate(lines):
            line = line.strip()
            if not line:
                continue

            # 跳过JSON相关的行
            if any(keyword in line.lower() for keyword in ['title', 'content', '{', '}', '":']):
                continue

            # 第一个非空、非JSON的行作为标题
            if not title and len(line) > 5 and len(line) < 100:
                title = line
                # 从下一行开始作为内容
                content_lines = []
                for j in range(i + 1, len(lines)):
                    content_line = lines[j].strip()
                    if content_line and not any(keyword in content_line.lower() for keyword in ['{', '}', '":', 'title', 'content']):
                        content_lines.append(content_line)
                content_text = '\n'.join(content_lines)
                break

        # 如果没找到合适的标题，使用第一行
        if not title:
            title = lines[0].strip() if lines else "生成的标题"
            content_text = '\n'.join(line.strip() for line in lines[1:] if line.strip())

        # 清理标题和内容中的JSON残留
        title = self._clean_json_artifacts(title)
        content_text = self._clean_json_artifacts(content_text)

        return {"title": title, "content": content_text}

    def _clean_json_artifacts(self, text: str) -> str:
        """清理文本中的JSON残留"""
        import re
        # 移除JSON相关的符号和关键词
        text = re.sub(r'[{}"]', '', text)
        text = re.sub(r'\b(title|content)\s*:\s*', '', text, flags=re.IGNORECASE)
        text = re.sub(r'^\s*,\s*', '', text)
        text = text.strip()
        return text

    def test_connection(self) -> bool:
        """
        测试API连接
        
        Returns:
            连接是否成功
        """
        try:
            data = {
                "model": "deepseek-ai/DeepSeek-V3",
                "messages": [{"role": "user", "content": "测试连接"}],
                "max_tokens": 10
            }
            self._make_request("chat/completions", data)
            return True
        except Exception as e:
            logger.error(f"SiliconFlow连接测试失败: {str(e)}")
            return False
