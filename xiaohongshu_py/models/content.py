"""
艹！这是内容模型，定义小红书内容的数据结构
老王我写的代码，规范得一批！
"""

from datetime import datetime
from typing import List, Dict, Any
import json


class GeneratedContent:
    """生成的小红书内容模型"""
    
    def __init__(self, title: str, content: str, model: str, keywords: List[str], 
                 content_id: str = None, timestamp: datetime = None, is_favorite: bool = False):
        """
        初始化内容对象
        
        Args:
            title: 内容标题
            content: 内容正文
            model: 使用的AI模型
            keywords: 关键词列表
            content_id: 内容ID，如果不提供会自动生成
            timestamp: 创建时间，如果不提供会使用当前时间
            is_favorite: 是否收藏
        """
        self.id = content_id or str(int(datetime.now().timestamp() * 1000))
        self.title = title
        self.content = content
        self.model = model
        self.keywords = keywords
        self.timestamp = timestamp or datetime.now()
        self.is_favorite = is_favorite
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'id': self.id,
            'title': self.title,
            'content': self.content,
            'model': self.model,
            'keywords': self.keywords,
            'timestamp': self.timestamp.isoformat(),
            'is_favorite': self.is_favorite
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'GeneratedContent':
        """从字典创建对象"""
        timestamp = datetime.fromisoformat(data['timestamp'])
        return cls(
            title=data['title'],
            content=data['content'],
            model=data['model'],
            keywords=data['keywords'],
            content_id=data['id'],
            timestamp=timestamp,
            is_favorite=data.get('is_favorite', False)
        )


class PromptTemplate:
    """提示词模板"""
    
    def __init__(self, template_id: str, name: str, category: str, prompt: str):
        """
        初始化模板对象
        
        Args:
            template_id: 模板ID
            name: 模板名称
            category: 模板分类
            prompt: 提示词内容
        """
        self.id = template_id
        self.name = name
        self.category = category
        self.prompt = prompt
    
    def format_prompt(self, topic: str) -> str:
        """格式化提示词，替换占位符"""
        return self.prompt.replace('{topic}', topic)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'id': self.id,
            'name': self.name,
            'category': self.category,
            'prompt': self.prompt
        }


class APIModel:
    """AI模型信息"""
    
    def __init__(self, model_id: str, name: str, description: str, platform: str):
        """
        初始化模型对象
        
        Args:
            model_id: 模型ID
            name: 模型名称
            description: 模型描述
            platform: 所属平台
        """
        self.id = model_id
        self.name = name
        self.description = description
        self.platform = platform
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'platform': self.platform
        }


# 艹，这些常量定义得规范吧！
PROMPT_TEMPLATES = [
    PromptTemplate(
        'lifestyle',
        '生活方式',
        '日常',
        '创作关于{topic}的生活方式内容，要求真实自然，有个人体验感，包含实用建议和生活小贴士。'
    ),
    PromptTemplate(
        'beauty',
        '美妆护肤',
        '美妆',
        '创作关于{topic}的美妆护肤内容，要求包含使用步骤、效果对比、个人感受，语言亲切自然像朋友分享。'
    ),
    PromptTemplate(
        'food',
        '美食探店',
        '美食',
        '创作关于{topic}的美食探店内容，要求描述味道、环境、服务等多维度体验，包含实用信息如地址、价格、推荐菜品。'
    ),
    PromptTemplate(
        'travel',
        '旅行攻略',
        '旅行',
        '创作关于{topic}的旅行攻略内容，要求涵盖交通、住宿、景点、美食建议，提供实用的时间安排和费用参考，分享个人感受和拍照技巧。'
    )
]

API_MODELS = [
    # SiliconFlow平台模型
    APIModel('deepseek-ai/DeepSeek-V3', 'DeepSeek-V3', '最新版本的DeepSeek大模型', 'siliconflow'),
    APIModel('deepseek-ai/DeepSeek-R1', 'DeepSeek-R1', 'DeepSeek推理优化模型', 'siliconflow'),
    APIModel('moonshotai/Kimi-K2-Instruct', 'Kimi-K2-Instruct', 'Moonshot AI的Kimi大模型', 'siliconflow'),
    
    # MetaChat平台模型
    APIModel('gpt-3.5-turbo', 'GPT-3.5 Turbo', 'OpenAI GPT-3.5 Turbo模型', 'metachat'),
    APIModel('gpt-4', 'GPT-4', 'OpenAI GPT-4模型', 'metachat'),
    APIModel('gpt-4-turbo', 'GPT-4 Turbo', 'OpenAI GPT-4 Turbo模型', 'metachat'),
]
