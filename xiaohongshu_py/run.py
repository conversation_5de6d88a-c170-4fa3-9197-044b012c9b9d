#!/usr/bin/env python3
"""
艹！老王写的启动脚本
运行这个脚本就能启动小红书生成器了！
"""

import os
import sys
import json
import logging

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# 确保能找到项目模块
if current_dir not in sys.path:
    sys.path.append(current_dir)

def check_dependencies():
    """检查依赖是否安装"""
    try:
        import flask
        import requests
        print("✅ 依赖检查通过")
        return True
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("请运行: pip install -r requirements.txt")
        return False

def create_directories():
    """创建必要的目录"""
    dirs = ['config', 'data', 'static/css', 'static/js', 'templates']
    for dir_path in dirs:
        os.makedirs(dir_path, exist_ok=True)
    print("✅ 目录结构检查完成")

def check_config():
    """检查配置文件"""
    config_file = 'config/config.json'
    example_file = 'config/config.example.json'
    
    if not os.path.exists(config_file):
        if os.path.exists(example_file):
            print(f"⚠️  配置文件不存在，请复制 {example_file} 为 {config_file} 并填入API密钥")
        else:
            print("⚠️  配置文件不存在，启动后请在Web界面配置API密钥")
    else:
        print("✅ 配置文件存在")

def main():
    """主函数"""
    print("🚀 老王的小红书生成器启动检查...")
    print("=" * 50)
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    # 创建目录
    create_directories()
    
    # 检查配置
    check_config()
    
    print("=" * 50)
    print("🎉 检查完成，正在启动应用...")
    
    # 启动Flask应用
    try:
        from app import app, init_api_manager, load_config
        
        # 初始化
        init_api_manager()
        config = load_config()
        app_config = config.get('app', {})
        
        host = app_config.get('host', '0.0.0.0')
        port = app_config.get('port', 5000)
        debug = app_config.get('debug', True)
        
        print(f"🌐 访问地址: http://localhost:{port}")
        print("🔥 艹！老王的小红书生成器启动成功！")
        
        app.run(host=host, port=port, debug=debug)
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
