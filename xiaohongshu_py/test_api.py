#!/usr/bin/env python3
"""
艹！老王写的API测试脚本
用来测试API是否正常工作
"""

import sys
import os
import json

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_siliconflow_api():
    """测试SiliconFlow API"""
    try:
        from services.siliconflow_api import SiliconFlowAPI
        
        # 这里需要真实的API密钥才能测试
        api_key = input("请输入SiliconFlow API密钥 (回车跳过): ").strip()
        if not api_key:
            print("⏭️  跳过SiliconFlow API测试")
            return
        
        api = SiliconFlowAPI(api_key)
        
        print("🧪 测试SiliconFlow连接...")
        if api.test_connection():
            print("✅ SiliconFlow API连接成功")
            
            # 测试内容生成
            print("🧪 测试内容生成...")
            result = api.generate_xiaohongshu_content(
                keywords="护肤",
                template="创作关于{topic}的美妆护肤内容，要求包含使用步骤、效果对比、个人感受。",
                model="deepseek-ai/DeepSeek-V3"
            )
            print("✅ 内容生成成功:")
            print(f"标题: {result['title']}")
            print(f"内容: {result['content'][:100]}...")
        else:
            print("❌ SiliconFlow API连接失败")
            
    except Exception as e:
        print(f"❌ SiliconFlow API测试失败: {e}")

def test_metachat_api():
    """测试MetaChat API"""
    try:
        from services.metachat_api import MetaChatAPI
        
        # 这里需要真实的API密钥才能测试
        api_key = input("请输入MetaChat API密钥 (回车跳过): ").strip()
        if not api_key:
            print("⏭️  跳过MetaChat API测试")
            return
        
        api = MetaChatAPI(api_key)
        
        print("🧪 测试MetaChat连接...")
        if api.test_connection():
            print("✅ MetaChat API连接成功")
            
            # 测试内容生成
            print("🧪 测试内容生成...")
            result = api.generate_xiaohongshu_content(
                keywords="美食",
                template="创作关于{topic}的美食探店内容，要求描述味道、环境、服务等多维度体验。",
                model="gpt-3.5-turbo"
            )
            print("✅ 内容生成成功:")
            print(f"标题: {result['title']}")
            print(f"内容: {result['content'][:100]}...")
        else:
            print("❌ MetaChat API连接失败")
            
    except Exception as e:
        print(f"❌ MetaChat API测试失败: {e}")

def test_api_manager():
    """测试API管理器"""
    try:
        from services.api_manager import APIManager
        from models.content import PROMPT_TEMPLATES, API_MODELS
        
        print("🧪 测试API管理器...")
        
        # 测试模板和模型加载
        manager = APIManager()
        templates = manager.get_prompt_templates()
        models = manager.get_available_models()
        
        print(f"✅ 加载了 {len(templates)} 个模板")
        print(f"✅ 发现 {len(models)} 个可用模型")
        
        # 显示模板信息
        print("\n📝 可用模板:")
        for template in templates:
            print(f"  - {template.name} ({template.category})")
        
        # 显示模型信息
        print("\n🤖 可用模型:")
        for model in models:
            print(f"  - {model.name} ({model.platform})")
            
    except Exception as e:
        print(f"❌ API管理器测试失败: {e}")

def test_models():
    """测试数据模型"""
    try:
        from models.content import GeneratedContent, PromptTemplate, APIModel
        from datetime import datetime
        
        print("🧪 测试数据模型...")
        
        # 测试GeneratedContent
        content = GeneratedContent(
            title="测试标题",
            content="测试内容",
            model="test-model",
            keywords=["测试", "关键词"]
        )
        
        # 测试序列化
        data = content.to_dict()
        restored = GeneratedContent.from_dict(data)
        
        assert content.title == restored.title
        assert content.content == restored.content
        print("✅ GeneratedContent模型测试通过")
        
        # 测试PromptTemplate
        template = PromptTemplate("test", "测试模板", "测试", "这是{topic}的测试")
        formatted = template.format_prompt("示例")
        assert "这是示例的测试" == formatted
        print("✅ PromptTemplate模型测试通过")
        
        # 测试APIModel
        model = APIModel("test-id", "测试模型", "测试描述", "test-platform")
        model_data = model.to_dict()
        assert model_data['id'] == "test-id"
        print("✅ APIModel模型测试通过")
        
    except Exception as e:
        print(f"❌ 数据模型测试失败: {e}")

def main():
    """主函数"""
    print("🧪 老王的小红书生成器API测试")
    print("=" * 50)
    
    # 测试数据模型
    test_models()
    print()
    
    # 测试API管理器
    test_api_manager()
    print()
    
    # 测试API连接（需要真实密钥）
    print("🔑 API连接测试 (需要真实API密钥)")
    test_siliconflow_api()
    print()
    test_metachat_api()
    
    print("=" * 50)
    print("🎉 测试完成！")

if __name__ == '__main__':
    main()
