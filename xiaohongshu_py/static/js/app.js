// 艹！老王写的JavaScript，比前端那些框架简单多了！

// 全局工具函数
const Utils = {
    // 显示提示信息
    showAlert: function(type, message, duration = 3000) {
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        $('.container').prepend(alertHtml);
        
        // 自动消失
        if (duration > 0) {
            setTimeout(function() {
                $('.alert').alert('close');
            }, duration);
        }
    },
    
    // 复制到剪贴板
    copyToClipboard: function(text, successMessage = '已复制到剪贴板') {
        if (navigator.clipboard) {
            navigator.clipboard.writeText(text).then(function() {
                Utils.showAlert('success', successMessage);
            }).catch(function() {
                Utils.showAlert('warning', '复制失败，请手动复制');
            });
        } else {
            // 兼容旧浏览器
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            try {
                document.execCommand('copy');
                Utils.showAlert('success', successMessage);
            } catch (err) {
                Utils.showAlert('warning', '复制失败，请手动复制');
            }
            document.body.removeChild(textArea);
        }
    },
    
    // 格式化时间
    formatTime: function(timestamp) {
        const date = new Date(timestamp);
        return date.toLocaleString('zh-CN');
    },
    
    // 防抖函数
    debounce: function(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },
    
    // 节流函数
    throttle: function(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        }
    }
};

// 页面加载完成后执行
$(document).ready(function() {
    // 添加页面加载动画
    $('body').addClass('fade-in');
    
    // 为所有按钮添加点击效果
    $('.btn').on('click', function() {
        $(this).addClass('pulse');
        setTimeout(() => {
            $(this).removeClass('pulse');
        }, 600);
    });
    
    // 表单验证增强
    $('form').on('submit', function(e) {
        const form = $(this);
        const requiredFields = form.find('[required]');
        let isValid = true;
        
        requiredFields.each(function() {
            const field = $(this);
            if (!field.val().trim()) {
                field.addClass('is-invalid');
                isValid = false;
            } else {
                field.removeClass('is-invalid');
            }
        });
        
        if (!isValid) {
            e.preventDefault();
            Utils.showAlert('warning', '请填写所有必填字段');
        }
    });
    
    // 输入框焦点效果
    $('.form-control, .form-select').on('focus', function() {
        $(this).parent().addClass('focused');
    }).on('blur', function() {
        $(this).parent().removeClass('focused');
    });
});

// 导出到全局
window.Utils = Utils;
