/* 艹！老王写的自定义样式，比前端那些花里胡哨的好多了！ */

.content-preview {
    line-height: 1.6;
    color: #666;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
}

.history-item {
    transition: all 0.3s ease;
    border: 1px solid rgba(233, 30, 99, 0.1);
}

.history-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(233, 30, 99, 0.2);
    border-color: rgba(233, 30, 99, 0.3);
}

.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(233, 30, 99, 0.3);
    border-radius: 50%;
    border-top-color: #e91e63;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}
