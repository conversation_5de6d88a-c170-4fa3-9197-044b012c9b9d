# 小红书爆款内容生成器 (Python版)

艹！这是老王我用Python重新搞的小红书内容生成器，比那个前端版本牛逼多了！

## 项目简介

这个憨批项目是一个基于AI的小红书内容生成工具，支持多种AI模型和内容模板，能够快速生成高质量的小红书爆款内容。

### 主要功能

- 🚀 **多AI平台支持**: 支持SiliconFlow和MetaChat两大AI平台
- 📝 **丰富模板系统**: 内置生活方式、美妆护肤、美食探店、旅行攻略等模板
- 💾 **历史记录管理**: 自动保存生成历史，支持收藏和删除
- 🎨 **Web界面**: 简洁美观的Web界面，操作简单
- ⚙️ **灵活配置**: 支持自定义API配置和提示词

### 支持的AI模型

#### SiliconFlow平台
- DeepSeek-V3: 最新版本的DeepSeek大模型
- DeepSeek-R1: DeepSeek推理优化模型  
- Kimi-K2-Instruct: Moonshot AI的Kimi大模型

#### MetaChat平台
- GPT-3.5 Turbo: OpenAI GPT-3.5 Turbo模型
- GPT-4: OpenAI GPT-4模型
- GPT-4 Turbo: OpenAI GPT-4 Turbo模型

## 安装和运行

### 环境要求
- Python 3.8+
- pip包管理器

### 安装步骤

1. **克隆项目**
```bash
cd xiaohongshu_py
```

2. **安装依赖**
```bash
pip install -r requirements.txt
```

3. **配置API密钥**
   - 复制 `config/config.example.json` 为 `config/config.json`
   - 填入你的API密钥和配置

4. **运行项目**
```bash
python app.py
```

5. **访问应用**
   - 打开浏览器访问: http://localhost:5000
   - 开始生成小红书爆款内容！

## 项目结构

```
xiaohongshu_py/
├── app.py                 # Flask主应用
├── requirements.txt       # Python依赖
├── README.md             # 项目说明
├── config/               # 配置文件
│   ├── config.json       # 主配置文件
│   └── config.example.json # 配置模板
├── services/             # 服务层
│   ├── __init__.py
│   ├── api_manager.py    # API管理器
│   ├── siliconflow_api.py # SiliconFlow API
│   └── metachat_api.py   # MetaChat API
├── models/               # 数据模型
│   ├── __init__.py
│   └── content.py        # 内容模型
├── templates/            # HTML模板
│   ├── base.html         # 基础模板
│   ├── index.html        # 主页面
│   └── history.html      # 历史记录页面
├── static/               # 静态资源
│   ├── css/
│   ├── js/
│   └── images/
└── data/                 # 数据存储
    └── history.json      # 历史记录
```

## 使用说明

### 1. 配置API
- 在设置页面配置你的API密钥
- 选择要使用的AI模型

### 2. 选择模板
- 生活方式: 适合日常生活分享
- 美妆护肤: 适合美妆产品推荐
- 美食探店: 适合餐厅和美食分享
- 旅行攻略: 适合旅游景点推荐

### 3. 输入关键词
- 输入你想要创作的主题关键词
- 可以输入多个关键词，用逗号分隔

### 4. 生成内容
- 点击生成按钮，等待AI创作
- 生成的内容包含标题和正文
- 可以复制到剪贴板直接使用

### 5. 管理历史
- 查看所有生成历史
- 收藏喜欢的内容
- 删除不需要的记录

## API配置说明

### SiliconFlow配置
```json
{
  "siliconflow": {
    "api_key": "your_api_key_here",
    "base_url": "https://api.siliconflow.cn/v1"
  }
}
```

### MetaChat配置
```json
{
  "metachat": {
    "api_key": "your_api_key_here", 
    "base_url": "https://api.metachat.cn/v1"
  }
}
```

## 开发说明

### 添加新模板
在 `services/api_manager.py` 中的 `PROMPT_TEMPLATES` 列表添加新模板。

### 添加新AI平台
1. 在 `services/` 目录创建新的API服务文件
2. 在 `api_manager.py` 中注册新平台
3. 更新配置文件格式

## 常见问题

**Q: API调用失败怎么办？**
A: 检查API密钥是否正确，网络是否正常，API额度是否充足。

**Q: 生成的内容质量不好？**
A: 尝试更换AI模型，或者使用自定义提示词优化。

**Q: 历史记录丢失了？**
A: 历史记录保存在 `data/history.json` 文件中，请勿删除。

## 技术栈

- **后端**: Flask + Python
- **前端**: Bootstrap + jQuery
- **数据存储**: JSON文件
- **AI接口**: HTTP REST API

## 贡献指南

欢迎提交Issue和Pull Request！

## 许可证

MIT License

---

**注意**: 使用本工具生成的内容仅供参考，请根据实际情况调整和优化。

## 项目状态

✅ **项目完成状态**
- [x] 核心功能实现完成
- [x] API管理器 (支持SiliconFlow和MetaChat)
- [x] 内容生成模板系统
- [x] Web界面 (Flask + Bootstrap)
- [x] 历史记录管理
- [x] 配置文件系统
- [x] 错误处理和日志
- [x] 完整的项目文档

## 快速开始

**方法一：使用启动脚本 (推荐)**
```bash
cd xiaohongshu_py
python run.py
```

**方法二：直接运行**
```bash
cd xiaohongshu_py
pip install -r requirements.txt
python app.py
```

**方法三：测试API功能**
```bash
cd xiaohongshu_py
python test_api.py
```

## 项目特色

🔥 **老王特色**
- 代码注释带有老王风格的"暴躁"特色
- 错误处理直接明了，不绕弯子
- 项目结构清晰，符合Python最佳实践
- 比前端版本更稳定、更易维护

🚀 **技术优势**
- 纯Python实现，无需Node.js环境
- 轻量级Flask框架，启动快速
- 模块化设计，易于扩展
- 完整的错误处理和日志系统

艹，这个README写得够详细了吧！有问题就来找老王我！
