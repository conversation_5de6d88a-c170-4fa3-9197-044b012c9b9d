<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}小红书爆款内容生成器{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #ffeef8 0%, #fff5f5 50%, #fef7f0 100%);
            min-height: 100vh;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        
        .navbar-brand {
            background: linear-gradient(45deg, #e91e63, #f06292);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: bold;
            font-size: 1.5rem;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(233, 30, 99, 0.1);
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.9);
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #e91e63, #f06292);
            border: none;
            border-radius: 25px;
            padding: 10px 25px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            background: linear-gradient(45deg, #c2185b, #e91e63);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(233, 30, 99, 0.3);
        }
        
        .btn-outline-primary {
            border: 2px solid #e91e63;
            color: #e91e63;
            border-radius: 25px;
            padding: 8px 20px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-outline-primary:hover {
            background: #e91e63;
            border-color: #e91e63;
            transform: translateY(-1px);
        }
        
        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #f8f9fa;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #e91e63;
            box-shadow: 0 0 0 0.2rem rgba(233, 30, 99, 0.25);
        }
        
        .alert {
            border-radius: 10px;
            border: none;
        }
        
        .alert-success {
            background: linear-gradient(45deg, #4caf50, #66bb6a);
            color: white;
        }
        
        .alert-danger {
            background: linear-gradient(45deg, #f44336, #ef5350);
            color: white;
        }
        
        .spinner-border {
            color: #e91e63;
        }
        
        .content-result {
            background: linear-gradient(135deg, #fff 0%, #fafafa 100%);
            border-radius: 15px;
            padding: 25px;
            margin-top: 20px;
            border-left: 5px solid #e91e63;
        }
        
        .content-title {
            color: #e91e63;
            font-weight: bold;
            font-size: 1.2rem;
            margin-bottom: 15px;
        }
        
        .content-text {
            line-height: 1.8;
            color: #333;
            white-space: pre-wrap;
        }
        
        .copy-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(233, 30, 99, 0.1);
            border: 1px solid #e91e63;
            color: #e91e63;
            border-radius: 20px;
            padding: 5px 12px;
            font-size: 0.8rem;
            transition: all 0.3s ease;
        }
        
        .copy-btn:hover {
            background: #e91e63;
            color: white;
        }
        
        .history-item {
            transition: all 0.3s ease;
        }
        
        .history-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(233, 30, 99, 0.15);
        }
        
        .badge {
            border-radius: 15px;
            padding: 5px 12px;
        }
        
        .navbar {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(233, 30, 99, 0.1);
        }
        
        .footer {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-top: 1px solid rgba(233, 30, 99, 0.1);
            margin-top: 50px;
            padding: 20px 0;
            text-align: center;
            color: #666;
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-light bg-light sticky-top">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="fas fa-sparkles me-2"></i>小红书爆款内容生成器
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('index') }}">
                            <i class="fas fa-home me-1"></i>内容生成
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('history') }}">
                            <i class="fas fa-history me-1"></i>历史记录
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container my-4">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        
        {% block content %}{% endblock %}
    </div>

    <!-- 页脚 -->
    <div class="footer">
        <div class="container">
            <p class="mb-0">
                <i class="fas fa-code me-1"></i>
                艹！老王用Python写的小红书生成器，比前端版本牛逼多了！
            </p>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
