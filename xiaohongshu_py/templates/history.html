{% extends "base.html" %}

{% block title %}历史记录 - 小红书爆款内容生成器{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <!-- 页面标题 -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>
                <i class="fas fa-history text-primary me-2"></i>
                历史记录
            </h2>
            <div>
                <span class="badge bg-primary">共 {{ history|length }} 条记录</span>
            </div>
        </div>

        {% if history %}
        <!-- 历史记录列表 -->
        <div class="row">
            {% for item in history %}
            <div class="col-lg-6 mb-4">
                <div class="card history-item h-100">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <div>
                            <small class="text-muted">
                                <i class="fas fa-clock me-1"></i>
                                {{ item.timestamp.strftime('%Y-%m-%d %H:%M') }}
                            </small>
                        </div>
                        <div>
                            <span class="badge bg-info">{{ item.model }}</span>
                            {% if item.is_favorite %}
                            <i class="fas fa-star text-warning ms-1"></i>
                            {% endif %}
                        </div>
                    </div>
                    <div class="card-body">
                        <h6 class="card-title text-primary mb-3">{{ item.title }}</h6>
                        <div class="card-text content-preview mb-3">
                            {{ item.content[:150] }}{% if item.content|length > 150 %}...{% endif %}
                        </div>
                        
                        <!-- 关键词标签 -->
                        <div class="mb-3">
                            {% for keyword in item.keywords %}
                            <span class="badge bg-light text-dark me-1">#{{ keyword }}</span>
                            {% endfor %}
                        </div>
                        
                        <!-- 操作按钮 -->
                        <div class="d-flex gap-2">
                            <button class="btn btn-sm btn-outline-primary" 
                                    onclick="viewContent('{{ item.id }}', '{{ item.title }}', `{{ item.content }}`)">
                                <i class="fas fa-eye me-1"></i>查看
                            </button>
                            <button class="btn btn-sm btn-outline-success" 
                                    onclick="copyContent('{{ item.title }}', `{{ item.content }}`)">
                                <i class="fas fa-copy me-1"></i>复制
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <!-- 空状态 -->
        <div class="text-center py-5">
            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
            <h4 class="text-muted">暂无历史记录</h4>
            <p class="text-muted">开始生成你的第一个小红书内容吧！</p>
            <a href="{{ url_for('index') }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>开始创作
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- 内容查看模态框 -->
<div class="modal fade" id="contentModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="contentModalTitle">内容详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="content-result position-relative">
                    <button class="copy-btn" onclick="copyModalTitle()">
                        <i class="fas fa-copy me-1"></i>复制标题
                    </button>
                    <div class="content-title" id="modalTitle"></div>
                    <hr>
                    <button class="copy-btn" onclick="copyModalContent()" style="top: 60px;">
                        <i class="fas fa-copy me-1"></i>复制内容
                    </button>
                    <div class="content-text" id="modalContent"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" onclick="copyAllContent()">
                    <i class="fas fa-copy me-1"></i>复制全部
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let contentModal;

$(document).ready(function() {
    // 初始化模态框
    contentModal = new bootstrap.Modal(document.getElementById('contentModal'));
});

// 查看内容详情
function viewContent(id, title, content) {
    $('#modalTitle').text(title);
    $('#modalContent').text(content);
    contentModal.show();
}

// 复制内容
function copyContent(title, content) {
    const fullContent = title + '\n\n' + content;
    navigator.clipboard.writeText(fullContent).then(function() {
        showAlert('success', '内容已复制到剪贴板');
    }).catch(function() {
        showAlert('warning', '复制失败，请手动复制');
    });
}

// 复制模态框标题
function copyModalTitle() {
    const title = document.getElementById('modalTitle').textContent;
    navigator.clipboard.writeText(title).then(function() {
        showAlert('success', '标题已复制到剪贴板');
    }).catch(function() {
        showAlert('warning', '复制失败，请手动复制');
    });
}

// 复制模态框内容
function copyModalContent() {
    const content = document.getElementById('modalContent').textContent;
    navigator.clipboard.writeText(content).then(function() {
        showAlert('success', '内容已复制到剪贴板');
    }).catch(function() {
        showAlert('warning', '复制失败，请手动复制');
    });
}

// 复制全部内容
function copyAllContent() {
    const title = document.getElementById('modalTitle').textContent;
    const content = document.getElementById('modalContent').textContent;
    const fullContent = title + '\n\n' + content;
    navigator.clipboard.writeText(fullContent).then(function() {
        showAlert('success', '全部内容已复制到剪贴板');
        contentModal.hide();
    }).catch(function() {
        showAlert('warning', '复制失败，请手动复制');
    });
}

// 显示提示信息
function showAlert(type, message) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    $('.container').prepend(alertHtml);
    
    // 3秒后自动消失
    setTimeout(function() {
        $('.alert').alert('close');
    }, 3000);
}
</script>
{% endblock %}
