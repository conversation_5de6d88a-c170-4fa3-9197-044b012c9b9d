{% extends "base.html" %}

{% block title %}内容生成 - 小红书爆款内容生成器{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-8 mx-auto">
        <!-- 页面标题 -->
        <div class="text-center mb-4">
            <h1 class="display-5 fw-bold mb-3">
                <i class="fas fa-magic text-primary me-2"></i>
                小红书爆款内容生成器
            </h1>
            <p class="lead text-muted">AI驱动的内容创作助手，让你的小红书内容更有吸引力</p>
        </div>

        <!-- API配置状态 -->
        {% if not is_configured %}
        <div class="alert alert-warning" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>请先配置API密钥！</strong>
            <button class="btn btn-sm btn-outline-warning ms-2" onclick="showConfigModal()">
                <i class="fas fa-cog me-1"></i>配置API
            </button>
        </div>
        {% endif %}

        <!-- 内容生成表单 -->
        <div class="card">
            <div class="card-header bg-gradient text-white" style="background: linear-gradient(45deg, #e91e63, #f06292);">
                <h5 class="card-title mb-0">
                    <i class="fas fa-edit me-2"></i>内容生成
                </h5>
            </div>
            <div class="card-body">
                <form id="generateForm">
                    <div class="row">
                        <!-- 关键词输入 -->
                        <div class="col-md-6 mb-3">
                            <label for="keywords" class="form-label">
                                <i class="fas fa-tags me-1"></i>关键词
                            </label>
                            <input type="text" class="form-control" id="keywords" 
                                   placeholder="输入关键词，多个用逗号分隔" required>
                            <div class="form-text">例如：护肤品推荐，美妆教程</div>
                        </div>

                        <!-- 内容模板 -->
                        <div class="col-md-6 mb-3">
                            <label for="template" class="form-label">
                                <i class="fas fa-layer-group me-1"></i>内容模板
                            </label>
                            <select class="form-select" id="template" required>
                                <option value="">选择内容模板</option>
                                {% for template in templates %}
                                <option value="{{ template.id }}">{{ template.name }} - {{ template.category }}</option>
                                {% endfor %}
                            </select>
                        </div>

                        <!-- AI模型选择 -->
                        <div class="col-md-6 mb-3">
                            <label for="model" class="form-label">
                                <i class="fas fa-robot me-1"></i>AI模型
                            </label>
                            <select class="form-select" id="model" required>
                                <option value="">选择AI模型</option>
                                {% for model in models %}
                                <option value="{{ model.id }}" data-platform="{{ model.platform }}">
                                    {{ model.name }} ({{ model.platform }})
                                </option>
                                {% endfor %}
                            </select>
                        </div>

                        <!-- 自定义提示词 -->
                        <div class="col-12 mb-3">
                            <label for="customPrompt" class="form-label">
                                <i class="fas fa-comment-dots me-1"></i>自定义提示词 (可选)
                            </label>
                            <textarea class="form-control" id="customPrompt" rows="3" 
                                      placeholder="输入自定义提示词来覆盖默认模板..."></textarea>
                        </div>

                        <!-- 生成按钮 -->
                        <div class="col-12 text-center">
                            <button type="submit" class="btn btn-primary btn-lg" id="generateBtn">
                                <i class="fas fa-magic me-2"></i>生成内容
                            </button>
                            <button class="btn btn-outline-primary ms-2" type="button" onclick="showConfigModal()">
                                <i class="fas fa-cog me-1"></i>API配置
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- 生成结果 -->
        <div id="resultContainer" style="display: none;">
            <div class="content-result position-relative">
                <button class="copy-btn" onclick="copyToClipboard('resultTitle', '标题')">
                    <i class="fas fa-copy me-1"></i>复制标题
                </button>
                <div class="content-title" id="resultTitle"></div>
                <hr>
                <button class="copy-btn" onclick="copyToClipboard('resultContent', '内容')" style="top: 60px;">
                    <i class="fas fa-copy me-1"></i>复制内容
                </button>
                <div class="content-text" id="resultContent"></div>
            </div>
        </div>

        <!-- 加载状态 -->
        <div id="loadingContainer" style="display: none;">
            <div class="text-center py-5">
                <div class="spinner-border spinner-border-lg" role="status">
                    <span class="visually-hidden">生成中...</span>
                </div>
                <p class="mt-3 text-muted">AI正在为您生成精彩内容，请稍候...</p>
            </div>
        </div>
    </div>
</div>

<!-- API配置模态框 -->
<div class="modal fade" id="configModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-cog me-2"></i>API配置
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="configForm">
                    <!-- SiliconFlow配置 -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0">SiliconFlow API</h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="siliconflowApiKey" class="form-label">API密钥</label>
                                <input type="password" class="form-control" id="siliconflowApiKey" 
                                       placeholder="输入SiliconFlow API密钥">
                            </div>
                            <div class="mb-3">
                                <label for="siliconflowBaseUrl" class="form-label">API地址</label>
                                <input type="url" class="form-control" id="siliconflowBaseUrl" 
                                       value="https://api.siliconflow.cn/v1">
                            </div>
                        </div>
                    </div>

                    <!-- MetaChat配置 -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0">MetaChat API</h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="metachatApiKey" class="form-label">API密钥</label>
                                <input type="password" class="form-control" id="metachatApiKey" 
                                       placeholder="输入MetaChat API密钥">
                            </div>
                            <div class="mb-3">
                                <label for="metachatBaseUrl" class="form-label">API地址</label>
                                <input type="url" class="form-control" id="metachatBaseUrl" 
                                       value="https://api.metachat.cn/v1">
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveConfig()">
                    <i class="fas fa-save me-1"></i>保存配置
                </button>
                <button type="button" class="btn btn-outline-primary" onclick="testConnection()">
                    <i class="fas fa-plug me-1"></i>测试连接
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 全局变量
let configModal;

$(document).ready(function() {
    // 初始化模态框
    configModal = new bootstrap.Modal(document.getElementById('configModal'));
    
    // 加载API配置
    loadConfig();
    
    // 表单提交事件
    $('#generateForm').on('submit', function(e) {
        e.preventDefault();
        generateContent();
    });
});

// 显示配置模态框
function showConfigModal() {
    configModal.show();
}

// 加载API配置
function loadConfig() {
    $.get('/api/config')
        .done(function(data) {
            if (data.siliconflow) {
                $('#siliconflowBaseUrl').val(data.siliconflow.base_url || '');
            }
            if (data.metachat) {
                $('#metachatBaseUrl').val(data.metachat.base_url || '');
            }
        })
        .fail(function() {
            console.log('加载配置失败');
        });
}

// 保存配置
function saveConfig() {
    const config = {
        siliconflow: {
            api_key: $('#siliconflowApiKey').val(),
            base_url: $('#siliconflowBaseUrl').val()
        },
        metachat: {
            api_key: $('#metachatApiKey').val(),
            base_url: $('#metachatBaseUrl').val()
        }
    };
    
    $.ajax({
        url: '/api/config',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(config),
        success: function(response) {
            if (response.success) {
                showAlert('success', '配置保存成功！页面将刷新...');
                setTimeout(() => location.reload(), 1500);
            } else {
                showAlert('danger', '配置保存失败：' + response.error);
            }
        },
        error: function() {
            showAlert('danger', '配置保存失败，请检查网络连接');
        }
    });
}

// 测试连接
function testConnection() {
    $.post('/api/test_connection')
        .done(function(response) {
            if (response.success) {
                const results = response.data;
                let message = '连接测试结果：\n';
                message += `SiliconFlow: ${results.siliconflow ? '✅ 成功' : '❌ 失败'}\n`;
                message += `MetaChat: ${results.metachat ? '✅ 成功' : '❌ 失败'}`;
                alert(message);
            } else {
                showAlert('danger', '连接测试失败：' + response.error);
            }
        })
        .fail(function() {
            showAlert('danger', '连接测试失败，请检查网络连接');
        });
}

// 生成内容
function generateContent() {
    const keywords = $('#keywords').val().trim();
    const template_id = $('#template').val();
    const model_id = $('#model').val();
    const custom_prompt = $('#customPrompt').val().trim();
    
    if (!keywords || !template_id || !model_id) {
        showAlert('warning', '请填写完整的生成参数');
        return;
    }
    
    // 显示加载状态
    $('#loadingContainer').show();
    $('#resultContainer').hide();
    $('#generateBtn').prop('disabled', true);
    
    const data = {
        keywords: keywords,
        template_id: template_id,
        model_id: model_id,
        custom_prompt: custom_prompt
    };
    
    $.ajax({
        url: '/api/generate',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(data),
        success: function(response) {
            $('#loadingContainer').hide();
            $('#generateBtn').prop('disabled', false);
            
            if (response.success) {
                $('#resultTitle').text(response.data.title);
                $('#resultContent').text(response.data.content);
                $('#resultContainer').show();
                showAlert('success', '内容生成成功！');
            } else {
                showAlert('danger', '生成失败：' + response.error);
            }
        },
        error: function() {
            $('#loadingContainer').hide();
            $('#generateBtn').prop('disabled', false);
            showAlert('danger', '生成失败，请检查网络连接');
        }
    });
}

// 复制到剪贴板
function copyToClipboard(elementId, type) {
    const text = document.getElementById(elementId).textContent;
    navigator.clipboard.writeText(text).then(function() {
        showAlert('success', type + '已复制到剪贴板');
    }).catch(function() {
        showAlert('warning', '复制失败，请手动复制');
    });
}

// 显示提示信息
function showAlert(type, message) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    $('.container').prepend(alertHtml);
    
    // 3秒后自动消失
    setTimeout(function() {
        $('.alert').alert('close');
    }, 3000);
}
</script>
{% endblock %}
