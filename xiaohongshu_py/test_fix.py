#!/usr/bin/env python3
"""
艹！老王写的修复验证脚本
测试JSON解析修复是否有效！
"""

import os
import sys
import json

# 设置Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)
os.chdir(current_dir)

from services.siliconflow_api import SiliconFlowAPI

def test_json_parsing():
    """测试JSON解析修复"""
    print("🧪 测试JSON解析修复")
    print("=" * 50)
    
    # 模拟AI返回的原始内容（从你的日志中复制）
    raw_content = '''{\n  "title": "🔥自用5年护肤品推荐！敏感肌变水光肌的秘诀✨",\n  "content": "姐妹们！今天要分享我5年护肤生涯中最爱的护肤品清单🧾从敏感泛红到现在的稳定水光肌，真的亲测有效！\n\n💧晨间护肤步骤：\n1. 珂润泡沫洁面（温和不紧绷）\n2. 悦木之源菌菇水（湿敷效果绝了！）\n3. 雅诗兰黛小棕瓶（维稳一把手）\n4. 薇诺娜特护霜（国货之光！）\n\n🌙晚间加强版：\n1. 植村秀卸妆油（乳化超快不闷痘）\n2. SKII神仙水（用了3瓶皮肤透亮度up）\n3. 修丽可色修精华（红血丝克星！）\n4. 海蓝之谜经典面霜（贵但值得！）\n\n🌟效果对比：\n之前：T区油两颊干，经常过敏起小红疹\n现在：水油平衡，素颜也有健康光泽感\n\n💖个人感受：\n最惊喜的是修丽可色修！之前换季必过敏，现在居然平安度过～而且性价比超高！\n\n小tips：护肤品一定要坚持用28天！不要三天打鱼两天晒网哦～\n\n你们最近在用啥神仙护肤品？评论区交流呀～👇"\n}'''
    
    print("📝 原始内容:")
    print(repr(raw_content))
    print()
    
    # 创建一个临时的API实例来测试解析方法
    class TestAPI:
        def _extract_with_regex(self, content: str):
            """使用正则表达式提取"""
            import re
            
            title = ""
            content_text = ""
            
            # 提取title
            title_match = re.search(r'"title"\s*:\s*"([^"]*(?:\\.[^"]*)*)"', content, re.DOTALL)
            if title_match:
                title = title_match.group(1)
                # 处理转义字符
                try:
                    title = title.encode().decode('unicode_escape')
                except:
                    pass
            
            # 提取content
            content_match = re.search(r'"content"\s*:\s*"([^"]*(?:\\.[^"]*)*)"', content, re.DOTALL)
            if content_match:
                content_text = content_match.group(1)
                # 处理转义字符
                try:
                    content_text = content_text.encode().decode('unicode_escape')
                except:
                    pass
            
            return {"title": title, "content": content_text}
    
    test_api = TestAPI()
    
    # 测试1: 直接JSON解析（strict=False）
    print("1️⃣ 测试直接JSON解析 (strict=False):")
    try:
        result = json.loads(raw_content, strict=False)
        print("✅ 解析成功!")
        print(f"标题: {result['title']}")
        print(f"内容前100字: {result['content'][:100]}...")
        return result
    except Exception as e:
        print(f"❌ 解析失败: {e}")
    print()
    
    # 测试2: 正则表达式提取
    print("2️⃣ 测试正则表达式提取:")
    try:
        result = test_api._extract_with_regex(raw_content)
        print("✅ 提取成功!")
        print(f"标题: {result['title']}")
        print(f"内容前100字: {result['content'][:100]}...")
        return result
    except Exception as e:
        print(f"❌ 提取失败: {e}")
    print()
    
    return None

def main():
    """主函数"""
    print("🛠️ 老王的JSON解析修复验证")
    print("=" * 50)
    
    result = test_json_parsing()
    
    if result:
        print("\n🎉 修复成功！")
        print("=" * 50)
        print("📋 最终结果:")
        print(f"标题: {result['title']}")
        print(f"内容: {result['content']}")
        print("=" * 50)
        print("✅ 标题和内容正确分离，没有错位问题！")
    else:
        print("\n❌ 修复失败，需要进一步调试")

if __name__ == '__main__':
    main()
