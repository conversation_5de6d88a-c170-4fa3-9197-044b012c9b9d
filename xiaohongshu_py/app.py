"""
艹！小红书内容生成器主应用
老王我写的Flask应用，稳得一批！
"""

import os
import json
import logging
from datetime import datetime
from typing import List, Dict, Any
from flask import Flask, render_template, request, jsonify, redirect, url_for, flash
from werkzeug.exceptions import BadRequest

import sys
import os

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from services.api_manager import APIManager
from models.content import GeneratedContent, PROMPT_TEMPLATES, API_MODELS

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

app = Flask(__name__)
app.secret_key = 'xiaohongshu_generator_secret_key_by_laowang'

# 全局变量
api_manager = None
history_file = 'data/history.json'


def load_config() -> Dict[str, Any]:
    """加载配置文件"""
    config_file = 'config/config.json'
    if not os.path.exists(config_file):
        logger.warning("配置文件不存在，使用默认配置")
        return {}
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"加载配置文件失败: {str(e)}")
        return {}


def save_config(config: Dict[str, Any]) -> bool:
    """保存配置文件"""
    try:
        os.makedirs('config', exist_ok=True)
        with open('config/config.json', 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        logger.error(f"保存配置文件失败: {str(e)}")
        return False


def load_history() -> List[GeneratedContent]:
    """加载历史记录"""
    if not os.path.exists(history_file):
        return []
    
    try:
        with open(history_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
            return [GeneratedContent.from_dict(item) for item in data]
    except Exception as e:
        logger.error(f"加载历史记录失败: {str(e)}")
        return []


def save_history(history: List[GeneratedContent]) -> bool:
    """保存历史记录"""
    try:
        os.makedirs('data', exist_ok=True)
        data = [item.to_dict() for item in history]
        with open(history_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        logger.error(f"保存历史记录失败: {str(e)}")
        return False


def init_api_manager():
    """初始化API管理器"""
    global api_manager
    config = load_config()
    
    siliconflow_config = config.get('siliconflow')
    metachat_config = config.get('metachat')
    
    api_manager = APIManager(siliconflow_config, metachat_config)
    logger.info("API管理器初始化完成")


@app.route('/')
def index():
    """主页"""
    templates = [t.to_dict() for t in PROMPT_TEMPLATES]
    models = [m.to_dict() for m in api_manager.get_available_models()] if api_manager else []
    is_configured = api_manager.is_configured() if api_manager else False
    
    return render_template('index.html', 
                         templates=templates, 
                         models=models,
                         is_configured=is_configured)


@app.route('/history')
def history():
    """历史记录页面"""
    history_list = load_history()
    return render_template('history.html', history=history_list)


@app.route('/api/generate', methods=['POST'])
def generate_content():
    """生成内容API"""
    try:
        data = request.get_json()
        if not data:
            raise BadRequest("艹！请求数据为空！")
        
        keywords = data.get('keywords', '').strip()
        template_id = data.get('template_id', '')
        model_id = data.get('model_id', '')
        custom_prompt = data.get('custom_prompt', '').strip()
        
        if not keywords:
            return jsonify({'success': False, 'error': '关键词不能为空！'})
        
        if not template_id:
            return jsonify({'success': False, 'error': '请选择内容模板！'})
        
        if not model_id:
            return jsonify({'success': False, 'error': '请选择AI模型！'})
        
        if not api_manager or not api_manager.is_configured():
            return jsonify({'success': False, 'error': '请先配置API密钥！'})
        
        # 生成内容
        result = api_manager.generate_content(
            keywords=keywords,
            template_id=template_id,
            model_id=model_id,
            custom_prompt=custom_prompt if custom_prompt else None
        )
        
        # 保存到历史记录
        keywords_list = [k.strip() for k in keywords.replace('，', ',').split(',') if k.strip()]
        content = GeneratedContent(
            title=result['title'],
            content=result['content'],
            model=model_id,
            keywords=keywords_list
        )
        
        history_list = load_history()
        history_list.insert(0, content)
        
        # 只保留最近100条记录
        if len(history_list) > 100:
            history_list = history_list[:100]
        
        save_history(history_list)
        
        return jsonify({
            'success': True,
            'data': {
                'title': result['title'],
                'content': result['content'],
                'id': content.id
            }
        })
        
    except Exception as e:
        logger.error(f"生成内容失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})


@app.route('/api/config', methods=['GET', 'POST'])
def api_config():
    """API配置"""
    if request.method == 'GET':
        config = load_config()
        # 隐藏API密钥的敏感信息
        safe_config = {}
        for platform in ['siliconflow', 'metachat']:
            if platform in config:
                safe_config[platform] = {
                    'base_url': config[platform].get('base_url', ''),
                    'api_key_configured': bool(config[platform].get('api_key'))
                }
        return jsonify(safe_config)
    
    elif request.method == 'POST':
        try:
            data = request.get_json()
            config = load_config()
            
            # 更新配置
            for platform in ['siliconflow', 'metachat']:
                if platform in data:
                    if platform not in config:
                        config[platform] = {}
                    
                    platform_data = data[platform]
                    if 'base_url' in platform_data:
                        config[platform]['base_url'] = platform_data['base_url']
                    if 'api_key' in platform_data and platform_data['api_key']:
                        config[platform]['api_key'] = platform_data['api_key']
            
            # 保存配置
            if save_config(config):
                # 重新初始化API管理器
                init_api_manager()
                return jsonify({'success': True, 'message': '配置保存成功！'})
            else:
                return jsonify({'success': False, 'error': '配置保存失败！'})
                
        except Exception as e:
            logger.error(f"保存配置失败: {str(e)}")
            return jsonify({'success': False, 'error': str(e)})


@app.route('/api/test_connection', methods=['POST'])
def test_connection():
    """测试API连接"""
    try:
        if not api_manager:
            return jsonify({'success': False, 'error': '请先配置API！'})
        
        results = api_manager.test_connections()
        return jsonify({'success': True, 'data': results})
        
    except Exception as e:
        logger.error(f"测试连接失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})


if __name__ == '__main__':
    # 创建必要的目录
    os.makedirs('config', exist_ok=True)
    os.makedirs('data', exist_ok=True)
    os.makedirs('static/css', exist_ok=True)
    os.makedirs('static/js', exist_ok=True)
    os.makedirs('templates', exist_ok=True)
    
    # 初始化API管理器
    init_api_manager()
    
    # 加载配置
    config = load_config()
    app_config = config.get('app', {})
    
    host = app_config.get('host', '0.0.0.0')
    port = app_config.get('port', 5000)
    debug = app_config.get('debug', True)
    
    logger.info(f"艹！老王的小红书生成器启动了！访问地址: http://{host}:{port}")
    app.run(host=host, port=port, debug=debug)
