#!/usr/bin/env python3
"""
艹！老王专门为虚拟环境写的启动脚本
这个脚本解决了相对导入的问题！
"""

import os
import sys
import json
import logging

def setup_python_path():
    """设置Python路径"""
    # 获取当前脚本所在目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 添加到Python路径
    if current_dir not in sys.path:
        sys.path.insert(0, current_dir)
    
    # 切换到项目目录
    os.chdir(current_dir)
    
    print(f"📁 工作目录: {current_dir}")
    print(f"🐍 Python路径已设置")

def check_dependencies():
    """检查依赖是否安装"""
    try:
        import flask
        import requests
        print("✅ 依赖检查通过")
        return True
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("请运行: pip install -r requirements.txt")
        return False

def create_directories():
    """创建必要的目录"""
    dirs = ['config', 'data', 'static/css', 'static/js', 'templates']
    for dir_path in dirs:
        os.makedirs(dir_path, exist_ok=True)
    print("✅ 目录结构检查完成")

def check_config():
    """检查配置文件"""
    config_file = 'config/config.json'
    
    if not os.path.exists(config_file):
        print("⚠️  配置文件不存在，启动后请在Web界面配置API密钥")
    else:
        print("✅ 配置文件存在")

def main():
    """主函数"""
    print("🚀 老王的小红书生成器启动检查...")
    print("=" * 50)
    
    # 设置Python路径
    setup_python_path()
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    # 创建目录
    create_directories()
    
    # 检查配置
    check_config()
    
    print("=" * 50)
    print("🎉 检查完成，正在启动应用...")
    
    # 启动Flask应用
    try:
        # 直接导入并运行
        from flask import Flask
        from werkzeug.exceptions import BadRequest
        import logging
        from datetime import datetime
        from typing import List, Dict, Any
        
        # 导入项目模块
        from services.api_manager import APIManager
        from models.content import GeneratedContent, PROMPT_TEMPLATES, API_MODELS
        
        print("✅ 模块导入成功")
        
        # 启动应用
        exec(open('app.py').read())
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == '__main__':
    main()
