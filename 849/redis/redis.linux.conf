# Redis configuration for 849 pad service
bind 127.0.0.1
protected-mode yes
port 6378
tcp-backlog 511
timeout 0
tcp-keepalive 60

# 基本设置
loglevel notice
logfile ""
databases 16

# 持久化设置
save 900 1
save 300 10
save 60 10000
stop-writes-on-bgsave-error yes
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb
dir ./

# AOF设置
appendonly yes
appendfilename "appendonly.aof"
appendfsync everysec
no-appendfsync-on-rewrite no
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb
aof-load-truncated yes

# 其他优化设置
activerehashing yes
hz 10
aof-rewrite-incremental-fsync yes
