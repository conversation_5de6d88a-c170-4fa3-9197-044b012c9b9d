# 协议版本选择
[Protocol]
version = "849"            # 协议版本，可选值: "849", "855" 或 "ipad"，暂时别用855

[WechatAPIServer]
port = 9011                # WechatAPI服务器端口，默认9000，如有冲突可修改
mode = "release"           # 运行模式：release(生产环境)，debug(调试模式)
redis-host = "127.0.0.1"   # Redis服务器地址，本地使用127.0.0.1
redis-port = 6379          # Redis端口，使用系统Redis服务的默认端口
redis-password = ""        # Redis密码，如果有设置密码则填写
redis-db = 0               # Redis数据库编号，默认0

# 管理后台设置
[Admin]
enabled = true             # 是否启用管理后台
host = "0.0.0.0"           # 管理后台监听地址，使用0.0.0.0允许外部访问
port = 9090                # 管理后台端口，修改为9090
username = "admin"         # 管理后台登录用户名
password = "admin1234"      # 管理后台登录密码
debug = true               # 是否开启调试模式

# XYBot 核心设置
[XYBot]
version = "v1.0.0"                    # 版本号，请勿修改
ignore-protection = true             # 是否忽略风控保护机制，建议保持false

# GitHub加速服务设置
# 可选值: "", "https://ghfast.top/", "https://gh-proxy.com/", "https://mirror.ghproxy.com/"
# 空字符串表示直连不使用加速
# 注意: 如果使用加速服务，请确保以"/"结尾
github-proxy = "https://ghfast.top/"

# SQLite数据库地址，一般无需修改
XYBotDB-url = "sqlite:///database/xybot.db"
msgDB-url = "sqlite+aiosqlite:///database/message.db"
keyvalDB-url = "sqlite+aiosqlite:///database/keyval.db"

# 管理员设置
admins = ["wxid_lnbsshdobq7y22"]  # 管理员的wxid列表，可从消息日志中获取
disabled-plugins = ["ExamplePlugin", "TencentLke","FastGPT","OpenAIAPI","SiliconFlow"]   # 禁用的插件列表，不需要的插件名称填在这里
timezone = "Asia/Shanghai"             # 时区设置，中国用户使用 Asia/Shanghai

# 实验性功能，如果main_config.toml配置改动，或者plugins文件夹有改动，自动重启。可以在开发时使用，不建议在生产环境使用。
auto-restart = false                 # 仅建议在开发时启用，生产环境保持false

# 自动重启监控器设置
[AutoRestart]
enabled = true                      # 是否启用自动重启监控器
check-interval = 60                 # 检查间隔（秒）
offline-threshold = 300             # 离线阈值（秒），超过这个时间没有状态更新就触发重启
max-restart-attempts = 3            # 最大重启尝试次数
restart-cooldown = 1800             # 重启冷却时间（秒），两次重启之间的最小间隔
check-offline-trace = true         # 是否检查掉线追踪，如果为true，则仅在检测到"获取新消息失败"的日志时触发重启
failure-count-threshold = 10       # 连续失败次数阈值，检测到达到这个次数的"获取新消息失败"时才触发重启
reset-threshold-multiplier = 3     # 重置阈值倍数，失败计数器重置时间 = 离线阈值 * 此值

# 消息过滤设置
ignore-mode = "None"            # 消息处理模式：
# "None" - 处理所有消息
# "Whitelist" - 仅处理白名单消息
# "Blacklist" - 屏蔽黑名单消息

whitelist = [# 白名单列表
    "wxid_uz9za1pqr3ea22",  # 您的个人微信ID
    "wxid_l5im9jaxhr4412",  # 您的另一个微信ID
    "48369192388@chatroom"  # 您的群聊ID
]

blacklist = [# 黑名单列表
    "wxid_3", # 个人用户微信ID
    "wxid_4",
    "333@chatroom", # 群聊ID
    "444@chatroom"
]

# 系统通知设置
[Notification]
enabled = true                      # 是否启用通知功能
token = ""                          # PushPlus Token，请在 http://www.pushplus.plus/ 注册并填写您的Token
channel = "wechat"                  # 通知渠道：wechat(微信公众号)、sms(短信)、mail(邮件)、webhook、cp(企业微信)
template = "html"                   # 通知模板
topic = ""                          # 群组编码，不填仅发送给自己
heartbeatThreshold = 3              # 心跳失败阈值，连续失败多少次判定为离线

# 通知触发条件
[Notification.triggers]
offline = true                      # 微信离线时通知
reconnect = true                   # 微信重新连接时通知
restart = true                      # 系统重启时通知
error = true                        # 系统错误时通知

# 通知模板设置
[Notification.templates]
offlineTitle = "警告：微信离线通知 - {time}"  # 离线通知标题
offlineContent = "您的微信账号 <b>{wxid}</b> 已于 <span style=\"color:#ff4757;font-weight:bold;\">{time}</span> 离线，请尽快检查您的设备连接状态或重新登录。"  # 离线通知内容
reconnectTitle = "微信重新连接通知 - {time}"  # 重连通知标题
reconnectContent = "您的微信账号 <b>{wxid}</b> 已于 <span style=\"color:#2ed573;font-weight:bold;\">{time}</span> 重新连接。"  # 重连通知内容
restartTitle = "系统重启通知 - {time}"  # 系统重启通知标题
restartContent = "系统已于 <span style=\"color:#1e90ff;font-weight:bold;\">{time}</span> 重新启动。"  # 系统重启通知内容