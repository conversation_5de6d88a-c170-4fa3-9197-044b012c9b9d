from Plugins._Tools import Tools
from Config.logger import logger
from Core.PluginBase import PluginBase
import os
import time
import httpx
from pathlib import Path

class QuarkSignInPlugin(PluginBase):
    def __init__(self):
        super().__init__()
        # 基本信息（重要：name必须与目录名一致）
        self.name = "QuarkSignIn"
        self.description = "夸克网盘自动签到插件"
        self.version = "1.0.0"
        self.author = "移植自XYBot"
        
        # 工具类实例
        self.tools = Tools()
        
        # 创建临时目录
        self.temp_dir = Path("App/Plugins/QuarkSignIn/temp")
        self.temp_dir.mkdir(parents=True, exist_ok=True)
        
        # 加载配置文件
        self.configData = self.tools.returnConfigData(os.path.dirname(__file__))
        self._load_config()
        
        # 用户冷却时间记录
        self.user_last_request = {}
        
        # 注意：self.dp 会由 PluginManager 自动设置，无需手动实例化

        # 定时任务设置标志
        self._scheduled_task_setup = False

    def _load_config(self):
        """加载配置"""
        self.enabled = self.configData.get('enabled', True)
        self.natural_response = self.configData.get('natural_response', False)
        self.commands = self.configData.get('commands', ["夸克签到", "夸克"])
        self.command_format = self.configData.get('command_format', "使用方法：夸克签到 - 执行夸克网盘签到任务")
        self.cooldown = self.configData.get('cooldown', 10)
        
        # 自动签到配置
        self.auto_signin_enable = self.configData.get('auto_signin_enable', True)
        self.auto_signin_hour = self.configData.get('auto_signin_hour', 8)
        self.auto_signin_minute = self.configData.get('auto_signin_minute', 0)
        self.notification_wxid = self.configData.get('notification_wxid', "")
        
        # Cookie配置
        self.cookie_list = self.configData.get('cookie_list', [])

    def _setup_scheduled_task(self):
        """设置定时任务"""
        if not self._message_handler:
            logger.error(f"[{self.name}] MessageHandler未设置，无法添加定时任务")
            return False

        success = self.add_scheduled_task(
            "auto_signin",
            self.auto_signin_hour,
            self.auto_signin_minute,
            self._auto_signin_task
        )
        if success:
            logger.info(f"[{self.name}] 已设置定时签到任务，时间: {self.auto_signin_hour:02d}:{self.auto_signin_minute:02d}")
        else:
            logger.error(f"[{self.name}] 设置定时签到任务失败")
        return success

    def _check_and_update_scheduled_task(self):
        """检查并更新定时任务配置"""
        if not self._message_handler:
            return

        # 重新加载配置
        old_hour = self.auto_signin_hour
        old_minute = self.auto_signin_minute
        self._load_config()

        # 检查时间是否有变化
        if old_hour != self.auto_signin_hour or old_minute != self.auto_signin_minute:
            logger.info(f"[{self.name}] 检测到定时任务时间变化: {old_hour:02d}:{old_minute:02d} -> {self.auto_signin_hour:02d}:{self.auto_signin_minute:02d}")
            # 重新设置定时任务（会覆盖原有任务）
            self._setup_scheduled_task()

    async def _auto_signin_task(self):
        """自动签到任务"""
        try:
            logger.info(f"[{self.name}] 开始执行定时自动签到任务")
            
            # 执行签到任务
            result = await self._process_signin()
            
            # 发送通知到指定微信ID
            if self.notification_wxid:
                notification_msg = f"🤖 夸克网盘自动签到完成\n\n{result}"
                # 获取机器人自身的微信ID
                if hasattr(self._message_handler, 'self_wxid') and self._message_handler.self_wxid:
                    bot_wxid = self._message_handler.self_wxid
                    await self.dp.sendText(notification_msg, self.notification_wxid, bot_wxid)
                    logger.info(f"[{self.name}] 已发送签到通知到: {self.notification_wxid}")
                else:
                    logger.error(f"[{self.name}] 无法获取机器人微信ID，通知发送失败")
            else:
                logger.warning(f"[{self.name}] 未配置通知微信ID，跳过通知发送")
                
        except Exception as e:
            logger.error(f"[{self.name}] 定时自动签到异常: {e}")
            
            # 发送错误通知
            if self.notification_wxid:
                error_msg = f"❌ 夸克网盘自动签到失败\n错误信息: {str(e)}"
                try:
                    await self.dp.sendText(error_msg, self.notification_wxid, self.notification_wxid)
                except Exception as notify_error:
                    logger.error(f"[{self.name}] 发送错误通知失败: {notify_error}")

    def convert_bytes(self, b):
        """将字节转换为 MB GB TB"""
        units = ("B", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB")
        i = 0
        while b >= 1024 and i < len(units) - 1:
            b /= 1024
            i += 1
        return f"{b:.2f} {units[i]}"

    async def get_growth_info(self, user_data):
        """获取用户当前的签到信息"""
        url = "https://drive-m.quark.cn/1/clouddrive/capacity/growth/info"
        querystring = {
            "pr": "ucpro",
            "fr": "android",
            "kps": user_data.get('kps'),
            "sign": user_data.get('sign'),
            "vcode": user_data.get('vcode')
        }

        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Referer': 'https://pan.quark.cn/',
        }

        try:
            async with httpx.AsyncClient(timeout=10) as client:
                response = await client.get(url=url, params=querystring, headers=headers)
                logger.debug(f"[{self.name}] API响应状态码: {response.status_code}")
                
                response_json = response.json()
                if response_json.get("data"):
                    return response_json["data"]
                else:
                    logger.error(f"[{self.name}] API返回错误: {response_json}")
                    return False
        except Exception as e:
            logger.error(f"[{self.name}] 请求异常: {e}")
            return False

    async def get_growth_sign(self, user_data):
        """执行签到"""
        url = "https://drive-m.quark.cn/1/clouddrive/capacity/growth/sign"
        querystring = {
            "pr": "ucpro",
            "fr": "android",
            "kps": user_data.get('kps'),
            "sign": user_data.get('sign'),
            "vcode": user_data.get('vcode')
        }

        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Referer': 'https://pan.quark.cn/',
            'Content-Type': 'application/json',
        }

        data = {"sign_cyclic": True}

        try:
            async with httpx.AsyncClient(timeout=10) as client:
                response = await client.post(url=url, json=data, params=querystring, headers=headers)
                logger.debug(f"[{self.name}] 签到响应状态码: {response.status_code}")
                
                response_json = response.json()
                if response_json.get("data"):
                    return True, response_json["data"]["sign_daily_reward"]
                else:
                    return False, response_json.get("message", "未知错误")
        except Exception as e:
            logger.error(f"[{self.name}] 签到请求异常: {e}")
            return False, str(e)

    async def do_sign_for_user(self, user_data):
        """为单个用户执行签到任务"""
        log = ""
        
        # 获取签到信息
        growth_info = await self.get_growth_info(user_data)
        if growth_info:
            user_type = "88VIP" if growth_info['88VIP'] else "普通用户"
            username = user_data.get('user', '未知用户')
            total_capacity = self.convert_bytes(growth_info['total_capacity'])
            
            log += f"👤 {user_type} {username}\n"
            log += f"💾 网盘总容量：{total_capacity}\n"
            
            # 签到累计容量
            if "sign_reward" in growth_info['cap_composition']:
                sign_capacity = self.convert_bytes(growth_info['cap_composition']['sign_reward'])
                log += f"📈 签到累计容量：{sign_capacity}\n"
            else:
                log += f"📈 签到累计容量：0 MB\n"
            
            # 检查今日是否已签到
            if growth_info["cap_sign"]["sign_daily"]:
                daily_reward = self.convert_bytes(growth_info['cap_sign']['sign_daily_reward'])
                progress = growth_info['cap_sign']['sign_progress']
                target = growth_info['cap_sign']['sign_target']
                log += f"✅ 今日已签到+{daily_reward}，连签进度({progress}/{target})\n"
            else:
                # 执行签到
                sign_success, sign_return = await self.get_growth_sign(user_data)
                if sign_success:
                    daily_reward = self.convert_bytes(sign_return)
                    progress = growth_info['cap_sign']['sign_progress'] + 1
                    target = growth_info['cap_sign']['sign_target']
                    log += f"🎉 今日签到成功+{daily_reward}，连签进度({progress}/{target})\n"
                else:
                    log += f"❌ 签到失败: {sign_return}\n"
        else:
            log += f"❌ 获取签到信息失败\n"
        
        return log

    async def _process_signin(self) -> str:
        """处理签到请求"""
        if not self.cookie_list:
            return "❌ 未配置夸克网盘Cookie信息"

        msg = f"🌟 夸克网盘签到开始\n"
        msg += f"📊 检测到 {len(self.cookie_list)} 个账号\n\n"

        for i, cookie_str in enumerate(self.cookie_list):
            # 解析Cookie
            user_data = {}
            for item in cookie_str.replace(" ", "").split(';'):
                if item and '=' in item:
                    key, value = item.split('=', 1)
                    user_data[key] = value

            msg += f"🔄 第{i + 1}个账号签到中...\n"

            try:
                log = await self.do_sign_for_user(user_data)
                msg += log + "\n"
            except Exception as e:
                error_msg = f"❌ 账号{i + 1}签到失败: {e}\n\n"
                logger.error(f"[{self.name}] {error_msg}")
                msg += error_msg

        msg += "✨ 夸克网盘签到完成"
        return msg

    def _check_user_limit(self, roomid: str, sender: str) -> float:
        """检查用户冷却时间"""
        user_key = f"{roomid}_{sender}"
        last_request = self.user_last_request.get(user_key, 0)
        current_time = time.time()
        elapsed = current_time - last_request
        self.user_last_request[user_key] = current_time

        if elapsed < self.cooldown:
            return self.cooldown - elapsed
        return 0

    async def handle_message(self, msg):
        """处理群聊消息"""
        if not self.enabled:
            return False

        # 延迟设置定时任务（确保MessageHandler已设置）
        if not self._scheduled_task_setup and self.auto_signin_enable:
            self._setup_scheduled_task()
            self._scheduled_task_setup = True
        elif self.auto_signin_enable:
            # 检查配置是否有变化，如果有则更新定时任务
            self._check_and_update_scheduled_task()

        content = msg.content.strip()
        target_id = msg.roomid if msg.from_group() else msg.sender

        # 检查是否是签到命令
        command = content.split(" ", 1)
        if command[0] not in self.commands:
            return False

        try:
            # 限流检查
            wait_time = self._check_user_limit(msg.roomid, msg.sender)
            if wait_time > 0:
                await self.dp.sendText(f"请等待 {wait_time:.1f} 秒", target_id, msg.self_wxid)
                return True

            # 执行签到任务
            result = await self._process_signin()

            # 发送结果
            await self.dp.sendText(result, target_id, msg.self_wxid)
            return True

        except Exception as e:
            logger.error(f"[{self.name}] 处理签到请求异常: {e}")
            await self.dp.sendText("签到处理失败", target_id, msg.self_wxid)
            return False

    async def handle_private_message(self, msg):
        """
        处理私聊消息
        复用群聊消息处理逻辑
        """
        return await self.handle_message(msg)

    def __del__(self):
        """析构函数，清理定时任务"""
        if self._timer_task and not self._timer_task.done():
            self._timer_task.cancel()
