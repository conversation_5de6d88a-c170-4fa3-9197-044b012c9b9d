from Plugins._Tools import Tools
from Config.logger import logger
from Core.PluginBase import PluginBase
import os
import json
import time
import uuid
import httpx
import asyncio
from typing import Optional, Dict, Any
from urllib.parse import quote

# 定义HTTP响应类，用于兼容旧插件代码
class HttpResponse:
    def __init__(self, status_code, body, headers=None):
        self.code = status_code
        self.status_code = status_code  # 兼容性
        self.body = body
        self.headers = headers or {}
        self._text = None

    def json(self):
        """解析 JSON 响应体"""
        if not self.body:
            return {}
        try:
            import json
            return json.loads(self.body)
        except (json.JSONDecodeError, TypeError):
            return {}

class DoubaoChatPlugin(PluginBase):
    def __init__(self):
        super().__init__()
        # 基本信息（重要：name必须与目录名一致）
        self.name = "DoubaoChat"
        self.description = "豆包AI对话插件"
        self.version = "1.0.0"
        self.author = "XYBot"

        # 工具类实例
        self.tools = Tools()

        # 加载配置文件
        self.configData = self.tools.returnConfigData(os.path.dirname(__file__))
        # 豆包对话：豆包+#开头
        self.keywords = ['豆包']
        self.api_config = self.configData.get('API', {})
        
        # 豆包API配置
        self.api_base_url = "https://www.doubao.com/samantha/chat/completion"
        self.cookies = self.api_config.get('cookies', '')
        
        # 豆包API常量
        self.device_id = "7468716989062841895"
        self.tea_uuid = "7468716986638386703"
        self.web_id = "7468716986638386703"
        
        # 会话模式配置
        self._conversation_mode = {}  # 群组ID -> 是否在会话模式
        self._conversation_users = {}  # 群组ID -> 用户ID
        self._conversation_timeout = 300  # 会话模式超时时间（秒）
        self._last_conversation_time = {}  # 群组ID -> 最后会话时间
        
        # httpx客户端管理
        self._client = None
        self._client_lock = asyncio.Lock()
        

        
        # 用户级别的请求限制
        self._user_limits = {}
        self.min_request_interval = 2.0
        


        # 排除的关键词（其他插件的功能）
        self.exclude_keywords = [
            # 画图相关
            "画一", "画个", "画图", "绘制", "生成图片", "创建图像", "创建图片",
            "生成一张", "生成一个", "生成一幅", "作画", "画出", "生成一套",
            "分镜", "插画", "漫画", "海报", "封面", "图像", "图集",
            # 视频相关 - 修复视频生成插件冲突
            "找点", "视频", "生成视频", "图生视频", "豆包视频",
            # 识图相关（这些通常不会和"豆包"一起出现，但为了完整性）
            "解释", "识别", "这是什么", "分析", "看看", "这张图", "图里有什么"
        ]



    async def get_httpx_client(self):
        """创建httpx客户端"""
        return httpx.AsyncClient(
            timeout=httpx.Timeout(connect=10.0, read=20.0, write=20.0, pool=5.0),
            verify=False,  # 禁用SSL验证
            follow_redirects=True,
            limits=httpx.Limits(max_connections=20, max_keepalive_connections=10),
        )

    async def get_session(self):
        """获取或创建httpx异步客户端"""
        async with self._client_lock:
            if self._client is None or self._client.is_closed:
                self._client = await self.get_httpx_client()
            return self._client

    async def close_session(self):
        """关闭httpx客户端"""
        async with self._client_lock:
            if self._client and not self._client.is_closed:
                await self._client.aclose()
                self._client = None

    def _generate_headers(self):
        """生成请求头"""
        # 生成随机的x-flow-trace
        flow_trace = f"04-{uuid.uuid4().hex[:16]}-{uuid.uuid4().hex[:16]}-01"

        return {
            "Accept": "*/*",
            "Accept-Encoding": "gzip, deflate",
            "Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
            "Connection": "keep-alive",
            "Content-Type": "application/json; charset=utf-8",
            "Cookie": self.cookies,
            "Host": "www.doubao.com",
            "Origin": "https://www.doubao.com",
            "Referer": "https://www.doubao.com/chat/",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "x-flow-trace": flow_trace,
            "Agw-Js-Conv": "str",
            "X-Requested-With": "mark.via",
            "last-event-id": "undefined",
            "Sec-Fetch-Site": "same-origin",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Dest": "empty"
        }

    async def handle_message(self, msg) -> bool:
        """处理消息"""
        try:
            # 只处理文本消息
            if msg.type != 1:
                return False

            content = msg.content.strip()
            target_id = msg.roomid if msg.from_group() else msg.sender
            
            # 检查会话模式
            if self._is_in_conversation_mode(target_id, msg.sender):
                # 在会话模式中，处理所有消息
                if content == "结束" or content == "豆包 结束":
                    await self._exit_conversation_mode(target_id)
                    await self.dp.sendText("已退出连续对话模式", target_id, msg.self_wxid)
                    return True
                else:
                    # 处理会话消息
                    await self._handle_conversation_message(msg, content)
                    return True
            
            # 检查关键词匹配：豆包+#开头
            if not content.startswith("豆包"):
                return False

            # 提取豆包后的内容
            after_doubao = content[2:].strip()  # 移除"豆包"
            if not after_doubao.startswith("#"):
                return False

            # 提取实际查询内容，必须有提示词
            query = after_doubao[1:].strip()  # 移除"#"
            if not query:
                return False

            logger.info(f"插件 {self.name} 处理消息: {content}")
            
            # 检查特殊命令
            if query == "对话":
                await self._enter_conversation_mode(target_id, msg.sender)
                await self.dp.sendText("已进入连续对话模式，直接发送消息即可对话，发送'结束'退出", target_id, msg.self_wxid)
                return True
            
            # 处理普通对话
            await self._handle_chat_request(msg, query)
            return True
            
        except Exception as e:
            logger.error(f"插件 {self.name} 处理消息时出错: {e}", exc_info=True)
            return True

    def _extract_query(self, content: str) -> str:
        """提取查询内容"""
        for keyword in self.keywords:
            if content.startswith(keyword):
                return content[len(keyword):].strip()
        return content.strip()

    def _is_in_conversation_mode(self, target_id: str, user_id: str) -> bool:
        """检查是否在会话模式"""
        if target_id not in self._conversation_mode:
            return False
            
        # 检查超时
        current_time = time.time()
        last_time = self._last_conversation_time.get(target_id, 0)
        if current_time - last_time > self._conversation_timeout:
            # 超时，自动退出会话模式
            self._exit_conversation_mode_internal(target_id)
            return False
            
        # 检查用户是否匹配
        return (self._conversation_mode.get(target_id, False) and 
                self._conversation_users.get(target_id) == user_id)

    async def _enter_conversation_mode(self, target_id: str, user_id: str):
        """进入会话模式"""
        self._conversation_mode[target_id] = True
        self._conversation_users[target_id] = user_id
        self._last_conversation_time[target_id] = time.time()

    async def _exit_conversation_mode(self, target_id: str):
        """退出会话模式"""
        self._exit_conversation_mode_internal(target_id)

    def _exit_conversation_mode_internal(self, target_id: str):
        """内部退出会话模式"""
        self._conversation_mode.pop(target_id, None)
        self._conversation_users.pop(target_id, None)
        self._last_conversation_time.pop(target_id, None)

    async def _handle_conversation_message(self, msg, content: str):
        """处理会话消息"""
        # 更新最后会话时间
        target_id = msg.roomid if msg.from_group() else msg.sender
        self._last_conversation_time[target_id] = time.time()
        
        # 处理对话
        await self._handle_chat_request(msg, content)

    async def _handle_chat_request(self, msg, query: str):
        """处理对话请求"""
        try:
            target_id = msg.roomid if msg.from_group() else msg.sender

            # 检查用户限制
            wait_time = self._check_user_limit(target_id, msg.sender)
            if wait_time > 0:
                return

            # 调用豆包API
            result = await self.call_doubao_api(query)

            if result and result.get("type") == "text":
                text = result.get("text", "")
                if text:
                    await self.dp.sendText(text, target_id, msg.self_wxid)

        except Exception as e:
            logger.error(f"处理对话请求失败: {e}", exc_info=True)



    def _check_user_limit(self, wxid: str, user_wxid: str) -> float:
        """检查用户是否可以发送请求"""
        current_time = time.time()

        if wxid not in self._user_limits:
            self._user_limits[wxid] = {}

        last_request = self._user_limits[wxid].get(user_wxid, 0)
        wait_time = max(0, self.min_request_interval - (current_time - last_request))

        if wait_time == 0:
            self._user_limits[wxid][user_wxid] = current_time

        return wait_time

    async def call_doubao_api(self, query: str) -> Optional[Dict[str, Any]]:
        """调用豆包API"""
        try:
                # 构造请求URL参数
                params = {
                    "aid": "497858",
                    "device_id": self.device_id,
                    "device_platform": "web",
                    "language": "zh",
                    "pc_version": "2.16.4",
                    "pkg_type": "release_version",
                    "real_aid": "497858",
                    "region": "CN",
                    "samantha_web": "1",
                    "sys_region": "CN",
                    "tea_uuid": self.tea_uuid,
                    "use-olympus-account": "1",
                    "version_code": "20800",
                    "web_id": self.web_id
                }

                # 创建随机的会话ID和消息ID
                conversation_id = f"38{int(time.time() * 10000)}8"
                section_id = f"{conversation_id[:10]}4{conversation_id[10:]}"
                message_id = f"ba0d6520-{uuid.uuid4().hex[:4]}-11f0-a188-0d4d{uuid.uuid4().hex[:8]}"

                # 构造请求数据
                request_data = {
                    "messages": [{
                        "content": json.dumps({"text": query}),
                        "content_type": 2001,
                        "attachments": [],
                        "references": []
                    }],
                    "completion_option": {
                        "is_regen": False,
                        "with_suggest": True,
                        "need_create_conversation": True,
                        "launch_stage": 1,
                        "is_replace": False,
                        "is_delete": False,
                        "message_from": 0,
                        "use_deep_think": False,
                        "use_auto_cot": False,
                        "event_id": "0"
                    },
                    "section_id": section_id,
                    "conversation_id": conversation_id,
                    "local_message_id": message_id
                }

                # 获取httpx客户端
                client = await self.get_session()

                # 设置请求头
                headers = self._generate_headers()

                # 构造完整URL
                url = f"{self.api_base_url}?" + "&".join([f"{k}={quote(str(v))}" for k,v in params.items()])

                # 发送POST请求
                response = await client.post(
                    url,
                    json=request_data,
                    headers=headers,
                    timeout=httpx.Timeout(connect=10.0, read=300.0, write=60.0, pool=10.0)
                )

                if response.status_code != 200:
                    error_text = response.content
                    logger.error(f"[DoubaoChat] API请求失败: 状态码={response.status_code}, 响应内容={error_text}")
                    raise ValueError(f"API请求失败: {response.status_code}")

                # 创建自定义响应对象
                http_response = HttpResponse(
                    status_code=response.status_code,
                    body=response.content,
                    headers=dict(response.headers)
                )

                # 处理响应
                result = await self._process_stream(http_response)

                if result:
                    return result

                return {"type": "text", "text": ""}

        except Exception as e:
            logger.error(f"[DoubaoChat] API调用失败: {e}")
            return {"type": "text", "text": ""}

    async def _process_stream(self, response) -> Optional[Dict[str, Any]]:
        """处理SSE响应流"""
        result_text = ""
        result_data = {"type": "text", "text": ""}

        try:
            # 解码响应数据
            if isinstance(response.body, str):
                chunk = response.body.encode('utf-8')
            else:
                chunk = response.body

            try:
                decoded_chunk = chunk.decode('utf-8', errors='ignore')
                buffer = decoded_chunk
            except UnicodeDecodeError:
                return None

            # 处理完整的SSE事件
            while "\n\n" in buffer:
                parts = buffer.split("\n\n", 1)
                event = parts[0]
                buffer = parts[1]

                if not event.strip():
                    continue

                # 提取"data:"行
                data_line = None
                for line in event.split("\n"):
                    if line.startswith("data:"):
                        data_line = line[5:].strip()
                        break

                if not data_line:
                    continue

                # 解析数据
                try:
                    event_data = json.loads(data_line)
                    if not isinstance(event_data, dict):
                        continue

                    if "event_type" not in event_data:
                        continue

                    event_type = event_data["event_type"]

                    # 处理结束事件
                    if event_type == 2003:
                        if "tts_content" in event_data:
                            full_text = event_data["tts_content"]
                            if full_text and len(full_text) > len(result_text):
                                result_text = full_text
                                result_data["text"] = result_text

                        result_data["text"] = result_text
                        return result_data

                    # 处理正常消息事件
                    if event_type == 2001 and "event_data" in event_data:
                        try:
                            inner_data = json.loads(event_data["event_data"])

                            if "message" not in inner_data:
                                continue

                            message = inner_data["message"]
                            is_finish = inner_data.get("is_finish", False)

                            if "content_type" not in message or "content" not in message:
                                continue

                            content_type = message["content_type"]
                            try:
                                content = json.loads(message["content"])
                            except json.JSONDecodeError:
                                continue

                            # 处理文本内容
                            if content_type == 2001:
                                if "text" in content:
                                    text = content["text"]
                                    result_text += text
                                    result_data["text"] = result_text

                                    # 当事件标记为完成时，检查tts_content
                                    if is_finish and "tts_content" in inner_data:
                                        full_text = inner_data["tts_content"]
                                        if full_text and len(full_text) > len(result_text):
                                            result_text = full_text
                                            result_data["text"] = result_text

                            elif content_type == 2030:  # 读取状态含文本内容
                                if "text" in content:
                                    text = content["text"]
                                    result_text += text
                                    result_data["text"] = result_text

                            elif content_type == 2008 or content_type == 2018:  # 处理搜索和特殊响应内容
                                if "text" in content:
                                    text = content["text"]
                                    result_text += text
                                    result_data["text"] = result_text

                                    if is_finish and "tts_content" in inner_data:
                                        full_text = inner_data["tts_content"]
                                        if full_text and len(full_text) > len(result_text):
                                            result_text = full_text
                                            result_data["text"] = result_text

                        except Exception:
                            continue

                except json.JSONDecodeError:
                    continue
                except Exception:
                    continue

            # 如果有文本内容，返回文本结果
            if result_text:
                result_data["text"] = result_text
                return result_data

            # 返回空结果
            result_data["text"] = ""
            return result_data

        except Exception as e:
            logger.error(f"[DoubaoChat] 处理响应流失败: {e}")
            if result_text:
                result_data["text"] = result_text
                return result_data
            return None