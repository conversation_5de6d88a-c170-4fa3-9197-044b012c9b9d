"""
豆包API调用模块
老王从现有项目提取的API逻辑，专门处理豆包对话请求
艹，这些API参数真tm复杂，但是能用就行
"""

import json
import time
import uuid
import httpx
import asyncio
from typing import Optional, Dict, Any
from urllib.parse import quote
import logging

# 设置日志
logger = logging.getLogger(__name__)

class DoubaoAPI:
    """豆包API调用类 - 老王专门搞的，别乱改"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化豆包API
        Args:
            config: 配置字典，包含API相关配置
        """
        self.config = config
        self.api_config = config.get('doubao_api', {})
        self.rate_config = config.get('rate_limit', {})
        
        # 豆包API配置
        self.api_base_url = self.api_config.get('base_url', 'https://www.doubao.com/samantha/chat/completion')
        self.cookies = self.api_config.get('cookies', '')
        self.device_id = self.api_config.get('device_id', '7468716989062841895')
        self.tea_uuid = self.api_config.get('tea_uuid', '7468716986638386703')
        self.web_id = self.api_config.get('web_id', '7468716986638386703')
        
        # 请求限制配置
        self.min_request_interval = self.rate_config.get('min_request_interval', 2.0)
        self.timeout_connect = self.rate_config.get('timeout_connect', 15.0)
        self.timeout_read = self.rate_config.get('timeout_read', 300.0)
        self.timeout_write = self.rate_config.get('timeout_write', 60.0)
        self.max_connections = self.rate_config.get('max_connections', 50)
        self.max_keepalive_connections = self.rate_config.get('max_keepalive_connections', 20)
        self.keepalive_expiry = self.rate_config.get('keepalive_expiry', 30.0)
        
        # httpx客户端管理
        self._client = None
        self._client_lock = asyncio.Lock()
        
        # 用户请求限制
        self._user_limits = {}
        
        logger.info("豆包API初始化完成，准备开始怼这些SB接口")

    async def get_httpx_client(self):
        """创建httpx客户端 - 修复连接池超时问题"""
        return httpx.AsyncClient(
            timeout=httpx.Timeout(
                connect=self.timeout_connect,
                read=self.timeout_read,
                write=self.timeout_write,
                pool=self.keepalive_expiry  # 使用配置的连接池超时时间
            ),
            verify=False,  # 禁用SSL验证，豆包的证书有时候抽风
            follow_redirects=True,
            # 修复连接池配置，使用配置文件中的参数
            limits=httpx.Limits(
                max_connections=self.max_connections,
                max_keepalive_connections=self.max_keepalive_connections,
                keepalive_expiry=self.keepalive_expiry
            ),
        )

    async def get_session(self):
        """获取或创建httpx异步客户端 - 修复连接池问题，每次创建新客户端"""
        # 不再复用客户端，每次创建新的，避免连接池超时问题
        return await self.get_httpx_client()

    async def close_session(self):
        """关闭httpx客户端 - 记得关闭，不然内存泄漏"""
        async with self._client_lock:
            if self._client and not self._client.is_closed:
                await self._client.aclose()
                self._client = None

    def _generate_headers(self):
        """生成请求头 - 这些头部参数都是必须的，少一个都不行"""
        # 生成随机的x-flow-trace，豆包用这个追踪请求
        flow_trace = f"04-{uuid.uuid4().hex[:16]}-{uuid.uuid4().hex[:16]}-01"

        return {
            "Accept": "*/*",
            "Accept-Encoding": "gzip, deflate",
            "Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
            "Connection": "keep-alive",
            "Content-Type": "application/json; charset=utf-8",
            "Cookie": self.cookies,
            "Host": "www.doubao.com",
            "Origin": "https://www.doubao.com",
            "Referer": "https://www.doubao.com/chat/",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "x-flow-trace": flow_trace,
            "Agw-Js-Conv": "str",
            "X-Requested-With": "mark.via",
            "last-event-id": "undefined",
            "Sec-Fetch-Site": "same-origin",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Dest": "empty"
        }

    def _check_user_limit(self, user_id: str) -> float:
        """检查用户请求限制 - 防止憨批用户疯狂刷接口"""
        current_time = time.time()
        
        last_request = self._user_limits.get(user_id, 0)
        wait_time = max(0, self.min_request_interval - (current_time - last_request))
        
        if wait_time == 0:
            self._user_limits[user_id] = current_time
            
        return wait_time

    async def chat(self, message: str, user_id: str = "default") -> Optional[Dict[str, Any]]:
        """
        发送对话请求到豆包API
        Args:
            message: 用户消息
            user_id: 用户ID，用于请求限制
        Returns:
            Dict包含响应文本，或None如果失败
        """
        try:
            # 检查请求限制
            wait_time = self._check_user_limit(user_id)
            if wait_time > 0:
                logger.warning(f"用户 {user_id} 请求过于频繁，需要等待 {wait_time:.1f} 秒")
                return {"type": "error", "text": f"请求过于频繁，请等待 {wait_time:.1f} 秒后再试"}

            # 构造请求URL参数 - 这些参数都是豆包要求的，缺一个都不行
            params = {
                "aid": "497858",
                "device_id": self.device_id,
                "device_platform": "web",
                "language": "zh",
                "pc_version": "2.16.4",
                "pkg_type": "release_version",
                "real_aid": "497858",
                "region": "CN",
                "samantha_web": "1",
                "sys_region": "CN",
                "tea_uuid": self.tea_uuid,
                "use-olympus-account": "1",
                "version_code": "20800",
                "web_id": self.web_id
            }

            # 创建随机的会话ID和消息ID - 豆包用这些来追踪对话
            conversation_id = f"38{int(time.time() * 10000)}8"
            section_id = f"{conversation_id[:10]}4{conversation_id[10:]}"
            message_id = f"ba0d6520-{uuid.uuid4().hex[:4]}-11f0-a188-0d4d{uuid.uuid4().hex[:8]}"

            # 构造请求数据 - 这个格式是豆包API要求的
            request_data = {
                "messages": [{
                    "content": json.dumps({"text": message}),
                    "content_type": 2001,  # 文本消息类型
                    "attachments": [],
                    "references": []
                }],
                "completion_option": {
                    "is_regen": False,
                    "with_suggest": True,
                    "need_create_conversation": True,
                    "launch_stage": 1,
                    "is_replace": False,
                    "is_delete": False,
                    "message_from": 0,
                    "use_deep_think": False,
                    "use_auto_cot": False,
                    "event_id": "0"
                },
                "section_id": section_id,
                "conversation_id": conversation_id,
                "local_message_id": message_id
            }

            # 获取httpx客户端
            client = await self.get_session()

            try:
                # 设置请求头
                headers = self._generate_headers()

                # 构造完整URL
                url = f"{self.api_base_url}?" + "&".join([f"{k}={quote(str(v))}" for k, v in params.items()])

                logger.info(f"发送豆包API请求: {message[:50]}...")

                # 发送POST请求
                response = await client.post(
                    url,
                    json=request_data,
                    headers=headers,
                    timeout=httpx.Timeout(
                        connect=self.timeout_connect,
                        read=self.timeout_read,
                        write=self.timeout_write,
                        pool=30.0  # 增加连接池超时时间
                    )
                )

                if response.status_code != 200:
                    error_text = response.content.decode('utf-8', errors='ignore') if response.content else "无响应内容"
                    logger.error(f"豆包API请求失败: 状态码={response.status_code}")
                    logger.error(f"响应内容: {error_text[:500]}...")
                    return {"type": "error", "text": f"API请求失败，状态码: {response.status_code}"}

                # 处理响应
                result = await self._process_stream_response(response.content)

                if result:
                    return result

                return {"type": "text", "text": "抱歉，没有收到有效响应"}

            finally:
                # 确保每次请求后都关闭客户端，避免连接池问题
                if client and not client.is_closed:
                    await client.aclose()

        except Exception as e:
            logger.error(f"豆包API调用失败: {e}", exc_info=True)
            return {"type": "error", "text": f"API调用失败: {str(e)}"}

    async def _process_stream_response(self, response_body) -> Optional[Dict[str, Any]]:
        """
        处理SSE响应流 - 豆包返回的是Server-Sent Events格式
        支持文本和图片混合内容，老王专门修复的
        """
        result_text = ""
        images_data = []
        result_data = {"type": "text", "text": "", "images": []}

        try:
            # 解码响应数据
            if isinstance(response_body, str):
                chunk = response_body.encode('utf-8')
            else:
                chunk = response_body

            try:
                decoded_chunk = chunk.decode('utf-8', errors='ignore')
                buffer = decoded_chunk
            except UnicodeDecodeError:
                logger.error("响应解码失败，这个憨批API返回的什么鬼数据")
                return None

            # 处理完整的SSE事件
            while "\n\n" in buffer:
                parts = buffer.split("\n\n", 1)
                event = parts[0]
                buffer = parts[1]

                if not event.strip():
                    continue

                # 提取"data:"行
                data_line = None
                for line in event.split("\n"):
                    if line.startswith("data:"):
                        data_line = line[5:].strip()
                        break

                if not data_line:
                    continue

                # 解析数据
                try:
                    event_data = json.loads(data_line)
                    if not isinstance(event_data, dict):
                        continue

                    if "event_type" not in event_data:
                        continue

                    event_type = event_data["event_type"]

                    # 处理结束事件
                    if event_type == 2003:
                        if "tts_content" in event_data:
                            full_text = event_data["tts_content"]
                            if full_text and len(full_text) > len(result_text):
                                result_text = full_text
                                result_data["text"] = result_text

                        result_data["text"] = result_text
                        result_data["images"] = images_data
                        # 如果有图片，返回混合类型
                        if images_data:
                            result_data["type"] = "mixed"
                        return result_data

                    # 处理正常消息事件
                    if event_type == 2001 and "event_data" in event_data:
                        try:
                            inner_data = json.loads(event_data["event_data"])

                            if "message" not in inner_data:
                                continue

                            message = inner_data["message"]
                            is_finish = inner_data.get("is_finish", False)

                            if "content_type" not in message or "content" not in message:
                                continue

                            content_type = message["content_type"]
                            try:
                                content = json.loads(message["content"])
                            except json.JSONDecodeError:
                                continue

                            # 处理文本内容
                            if content_type == 2001:
                                if "text" in content:
                                    text = content["text"]
                                    result_text += text
                                    result_data["text"] = result_text

                                    # 当事件标记为完成时，检查tts_content
                                    if is_finish and "tts_content" in inner_data:
                                        full_text = inner_data["tts_content"]
                                        if full_text and len(full_text) > len(result_text):
                                            result_text = full_text
                                            result_data["text"] = result_text

                            # 处理豆包生图的文本内容
                            elif content_type == 10000:
                                if "text" in content:
                                    text = content["text"]
                                    result_text += text
                                    result_data["text"] = result_text

                            # 处理图片内容 - 这是关键的图片处理逻辑
                            elif content_type == 2010 or content_type == 2074:
                                # 处理图片数据
                                if "data" in content:
                                    # 清空之前的图片数据（确保不重复）
                                    images_data = []

                                    for img_item in content["data"]:
                                        # 添加完整的图片数据到列表
                                        images_data.append(img_item)

                                    # 如果有图片数据，更新结果数据
                                    if images_data:
                                        result_data = {
                                            "type": "mixed",
                                            "text": result_text.strip(),
                                            "images": images_data
                                        }

                                        # 如果是完成状态，立即返回结果
                                        if is_finish:
                                            return result_data

                                # 处理图片生成响应 - 特殊处理content_type=2074的情况
                                elif "creations" in content:
                                    # 提取所有图片信息
                                    for creation in content["creations"]:
                                        if creation.get("type") == 1 and "image" in creation:
                                            img_info = creation["image"]

                                            # 检查图片是否已生成完成
                                            if img_info.get("status") == 2:  # 状态2表示生成完成
                                                # 添加完整的图片数据到列表
                                                images_data.append(img_info)

                                    # 如果有图片数据，更新结果数据
                                    if images_data:
                                        result_data = {
                                            "type": "mixed",
                                            "text": result_text.strip(),
                                            "images": images_data
                                        }

                                        # 如果是完成状态或reset标志为true，立即返回结果
                                        if is_finish or inner_data.get("reset", False):
                                            return result_data

                            elif content_type == 2030:  # 读取状态含文本内容
                                if "text" in content:
                                    text = content["text"]
                                    result_text += text
                                    result_data["text"] = result_text

                            elif content_type == 2008 or content_type == 2018:  # 处理搜索和特殊响应内容
                                if "text" in content:
                                    text = content["text"]
                                    result_text += text
                                    result_data["text"] = result_text

                                    if is_finish and "tts_content" in inner_data:
                                        full_text = inner_data["tts_content"]
                                        if full_text and len(full_text) > len(result_text):
                                            result_text = full_text
                                            result_data["text"] = result_text

                        except Exception:
                            continue

                except json.JSONDecodeError:
                    continue
                except Exception:
                    continue

            # 返回最终结果
            result_data = {
                "type": "mixed" if images_data else "text",
                "text": result_text.strip(),
                "images": images_data
            }
            return result_data

        except Exception as e:
            logger.error(f"处理响应流失败: {e}", exc_info=True)
            if result_text:
                result_data["text"] = result_text
                return result_data
            return None
