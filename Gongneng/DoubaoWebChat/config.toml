# DoubaoWebChat 配置文件
# 老王专门为你搞的豆包Web对话框配置，别tm乱改！

# 基本配置
[app]
host = "127.0.0.1"
port = 5000
debug = true
secret_key = "doubao_web_chat_secret_key_change_this_in_production"

# 豆包API配置 - 这个cookies必须配置，不然就是个憨批
[doubao_api]
base_url = "https://www.doubao.com/samantha/chat/completion"
device_id = "7468716989062841895"
tea_uuid = "7468716986638386703"
web_id = "7468716986638386703"
# 在这里填入你的豆包cookies，格式就是浏览器F12复制的那一长串
cookies = """odin_tt=ce01dd8a864a322a72c558bbe0e04f8f2665b85de103f8fa4838e8b5bcead46b66d7a2fb0f0b80e20d2102d62c9cafe49252524f05b07c987a31b619b94f9a14; uid_tt=b5afc593606da8952401c4f3d643389b; uid_tt_ss=b5afc593606da8952401c4f3d643389b; sid_tt=cd1f8eee1ccef2e53f65ba70262f26a1; sessionid=cd1f8eee1ccef2e53f65ba70262f26a1; sessionid_ss=cd1f8eee1ccef2e53f65ba70262f26a1; is_staff_user=false; store-region=cn-gd; store-region-src=uid; _ga=GA1.1.821753261.1741918457; sid_guard=cd1f8eee1ccef2e53f65ba70262f26a1%7C1751438306%7C5184000%7CSun%2C+31-Aug-2025+06%3A38%3A26+GMT; sid_ucp_v1=1.0.0-KDkzNzUzZTdiMGI3OWJjNTJhMjdhZjA5NDA0Y2U0NTI3YTI1NDFjZWUKHgjkxID8tsxzEOKnk8MGGMKxHiAMMN_w27kGOAhAJhoCbGYiIGNkMWY4ZWVlMWNjZWYyZTUzZjY1YmE3MDI2MmYyNmEx; ssid_ucp_v1=1.0.0-KDkzNzUzZTdiMGI3OWJjNTJhMjdhZjA5NDA0Y2U0NTI3YTI1NDFjZWUKHgjkxID8tsxzEOKnk8MGGMKxHiAMMN_w27kGOAhAJhoCbGYiIGNkMWY4ZWVlMWNjZWYyZTUzZjY1YmE3MDI2MmYyNmEx; session_tlb_tag=sttt%7C6%7CzR-O7hzO8uU_ZbpwJi8mof_________5RKLYByk5T5XEFCU3eaG8MyOfx9Q99soSQgW3xJz9zqQ%3D; gd_random=eyJtYXRjaCI6dHJ1ZSwicGVyY2VudCI6MC43MTQzOTY4NDkzMjkwNzA5fQ==.Q4kqbFfyHXnjAlewFoXNm8iZWKkkCgkhz75/TOQah78=; ttwid=1%7C_lwmd_UVjc190uZJ3ovtx-isP8NEP5R6oszUtdP0Y9g%7C1753086188%7C8bc7946a4506e6350b65a78c9fe2fa0c5663174a919bb80bc5be7bda118e9d8f; i18next=zh; flow_ssr_sidebar_expand=1; passport_fe_beating_status=true; flow_user_country=CN; tt_scid=A7hrnFT5QADorW6.Tl4j5Q0pOIzs9YcXLPW5oum9fv7.SSzwwT8sDqbzavSczEfqda5b; _ga_G8EP5CG8VZ=GS2.1.s1753086190$o27$g1$t1753086234$j16$l0$h0; msToken=Eoh6DOh9SzJG7kHoH3RRMsxqBt43bT-LEWiVkfNIGgBv8tRAMC4nrEdZzCcFamJeqWk0Zfl9L1ctiCg9FNj0LPlHGQR1du6h43wNxXW4Vt-XbEQ3y99GihD4vWv1lV7TrfD6"""

# 请求限制配置 - 防止你这个憨批疯狂刷API
[rate_limit]
requests_per_minute = 30  # 每分钟最多30次请求
min_request_interval = 2.0  # 最小请求间隔（秒）
timeout_connect = 15.0  # 连接超时（增加到15秒）
timeout_read = 300.0  # 读取超时
timeout_write = 60.0  # 写入超时
max_connections = 50  # 最大连接数
max_keepalive_connections = 20  # 最大保持连接数
keepalive_expiry = 30.0  # 连接保持时间（秒）

# 会话管理配置
[session]
max_history_messages = 50  # 最大历史消息数量
session_timeout = 1800  # 会话超时时间（秒），30分钟
auto_clear_history = true  # 是否自动清理历史记录

# 界面配置 - 让你的界面不那么丑
[ui]
theme = "dark"  # 主题：dark(深色) 或 light(浅色)
title = "豆包AI对话框 - 老王出品"
welcome_message = "欢迎使用豆包AI对话框！直接输入消息开始对话吧，别客气！"
placeholder_text = "在这里输入你的问题..."
max_input_length = 2000  # 最大输入长度

# 系统提示词配置 - 可以自定义豆包的行为
[system_prompt]
enabled = false  # 是否启用自定义系统提示词
content = "你是一个有用的AI助手，请用简洁明了的方式回答用户问题。"

# 高级配置 - 别乱动，除非你知道自己在干什么
[advanced]
enable_streaming = true  # 是否启用流式响应
enable_markdown = true  # 是否启用Markdown渲染
enable_code_highlight = true  # 是否启用代码高亮
auto_scroll = true  # 是否自动滚动到底部
show_typing_indicator = true  # 是否显示正在输入指示器

# 日志配置
[logging]
level = "INFO"  # 日志级别：DEBUG, INFO, WARNING, ERROR
log_file = "doubao_web_chat.log"  # 日志文件名
max_log_size = "10MB"  # 最大日志文件大小
backup_count = 5  # 日志文件备份数量
