# DoubaoWebChat 配置文件
# 老王专门为你搞的豆包Web对话框配置，别tm乱改！

# 基本配置
[app]
host = "127.0.0.1"
port = 5000
debug = true
secret_key = "doubao_web_chat_secret_key_change_this_in_production"

# 豆包API配置 - 这个cookies必须配置，不然就是个憨批
[doubao_api]
base_url = "https://www.doubao.com/samantha/chat/completion"
device_id = "7468716989062841895"
tea_uuid = "7468716986638386703"
web_id = "7468716986638386703"
# 在这里填入你的豆包cookies，格式就是浏览器F12复制的那一长串
cookies = """_ga=GA1.1.1839401100.1749310247; s_v_web_id=verify_mbp8fmzy_PTRIZ4YQ_PWG1_444m_AS6t_m6Plv6zTt8dK; passport_csrf_token=8d294fc68fc903affd74f537708c8e36; passport_csrf_token_default=8d294fc68fc903affd74f537708c8e36; n_mh=VhSvSe1Kqdv21URHsx7mhxnio1YOjcYFGu1rQI2CPxc; odin_tt=11a79b46f3a4d85562057d01e36d81b22e2effe495249ce3c812a7e4407a37fc7ddfe9099e6970f5734462b0de2e8dba72e76dcb77fd73c540d0fff97c9103aa; passport_auth_status=7d350b51cce817194520befe1870d61f%2C6356586a336e3bb9fb9a5d5684feefef; passport_auth_status_ss=7d350b51cce817194520befe1870d61f%2C6356586a336e3bb9fb9a5d5684feefef; sid_guard=8774095f547fb1510321d0ae0a2c86b6%7C1753193206%7C5184000%7CSat%2C+20-Sep-2025+14%3A06%3A46+GMT; uid_tt=3504b424c5e8fb4ba3f4dce5aec0a789; uid_tt_ss=3504b424c5e8fb4ba3f4dce5aec0a789; sid_tt=8774095f547fb1510321d0ae0a2c86b6; sessionid=8774095f547fb1510321d0ae0a2c86b6; sessionid_ss=8774095f547fb1510321d0ae0a2c86b6; session_tlb_tag=sttt%7C16%7Ch3QJX1R_sVEDIdCuCiyGtv________-qFutyZa8wedswpavhosTgJ1X2mSb9MSXfHaOeDR9Mxhs%3D; is_staff_user=false; sid_ucp_v1=1.0.0-KGRkZjJhN2U1N2FlYjZiNTRkZTNmZGE4NzQ3ODRlNmM3MzFiODdlNGMKHgjkxID8tsxzEPa1_sMGGMKxHiAMMN_w27kGOAhAJhoCaGwiIDg3NzQwOTVmNTQ3ZmIxNTEwMzIxZDBhZTBhMmM4NmI2; ssid_ucp_v1=1.0.0-KGRkZjJhN2U1N2FlYjZiNTRkZTNmZGE4NzQ3ODRlNmM3MzFiODdlNGMKHgjkxID8tsxzEPa1_sMGGMKxHiAMMN_w27kGOAhAJhoCaGwiIDg3NzQwOTVmNTQ3ZmIxNTEwMzIxZDBhZTBhMmM4NmI2; gd_random=eyJtYXRjaCI6dHJ1ZSwicGVyY2VudCI6MC45MzU2MDczMzE2Mzg4ODQ4fQ==.Hu7QG2k67tNtOT9HGizzZ259gDc3LHJMsEi2j611pq0=; hook_slardar_session_id=202507312203527F8418F473A28BD3B261,ttwid=1%7C6k1NudCoC-hge-44vXR-59WE45B0G1hS0fbeUewVaUA%7C1753970633%7C4c6932ae2fac67f93329eeb86e934dbcdf23ae603ab22d9801f9169c03ed7a88; ttwid=1%7C6k1NudCoC-hge-44vXR-59WE45B0G1hS0fbeUewVaUA%7C1753970732%7C079e842dd6f054dc6ce73ec6302b27a7344dd49d7d69cc79003b8e5b572622d9; i18next=zh; flow_ssr_sidebar_expand=1; passport_fe_beating_status=true; _ga_G8EP5CG8VZ=GS2.1.s1753970637$o17$g1$t1753970735$j51$l0$h0; flow_user_country=CN; msToken=bmB00D_sMWPNHT2d8aor4u-b_aGyXPK5kM5BbPcZsad7grjUne7LV_PKoqCmN9TB-_zOCO8CKea4-C4Yw45PPnXSVskmSr-_xB3ZpC8sgCMQfpPgHqU7MOhYih4ZHbVvUL0=; tt_scid=.Hk6e-bTe4dOgagPPE1YUKU1B.wBi1VHODre1XD4xqKk9lSW4MFUa9LJhQ1dUsCZa07e"""

# 请求限制配置 - 防止你这个憨批疯狂刷API
[rate_limit]
requests_per_minute = 30  # 每分钟最多30次请求
min_request_interval = 2.0  # 最小请求间隔（秒）
timeout_connect = 15.0  # 连接超时（增加到15秒）
timeout_read = 300.0  # 读取超时
timeout_write = 60.0  # 写入超时
max_connections = 50  # 最大连接数
max_keepalive_connections = 20  # 最大保持连接数
keepalive_expiry = 30.0  # 连接保持时间（秒）

# 会话管理配置
[session]
max_history_messages = 50  # 最大历史消息数量
session_timeout = 1800  # 会话超时时间（秒），30分钟
auto_clear_history = true  # 是否自动清理历史记录

# 界面配置 - 让你的界面不那么丑
[ui]
theme = "dark"  # 主题：dark(深色) 或 light(浅色)
title = "豆包AI对话框 - 老王出品"
welcome_message = "欢迎使用豆包AI对话框！直接输入消息开始对话吧，别客气！"
placeholder_text = "在这里输入你的问题..."
max_input_length = 2000  # 最大输入长度

# 系统提示词配置 - 可以自定义豆包的行为
[system_prompt]
enabled = false  # 是否启用自定义系统提示词
content = "你是一个有用的AI助手，请用简洁明了的方式回答用户问题。"

# 高级配置 - 别乱动，除非你知道自己在干什么
[advanced]
enable_streaming = true  # 是否启用流式响应
enable_markdown = true  # 是否启用Markdown渲染
enable_code_highlight = true  # 是否启用代码高亮
auto_scroll = true  # 是否自动滚动到底部
show_typing_indicator = true  # 是否显示正在输入指示器

# 日志配置
[logging]
level = "INFO"  # 日志级别：DEBUG, INFO, WARNING, ERROR
log_file = "doubao_web_chat.log"  # 日志文件名
max_log_size = "10MB"  # 最大日志文件大小
backup_count = 5  # 日志文件备份数量
