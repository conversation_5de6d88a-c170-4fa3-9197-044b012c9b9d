<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/styles/github-dark.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/marked/5.1.1/marked.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/highlight.min.js"></script>
    <style>
        /* 内联CSS - 老王懒得搞外部文件了 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: {% if theme == 'dark' %}#1a1a1a{% else %}#f5f5f5{% endif %};
            color: {% if theme == 'dark' %}#e0e0e0{% else %}#333{% endif %};
            height: 100vh;
            overflow: hidden;
        }

        .container {
            display: flex;
            flex-direction: column;
            height: 100vh;
            max-width: 1200px;
            margin: 0 auto;
            background: {% if theme == 'dark' %}#2d2d2d{% else %}#ffffff{% endif %};
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }

        .header {
            background: {% if theme == 'dark' %}#3d3d3d{% else %}#4a90e2{% endif %};
            color: white;
            padding: 1rem 2rem;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .header h1 {
            font-size: 1.5rem;
            font-weight: 600;
        }

        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .messages {
            flex: 1;
            overflow-y: auto;
            padding: 1rem;
            scroll-behavior: smooth;
        }

        .message {
            margin-bottom: 1rem;
            display: flex;
            align-items: flex-start;
            gap: 0.5rem;
        }

        .message.user {
            flex-direction: row-reverse;
        }

        .message-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
            flex-shrink: 0;
        }

        .message.user .message-avatar {
            background: #4a90e2;
        }

        .message.assistant .message-avatar {
            background: #ff6b6b;
        }

        .message-content {
            max-width: 70%;
            padding: 0.75rem 1rem;
            border-radius: 1rem;
            word-wrap: break-word;
            line-height: 1.4;
        }

        .message-images {
            margin-top: 0.5rem;
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
        }

        .message-image {
            max-width: 200px;
            max-height: 200px;
            border-radius: 8px;
            cursor: pointer;
            transition: transform 0.2s ease;
        }

        .message-image:hover {
            transform: scale(1.05);
        }

        .image-modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.8);
            cursor: pointer;
        }

        .image-modal img {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            max-width: 90%;
            max-height: 90%;
            border-radius: 8px;
        }

        .message.user .message-content {
            background: #4a90e2;
            color: white;
            border-bottom-right-radius: 0.25rem;
        }

        .message.assistant .message-content {
            background: {% if theme == 'dark' %}#3d3d3d{% else %}#f0f0f0{% endif %};
            color: {% if theme == 'dark' %}#e0e0e0{% else %}#333{% endif %};
            border-bottom-left-radius: 0.25rem;
        }

        .input-container {
            padding: 1rem 2rem;
            background: {% if theme == 'dark' %}#3d3d3d{% else %}#f8f9fa{% endif %};
            border-top: 1px solid {% if theme == 'dark' %}#4d4d4d{% else %}#e0e0e0{% endif %};
        }

        .input-form {
            display: flex;
            gap: 0.5rem;
            align-items: flex-end;
        }

        .input-wrapper {
            flex: 1;
            position: relative;
        }

        #messageInput {
            width: 100%;
            min-height: 50px;
            max-height: 150px;
            padding: 0.75rem 1rem;
            border: 2px solid {% if theme == 'dark' %}#4d4d4d{% else %}#ddd{% endif %};
            border-radius: 25px;
            background: {% if theme == 'dark' %}#2d2d2d{% else %}#ffffff{% endif %};
            color: {% if theme == 'dark' %}#e0e0e0{% else %}#333{% endif %};
            font-size: 1rem;
            resize: none;
            outline: none;
            transition: border-color 0.3s ease;
        }

        #messageInput:focus {
            border-color: #4a90e2;
        }

        #sendButton {
            padding: 0.75rem 1.5rem;
            background: #4a90e2;
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            transition: background-color 0.3s ease;
            white-space: nowrap;
        }

        #sendButton:hover:not(:disabled) {
            background: #357abd;
        }

        #sendButton:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .typing-indicator {
            display: none;
            padding: 0.5rem 1rem;
            color: #888;
            font-style: italic;
        }

        .typing-dots {
            display: inline-block;
        }

        .typing-dots::after {
            content: '';
            animation: typing 1.5s infinite;
        }

        @keyframes typing {
            0%, 20% { content: '.'; }
            40% { content: '..'; }
            60%, 100% { content: '...'; }
        }

        .controls {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }

        .control-button {
            padding: 0.5rem 1rem;
            background: {% if theme == 'dark' %}#4d4d4d{% else %}#e0e0e0{% endif %};
            color: {% if theme == 'dark' %}#e0e0e0{% else %}#333{% endif %};
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: background-color 0.3s ease;
        }

        .control-button:hover {
            background: {% if theme == 'dark' %}#5d5d5d{% else %}#d0d0d0{% endif %};
        }

        .welcome-message {
            text-align: center;
            padding: 2rem;
            color: #888;
            font-size: 1.1rem;
        }

        /* 代码高亮样式 */
        pre {
            background: {% if theme == 'dark' %}#1e1e1e{% else %}#f8f8f8{% endif %};
            border-radius: 8px;
            padding: 1rem;
            overflow-x: auto;
            margin: 0.5rem 0;
        }

        code {
            background: {% if theme == 'dark' %}#3d3d3d{% else %}#f0f0f0{% endif %};
            padding: 0.2rem 0.4rem;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
        }

        pre code {
            background: none;
            padding: 0;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                height: 100vh;
            }
            
            .header {
                padding: 0.75rem 1rem;
            }
            
            .header h1 {
                font-size: 1.25rem;
            }
            
            .input-container {
                padding: 1rem;
            }
            
            .message-content {
                max-width: 85%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{{ title }}</h1>
        </div>
        
        <div class="chat-container">
            <div class="controls">
                <button class="control-button" onclick="clearHistory()">清空历史</button>
                <button class="control-button" onclick="loadHistory()">加载历史</button>
                <button class="control-button" onclick="toggleTheme()">切换主题</button>
            </div>
            
            <div class="messages" id="messages">
                <div class="welcome-message">
                    {{ welcome_message }}
                </div>
            </div>
            
            <div class="typing-indicator" id="typingIndicator">
                豆包正在思考<span class="typing-dots"></span>
            </div>
        </div>
        
        <div class="input-container">
            <form class="input-form" onsubmit="sendMessage(event)">
                <div class="input-wrapper">
                    <textarea 
                        id="messageInput" 
                        placeholder="{{ placeholder_text }}"
                        rows="1"
                        maxlength="2000"
                    ></textarea>
                </div>
                <button type="submit" id="sendButton">发送</button>
            </form>
        </div>
    </div>

    <script>
        // 全局变量
        let userId = '{{ user_id }}';
        let isLoading = false;
        let currentTheme = '{{ theme }}';
        
        // DOM元素
        const messagesContainer = document.getElementById('messages');
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');
        const typingIndicator = document.getElementById('typingIndicator');
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 自动调整输入框高度
            messageInput.addEventListener('input', autoResizeTextarea);
            
            // 回车发送消息
            messageInput.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage(e);
                }
            });
            
            // 加载历史记录
            loadHistory();
        });
        
        // 发送消息
        async function sendMessage(event) {
            event.preventDefault();
            
            const message = messageInput.value.trim();
            if (!message || isLoading) return;
            
            // 添加用户消息到界面
            addMessage('user', message);
            
            // 清空输入框
            messageInput.value = '';
            autoResizeTextarea();
            
            // 显示加载状态
            setLoading(true);
            
            try {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: message,
                        user_id: userId
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    // 添加AI回复到界面，支持图片
                    addMessage('assistant', data.message, data.images);
                } else {
                    // 显示错误消息
                    addMessage('assistant', `错误: ${data.error || '未知错误'}`);
                }
            } catch (error) {
                console.error('发送消息失败:', error);
                addMessage('assistant', '抱歉，发送消息失败，请检查网络连接');
            } finally {
                setLoading(false);
            }
        }
        
        // 添加消息到界面 - 支持图片显示
        function addMessage(role, content, images = null) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${role}`;

            const avatar = document.createElement('div');
            avatar.className = 'message-avatar';
            avatar.textContent = role === 'user' ? '我' : '豆';

            const messageContent = document.createElement('div');
            messageContent.className = 'message-content';

            // 如果是Markdown内容，进行渲染
            if (typeof marked !== 'undefined' && content) {
                messageContent.innerHTML = marked.parse(content);
                // 代码高亮
                if (typeof hljs !== 'undefined') {
                    messageContent.querySelectorAll('pre code').forEach((block) => {
                        hljs.highlightElement(block);
                    });
                }
            } else if (content) {
                messageContent.textContent = content;
            }

            // 如果有图片，添加图片显示
            if (images && images.length > 0) {
                const imagesContainer = document.createElement('div');
                imagesContainer.className = 'message-images';

                images.forEach(imageData => {
                    const imageUrl = getImageUrl(imageData);
                    if (imageUrl) {
                        const img = document.createElement('img');
                        img.className = 'message-image';
                        img.src = imageUrl;
                        img.alt = '生成的图片';
                        img.onclick = () => showImageModal(imageUrl);
                        imagesContainer.appendChild(img);
                    }
                });

                messageContent.appendChild(imagesContainer);
            }

            messageDiv.appendChild(avatar);
            messageDiv.appendChild(messageContent);

            // 移除欢迎消息
            const welcomeMessage = messagesContainer.querySelector('.welcome-message');
            if (welcomeMessage) {
                welcomeMessage.remove();
            }

            messagesContainer.appendChild(messageDiv);
            scrollToBottom();
        }

        // 从图片数据中获取URL
        function getImageUrl(imageData) {
            // 优先选择高清原图
            if (imageData.image_raw && imageData.image_raw.url) {
                return imageData.image_raw.url;
            }
            if (imageData.image_ori && imageData.image_ori.url) {
                return imageData.image_ori.url;
            }
            if (imageData.image_thumb_ori && imageData.image_thumb_ori.url) {
                return imageData.image_thumb_ori.url;
            }
            if (imageData.image_thumb && imageData.image_thumb.url) {
                return imageData.image_thumb.url;
            }
            return null;
        }

        // 显示图片模态框
        function showImageModal(imageUrl) {
            const modal = document.createElement('div');
            modal.className = 'image-modal';
            modal.style.display = 'block';

            const img = document.createElement('img');
            img.src = imageUrl;
            img.alt = '图片预览';

            modal.appendChild(img);
            document.body.appendChild(modal);

            // 点击模态框关闭
            modal.onclick = () => {
                document.body.removeChild(modal);
            };
        }
        
        // 设置加载状态
        function setLoading(loading) {
            isLoading = loading;
            sendButton.disabled = loading;
            sendButton.textContent = loading ? '发送中...' : '发送';
            
            if (loading) {
                typingIndicator.style.display = 'block';
            } else {
                typingIndicator.style.display = 'none';
            }
        }
        
        // 自动调整输入框高度
        function autoResizeTextarea() {
            messageInput.style.height = 'auto';
            messageInput.style.height = Math.min(messageInput.scrollHeight, 150) + 'px';
        }
        
        // 滚动到底部
        function scrollToBottom() {
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }
        
        // 清空历史记录
        async function clearHistory() {
            if (!confirm('确定要清空所有聊天记录吗？')) return;
            
            try {
                const response = await fetch('/api/clear', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        user_id: userId
                    })
                });
                
                if (response.ok) {
                    messagesContainer.innerHTML = '<div class="welcome-message">{{ welcome_message }}</div>';
                } else {
                    alert('清空历史记录失败');
                }
            } catch (error) {
                console.error('清空历史记录失败:', error);
                alert('清空历史记录失败');
            }
        }
        
        // 加载历史记录
        async function loadHistory() {
            try {
                const response = await fetch(`/api/history?user_id=${userId}`);
                const data = await response.json();
                
                if (response.ok && data.history && data.history.length > 0) {
                    // 清空当前消息
                    messagesContainer.innerHTML = '';
                    
                    // 添加历史消息
                    data.history.forEach(msg => {
                        addMessage(msg.role, msg.content, msg.images);
                    });
                }
            } catch (error) {
                console.error('加载历史记录失败:', error);
            }
        }
        
        // 切换主题（简单实现）
        function toggleTheme() {
            alert('主题切换功能需要刷新页面，请在配置文件中修改theme设置');
        }
    </script>
</body>
</html>
