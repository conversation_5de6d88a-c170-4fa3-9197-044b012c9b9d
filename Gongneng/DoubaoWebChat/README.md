# 豆包Web对话框 - 老王出品

一个基于Flask的豆包AI对话框Web应用，老王参照现有的豆包插件项目专门搞的。

## 功能特色

- 🎨 **现代化界面** - 支持深色/浅色主题，响应式设计
- 💬 **实时对话** - 基于豆包API的AI对话功能
- 🖼️ **图片支持** - 支持豆包AI图片生成和显示
- 📝 **Markdown支持** - 支持Markdown渲染和代码高亮
- 💾 **会话管理** - 自动保存聊天历史，支持清空和加载
- ⚡ **高性能** - 异步处理，支持并发用户
- 🔧 **高度可配置** - 丰富的配置选项，满足不同需求

## 自定义配置项

### 基本配置
- **服务器配置** - 主机地址、端口、调试模式
- **Cookie配置** - 豆包API认证Cookie（必须配置）
- **API参数** - 设备ID、UUID等豆包API参数

### 界面配置
- **主题设置** - 深色/浅色主题切换
- **界面文本** - 标题、欢迎消息、占位符文本
- **输入限制** - 最大输入长度限制

### 功能配置
- **请求限制** - 每分钟请求次数、最小请求间隔
- **会话管理** - 最大历史消息数、会话超时时间
- **高级功能** - Markdown渲染、代码高亮、自动滚动

### 系统配置
- **日志设置** - 日志级别、文件大小、备份数量
- **超时设置** - 连接超时、读取超时、写入超时

## 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置Cookie

编辑 `config.toml` 文件，在 `[doubao_api]` 部分填入你的豆包Cookie：

```toml
[doubao_api]
cookies = "你的豆包Cookie字符串"
```

**获取Cookie方法：**
1. 打开浏览器，访问 https://www.doubao.com/chat/
2. 登录你的豆包账号
3. 按F12打开开发者工具
4. 切换到Network标签
5. 发送一条消息
6. 找到请求，复制Cookie头部的值

### 3. 启动应用

```bash
python app.py
```

### 4. 访问应用

打开浏览器访问：http://127.0.0.1:5000/

### 5. 测试图片功能

运行测试脚本验证图片功能：

```bash
python test_image.py
```

**图片生成示例：**
- "帮我生成一只可爱的小猫"
- "画一个美丽的风景"
- "生成一张科幻风格的图片"

## 配置说明

### config.toml 配置文件详解

```toml
# 应用基本配置
[app]
host = "127.0.0.1"          # 服务器地址
port = 5000                 # 服务器端口
debug = true                # 调试模式
secret_key = "your_secret"  # Flask密钥

# 豆包API配置（重要！）
[doubao_api]
base_url = "https://www.doubao.com/samantha/chat/completion"
cookies = "你的Cookie"      # 必须配置
device_id = "设备ID"
tea_uuid = "UUID"
web_id = "Web ID"

# 请求限制
[rate_limit]
requests_per_minute = 30    # 每分钟最大请求数
min_request_interval = 2.0  # 最小请求间隔（秒）
timeout_connect = 10.0      # 连接超时
timeout_read = 300.0        # 读取超时
timeout_write = 60.0        # 写入超时

# 会话管理
[session]
max_history_messages = 50   # 最大历史消息数
session_timeout = 1800      # 会话超时（秒）
auto_clear_history = true   # 自动清理历史

# 界面配置
[ui]
theme = "dark"              # 主题：dark/light
title = "豆包AI对话框"      # 页面标题
welcome_message = "欢迎！"  # 欢迎消息
placeholder_text = "输入..."# 输入框占位符
max_input_length = 2000     # 最大输入长度

# 高级功能
[advanced]
enable_streaming = true     # 流式响应
enable_markdown = true      # Markdown渲染
enable_code_highlight = true# 代码高亮
auto_scroll = true          # 自动滚动
show_typing_indicator = true# 输入指示器
```

## API接口

### POST /api/chat
发送聊天消息

**请求体：**
```json
{
    "message": "用户消息",
    "user_id": "用户ID"
}
```

**响应：**
```json
{
    "message": "AI回复",
    "timestamp": 1640995200.0
}
```

### GET /api/history
获取聊天历史

**参数：**
- `user_id`: 用户ID

**响应：**
```json
{
    "history": [
        {
            "role": "user",
            "content": "用户消息",
            "timestamp": 1640995200.0
        }
    ]
}
```

### POST /api/clear
清空聊天历史

**请求体：**
```json
{
    "user_id": "用户ID"
}
```

### GET /api/config
获取前端配置

## 项目结构

```
DoubaoWebChat/
├── app.py              # Flask主应用
├── doubao_api.py       # 豆包API调用模块
├── config.toml         # 配置文件
├── requirements.txt    # 依赖包列表
├── README.md          # 项目文档
└── templates/
    └── index.html     # 前端页面
```

## 注意事项

1. **Cookie配置是必须的** - 没有有效的Cookie无法调用豆包API
2. **请求频率限制** - 避免过于频繁的请求导致被限制
3. **会话管理** - 系统会自动清理过期会话以节省内存
4. **安全性** - 生产环境请修改secret_key和其他安全配置

## 故障排除

### 常见问题

1. **连接池超时错误 (PoolTimeout)**
   ```
   httpcore.PoolTimeout
   httpx.PoolTimeout
   ```
   **解决方案:**
   - 运行修复脚本: `python fix_timeout.py`
   - 检查网络连接是否稳定
   - 重新获取Cookie
   - 重启应用

2. **Cookie失效**
   - 重新获取Cookie并更新配置文件
   - 检查豆包账号是否正常
   - 确保Cookie包含完整的认证信息

3. **请求失败**
   - 检查网络连接
   - 确认豆包服务是否正常
   - 检查是否被频率限制

4. **界面异常**
   - 清除浏览器缓存
   - 检查JavaScript控制台错误
   - 确认Flask服务正常运行

### 快速修复

如果遇到连接超时问题，运行以下命令快速修复：

```bash
python fix_timeout.py
```

### 日志查看

应用会生成日志文件 `doubao_web_chat.log`，可以查看详细的运行信息和错误信息。

### 性能优化建议

1. **网络优化**
   - 使用稳定的网络连接
   - 避免使用代理或VPN（可能影响连接）

2. **配置优化**
   - 适当调整请求间隔
   - 根据网络情况调整超时时间

3. **使用优化**
   - 避免同时发送多个请求
   - 定期清理聊天历史

## 开发说明

这个项目是基于现有的豆包插件项目开发的，主要复用了豆包API调用逻辑，并封装成了独立的Web应用。

### 技术栈
- **后端**: Flask + httpx + asyncio
- **前端**: 原生HTML/CSS/JavaScript + Marked.js + Highlight.js
- **配置**: TOML格式配置文件

### 扩展开发
如果需要添加新功能，可以：
1. 修改 `config.toml` 添加新的配置项
2. 在 `app.py` 中添加新的路由和逻辑
3. 在 `templates/index.html` 中添加前端功能

## 许可证

本项目基于现有豆包插件项目开发，仅供学习和个人使用。

---

**老王提醒：记得配置Cookie，不然就是个憨批项目！**
