"""
豆包Web对话框应用
老王专门搞的豆包AI对话框，基于Flask框架
"""

import os
import json
import time
import uuid
import asyncio
import logging
import tomlkit
from typing import Dict, Any, List, Optional
from datetime import datetime
from flask import Flask, render_template, request, jsonify, session
from flask_cors import CORS

# 导入豆包API模块
from doubao_api import DoubaoAPI

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('doubao_web_chat.log')
    ]
)
logger = logging.getLogger(__name__)

# 加载配置文件
def load_config() -> Dict[str, Any]:
    """加载配置文件"""
    try:
        with open('config.toml', 'r', encoding='utf-8') as f:
            config = tomlkit.parse(f.read())
        logger.info("配置文件加载成功")
        return config
    except Exception as e:
        logger.error(f"加载配置文件失败: {e}")
        # 返回默认配置
        return {
            'app': {
                'host': '127.0.0.1',
                'port': 5000,
                'debug': True,
                'secret_key': 'default_secret_key'
            },
            'doubao_api': {
                'base_url': 'https://www.doubao.com/samantha/chat/completion',
                'cookies': ''
            },
            'ui': {
                'theme': 'dark',
                'title': '豆包AI对话框',
                'welcome_message': '欢迎使用豆包AI对话框！'
            }
        }

# 创建应用
config = load_config()
app = Flask(__name__)
CORS(app)  # 启用CORS，允许跨域请求

# 配置Flask应用
app.secret_key = config['app'].get('secret_key', 'default_secret_key')
app.config['SESSION_TYPE'] = 'filesystem'
app.config['SESSION_PERMANENT'] = False
app.config['PERMANENT_SESSION_LIFETIME'] = config['session'].get('session_timeout', 1800)

# 创建豆包API实例
doubao_api = DoubaoAPI(config)

# 会话管理
chat_sessions = {}  # 用户ID -> 会话数据

# 会话数据结构
class ChatSession:
    def __init__(self, user_id: str):
        self.user_id = user_id
        self.messages = []  # 消息历史
        self.created_at = time.time()
        self.last_active = time.time()
        self.max_history = config['session'].get('max_history_messages', 50)
    
    def add_message(self, role: str, content: str, images: List[Dict] = None):
        """添加消息到历史记录，支持图片"""
        message_data = {
            'role': role,
            'content': content,
            'timestamp': time.time()
        }
        # 如果有图片数据，添加到消息中
        if images:
            message_data['images'] = images

        self.messages.append(message_data)
        # 限制历史记录长度
        if len(self.messages) > self.max_history:
            self.messages = self.messages[-self.max_history:]
        self.last_active = time.time()
    
    def get_history(self) -> List[Dict[str, Any]]:
        """获取消息历史"""
        return self.messages
    
    def clear_history(self):
        """清空历史记录"""
        self.messages = []
        self.last_active = time.time()

# 路由定义
@app.route('/')
def index():
    """首页"""
    # 获取UI配置
    ui_config = config.get('ui', {})
    theme = ui_config.get('theme', 'dark')
    title = ui_config.get('title', '豆包AI对话框')
    welcome_message = ui_config.get('welcome_message', '欢迎使用豆包AI对话框！')
    placeholder_text = ui_config.get('placeholder_text', '在这里输入你的问题...')
    
    # 生成或获取用户ID
    if 'user_id' not in session:
        session['user_id'] = f"user_{uuid.uuid4().hex[:8]}"
    
    # 确保用户有会话
    user_id = session['user_id']
    if user_id not in chat_sessions:
        chat_sessions[user_id] = ChatSession(user_id)
    
    # 渲染模板
    return render_template(
        'index.html',
        theme=theme,
        title=title,
        welcome_message=welcome_message,
        placeholder_text=placeholder_text,
        user_id=user_id
    )

@app.route('/api/chat', methods=['POST'])
def chat():
    """聊天API接口"""
    try:
        # 获取请求数据
        data = request.json
        message = data.get('message', '').strip()
        user_id = data.get('user_id', 'default')

        # 验证消息
        if not message:
            return jsonify({'error': '消息不能为空'}), 400

        # 获取或创建会话
        if user_id not in chat_sessions:
            chat_sessions[user_id] = ChatSession(user_id)

        session = chat_sessions[user_id]
        session.add_message('user', message)

        # 调用豆包API - 使用asyncio.run来运行异步函数
        response = asyncio.run(doubao_api.chat(message, user_id))

        if not response:
            return jsonify({'error': '豆包API调用失败'}), 500

        if response.get('type') == 'error':
            return jsonify({'error': response.get('text', '未知错误')}), 500

        # 添加回复到会话，包括图片数据
        session.add_message('assistant', response.get('text', ''), response.get('images'))

        # 返回响应 - 支持文本和图片
        result = {
            'message': response.get('text', ''),
            'timestamp': time.time(),
            'type': response.get('type', 'text')
        }

        # 如果有图片数据，添加到响应中
        if response.get('images'):
            result['images'] = response.get('images', [])

        return jsonify(result)

    except Exception as e:
        logger.error(f"处理聊天请求失败: {e}", exc_info=True)
        return jsonify({'error': f'处理请求失败: {str(e)}'}), 500

@app.route('/api/history', methods=['GET'])
def get_history():
    """获取聊天历史"""
    try:
        user_id = request.args.get('user_id', session.get('user_id', 'default'))
        
        if user_id not in chat_sessions:
            return jsonify({'history': []})
        
        session = chat_sessions[user_id]
        return jsonify({'history': session.get_history()})
    
    except Exception as e:
        logger.error(f"获取历史记录失败: {e}", exc_info=True)
        return jsonify({'error': f'获取历史记录失败: {str(e)}'}), 500

@app.route('/api/clear', methods=['POST'])
def clear_history():
    """清空聊天历史"""
    try:
        user_id = request.json.get('user_id', session.get('user_id', 'default'))
        
        if user_id in chat_sessions:
            chat_sessions[user_id].clear_history()
        
        return jsonify({'success': True})
    
    except Exception as e:
        logger.error(f"清空历史记录失败: {e}", exc_info=True)
        return jsonify({'error': f'清空历史记录失败: {str(e)}'}), 500

@app.route('/api/config', methods=['GET'])
def get_config():
    """获取前端配置"""
    # 只返回UI相关配置，不返回敏感信息
    ui_config = config.get('ui', {})
    advanced_config = config.get('advanced', {})
    
    return jsonify({
        'theme': ui_config.get('theme', 'dark'),
        'title': ui_config.get('title', '豆包AI对话框'),
        'welcome_message': ui_config.get('welcome_message', '欢迎使用豆包AI对话框！'),
        'placeholder_text': ui_config.get('placeholder_text', '在这里输入你的问题...'),
        'max_input_length': ui_config.get('max_input_length', 2000),
        'enable_markdown': advanced_config.get('enable_markdown', True),
        'enable_code_highlight': advanced_config.get('enable_code_highlight', True),
        'auto_scroll': advanced_config.get('auto_scroll', True),
        'show_typing_indicator': advanced_config.get('show_typing_indicator', True)
    })

# 定期清理过期会话
def cleanup_sessions():
    """清理过期会话"""
    current_time = time.time()
    session_timeout = config['session'].get('session_timeout', 1800)
    expired_users = []
    
    for user_id, session in chat_sessions.items():
        if current_time - session.last_active > session_timeout:
            expired_users.append(user_id)
    
    for user_id in expired_users:
        del chat_sessions[user_id]
    
    if expired_users:
        logger.info(f"已清理 {len(expired_users)} 个过期会话")

# 启动应用
if __name__ == '__main__':
    host = config['app'].get('host', '127.0.0.1')
    port = config['app'].get('port', 5000)
    debug = config['app'].get('debug', False)
    
    # 打印启动信息
    logger.info(f"豆包Web对话框启动中，访问地址: http://{host}:{port}/")
    logger.info(f"调试模式: {'开启' if debug else '关闭'}")
    
    # 启动Flask应用
    app.run(host=host, port=port, debug=debug)
