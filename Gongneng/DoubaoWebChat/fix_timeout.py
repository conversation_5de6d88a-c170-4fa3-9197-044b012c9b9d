#!/usr/bin/env python3
"""
豆包Web对话框超时问题修复脚本
老王专门搞的修复脚本，解决连接池超时问题
"""

import os
import sys
import tomlkit
from pathlib import Path

def fix_config():
    """修复配置文件中的超时设置"""
    config_file = Path("config.toml")
    
    if not config_file.exists():
        print("❌ 配置文件 config.toml 不存在")
        return False
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = tomlkit.parse(f.read())
        
        # 修复rate_limit配置
        if 'rate_limit' not in config:
            config['rate_limit'] = {}
        
        rate_limit = config['rate_limit']
        
        # 更新超时配置
        rate_limit['timeout_connect'] = 15.0  # 增加连接超时
        rate_limit['timeout_read'] = 300.0
        rate_limit['timeout_write'] = 60.0
        rate_limit['max_connections'] = 50  # 增加最大连接数
        rate_limit['max_keepalive_connections'] = 20
        rate_limit['keepalive_expiry'] = 30.0
        
        # 保存配置文件
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(tomlkit.dumps(config))
        
        print("✅ 配置文件已修复")
        return True
        
    except Exception as e:
        print(f"❌ 修复配置文件失败: {e}")
        return False

def show_fix_info():
    """显示修复信息"""
    print("=" * 60)
    print("豆包Web对话框超时问题修复 - 老王出品")
    print("=" * 60)
    print()
    print("🔧 修复内容:")
    print("1. 增加连接超时时间到15秒")
    print("2. 增加最大连接数到50")
    print("3. 增加保持连接数到20")
    print("4. 设置连接保持时间为30秒")
    print("5. 每次请求后自动关闭客户端")
    print()
    print("🚨 常见超时问题原因:")
    print("- 网络连接不稳定")
    print("- 豆包服务器响应慢")
    print("- Cookie已过期")
    print("- 请求过于频繁被限制")
    print()
    print("💡 解决建议:")
    print("1. 检查网络连接")
    print("2. 更新豆包Cookie")
    print("3. 适当增加请求间隔")
    print("4. 重启应用")
    print()

def check_cookie_validity():
    """检查Cookie有效性"""
    config_file = Path("config.toml")
    
    if not config_file.exists():
        return False
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = tomlkit.parse(f.read())
        
        cookies = config.get('doubao_api', {}).get('cookies', '')
        
        if not cookies or len(cookies.strip()) < 100:
            print("⚠️  Cookie可能无效或过短")
            print("请确保Cookie包含完整的认证信息")
            return False
        
        # 检查Cookie中是否包含关键字段
        required_fields = ['sessionid', 'sid_tt', 'uid_tt']
        missing_fields = []
        
        for field in required_fields:
            if field not in cookies:
                missing_fields.append(field)
        
        if missing_fields:
            print(f"⚠️  Cookie缺少关键字段: {', '.join(missing_fields)}")
            print("请重新获取完整的Cookie")
            return False
        
        print("✅ Cookie格式检查通过")
        return True
        
    except Exception as e:
        print(f"❌ 检查Cookie失败: {e}")
        return False

def main():
    """主函数"""
    show_fix_info()
    
    print("🔧 开始修复...")
    print()
    
    # 修复配置文件
    if fix_config():
        print("✅ 配置文件修复完成")
    else:
        print("❌ 配置文件修复失败")
        return
    
    # 检查Cookie
    print("\n🍪 检查Cookie有效性...")
    if not check_cookie_validity():
        print("⚠️  建议重新获取Cookie")
    
    print("\n" + "=" * 60)
    print("🎉 修复完成！")
    print("=" * 60)
    print()
    print("📋 后续步骤:")
    print("1. 重启应用: python start.py")
    print("2. 如果仍有问题，请检查网络连接")
    print("3. 如果Cookie过期，请重新获取")
    print()
    print("🆘 如果问题持续存在:")
    print("- 检查豆包官网是否正常访问")
    print("- 尝试更换网络环境")
    print("- 联系技术支持")
    print()

if __name__ == '__main__':
    main()
