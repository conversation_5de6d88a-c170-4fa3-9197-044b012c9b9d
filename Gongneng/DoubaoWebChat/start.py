#!/usr/bin/env python3
"""
豆包Web对话框启动脚本
老王专门搞的启动脚本，检查环境和配置
"""

import os
import sys
import subprocess
import tomlkit
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 7):
        print("❌ Python版本过低，需要Python 3.7或更高版本")
        print(f"当前版本: {sys.version}")
        return False
    print(f"✅ Python版本检查通过: {sys.version}")
    return True

def check_dependencies():
    """检查依赖包"""
    required_packages = [
        'flask',
        'flask_cors', 
        'httpx',
        'tomlkit'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} 已安装")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} 未安装")
    
    if missing_packages:
        print(f"\n缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install -r requirements.txt")
        return False
    
    return True

def check_config():
    """检查配置文件"""
    config_file = Path("config.toml")
    
    if not config_file.exists():
        print("❌ 配置文件 config.toml 不存在")
        return False
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = tomlkit.parse(f.read())
        
        # 检查必要配置
        if 'doubao_api' not in config:
            print("❌ 配置文件缺少 [doubao_api] 部分")
            return False
        
        cookies = config['doubao_api'].get('cookies', '')
        if not cookies or cookies.strip() == '':
            print("❌ 豆包API的cookies未配置")
            print("请在config.toml中配置你的豆包cookies")
            return False
        
        print("✅ 配置文件检查通过")
        return True
        
    except Exception as e:
        print(f"❌ 配置文件解析失败: {e}")
        return False

def install_dependencies():
    """安装依赖包"""
    print("正在安装依赖包...")
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'])
        print("✅ 依赖包安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖包安装失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("豆包Web对话框启动检查 - 老王出品")
    print("=" * 50)
    
    # 检查Python版本
    if not check_python_version():
        sys.exit(1)
    
    # 检查依赖包
    if not check_dependencies():
        print("\n是否自动安装依赖包? (y/n): ", end="")
        choice = input().lower().strip()
        if choice == 'y' or choice == 'yes':
            if not install_dependencies():
                sys.exit(1)
        else:
            print("请手动安装依赖包后重试")
            sys.exit(1)
    
    # 检查配置文件
    if not check_config():
        print("\n配置文件检查失败，请检查config.toml文件")
        print("特别注意：必须配置有效的豆包cookies")
        sys.exit(1)
    
    print("\n" + "=" * 50)
    print("✅ 所有检查通过，正在启动应用...")
    print("=" * 50)
    
    # 启动应用
    try:
        from app import app, config
        host = config['app'].get('host', '127.0.0.1')
        port = config['app'].get('port', 5000)
        debug = config['app'].get('debug', False)
        
        print(f"🚀 应用启动成功！")
        print(f"📱 访问地址: http://{host}:{port}/")
        print(f"🔧 调试模式: {'开启' if debug else '关闭'}")
        print(f"⏹️  按 Ctrl+C 停止服务")
        print("=" * 50)
        
        app.run(host=host, port=port, debug=debug)
        
    except KeyboardInterrupt:
        print("\n👋 应用已停止")
    except Exception as e:
        print(f"❌ 应用启动失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
