#!/usr/bin/env python3
"""
豆包Web对话框图片功能测试脚本
老王专门搞的测试脚本，验证图片显示功能
"""

import asyncio
import json
from doubao_api import DoubaoAPI
import tomlkit

async def test_image_generation():
    """测试图片生成功能"""
    print("=" * 50)
    print("豆包图片生成功能测试 - 老王出品")
    print("=" * 50)
    
    # 加载配置
    try:
        with open('config.toml', 'r', encoding='utf-8') as f:
            config = tomlkit.parse(f.read())
    except Exception as e:
        print(f"❌ 加载配置失败: {e}")
        return
    
    # 创建API实例
    api = DoubaoAPI(config)
    
    # 测试图片生成请求
    test_prompts = [
        "帮我生成一只可爱的小猫",
        "画一个美丽的风景",
        "生成一张科幻风格的图片"
    ]
    
    for i, prompt in enumerate(test_prompts, 1):
        print(f"\n🎨 测试 {i}: {prompt}")
        print("-" * 30)
        
        try:
            result = await api.chat(prompt, f"test_user_{i}")
            
            if result:
                print(f"✅ 响应类型: {result.get('type', 'unknown')}")
                print(f"📝 文本内容: {result.get('text', '')[:100]}...")
                
                images = result.get('images', [])
                if images:
                    print(f"🖼️  图片数量: {len(images)}")
                    for j, img in enumerate(images):
                        print(f"   图片 {j+1}:")
                        if 'image_raw' in img:
                            print(f"     原图URL: {img['image_raw'].get('url', 'N/A')[:50]}...")
                        if 'image_thumb' in img:
                            print(f"     缩略图URL: {img['image_thumb'].get('url', 'N/A')[:50]}...")
                else:
                    print("❌ 没有检测到图片数据")
            else:
                print("❌ 没有收到响应")
                
        except Exception as e:
            print(f"❌ 测试失败: {e}")
        
        # 等待一下，避免请求过快
        await asyncio.sleep(3)
    
    # 关闭API连接
    await api.close_session()
    
    print("\n" + "=" * 50)
    print("🎉 测试完成！")
    print("=" * 50)
    print("\n💡 使用建议:")
    print("1. 如果没有图片数据，检查豆包Cookie是否有效")
    print("2. 确保请求的是图片生成相关的内容")
    print("3. 检查网络连接是否稳定")

def test_image_url_extraction():
    """测试图片URL提取功能"""
    print("\n🔧 测试图片URL提取功能...")
    
    # 模拟图片数据
    test_image_data = {
        "image_raw": {"url": "https://example.com/raw_image.jpg"},
        "image_ori": {"url": "https://example.com/ori_image.jpg"},
        "image_thumb_ori": {"url": "https://example.com/thumb_ori_image.jpg"},
        "image_thumb": {"url": "https://example.com/thumb_image.jpg"}
    }
    
    def get_image_url(image_data):
        """从图片数据中获取URL"""
        if image_data.get('image_raw', {}).get('url'):
            return image_data['image_raw']['url']
        if image_data.get('image_ori', {}).get('url'):
            return image_data['image_ori']['url']
        if image_data.get('image_thumb_ori', {}).get('url'):
            return image_data['image_thumb_ori']['url']
        if image_data.get('image_thumb', {}).get('url'):
            return image_data['image_thumb']['url']
        return None
    
    url = get_image_url(test_image_data)
    print(f"✅ 提取的URL: {url}")
    
    # 测试不完整的数据
    incomplete_data = {"image_thumb": {"url": "https://example.com/thumb_only.jpg"}}
    url2 = get_image_url(incomplete_data)
    print(f"✅ 不完整数据提取的URL: {url2}")

if __name__ == '__main__':
    print("🚀 开始测试豆包图片功能...")
    
    # 测试URL提取
    test_image_url_extraction()
    
    # 测试图片生成
    try:
        asyncio.run(test_image_generation())
    except KeyboardInterrupt:
        print("\n👋 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中出错: {e}")
