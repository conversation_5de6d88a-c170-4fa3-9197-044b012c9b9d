from Plugins._Tools import Tools
from Plugins._Tools.ReferenceMessageHandler import ReferenceMessageHandler
from Plugins._Tools.ReferenceMessageErrorHandler import ReferenceMessageErrorHandler
from Config.logger import logger
from Core.PluginBase import PluginBase
import os
import json
import time
import uuid
import httpx
import asyncio
import xml.etree.ElementTree as ET
import base64
import re
import zlib
import hashlib
import hmac
import datetime as dt
from urllib.parse import urlparse, urlencode
from typing import Optional, Dict, Any, Tuple

# HTTP响应类，用于兼容旧插件代码
class HttpResponse:
    def __init__(self, status_code, body, headers=None):
        self.code = status_code
        self.status_code = status_code
        self.body = body
        self.headers = headers or {}

# 常量定义
class Constants:
    # 豆包API配置
    API_BASE_URL = "https://www.doubao.com/samantha/chat/completion"
    UPLOAD_AUTH_URL = "https://www.doubao.com/alice/resource/prepare_upload"
    IMAGEX_BASE_URL = "https://imagex.bytedanceapi.com/"

    # 设备信息
    DEVICE_ID = "7468716989062841895"
    TEA_UUID = "7468716986638386703"
    WEB_ID = "7468716986638386703"

    # 上传配置
    SERVICE_ID = "a9rns2rl98"
    FILE_EXT = ".jpg"

    # 消息类型
    MSG_TYPE_IMAGE = 3
    MSG_TYPE_REFERENCE = 49

    # 识图关键词
    IMAGE_KEYWORDS = ["识图", "识别图片", "这张图", "图里有什么", "分析图片", "看图"]

    # 默认提示词
    DEFAULT_PROMPT = "请分析这张图片"

class DoubaoImageRecognitionPlugin(PluginBase, ReferenceMessageHandler):
    def __init__(self):
        super().__init__()
        # 基本信息（重要：name必须与目录名一致）
        self.name = "DoubaoImageRecognition"
        self.description = "豆包图片识别插件"
        self.version = "1.0.0"
        self.author = "XYBot"

        # 工具类实例和配置
        self.tools = Tools()
        self.configData = self.tools.returnConfigData(os.path.dirname(__file__))
        self.api_config = self.configData.get('API', {})
        self.cookies = self.api_config.get('cookies', '')

        # httpx客户端管理
        self._client = None
        self._client_lock = asyncio.Lock()

        # 用户级别的请求限制
        self._user_limits = {}
        self.min_request_interval = 3.0  # 识图间隔

        # 图片缓存 - 存储图片消息的下载参数
        self.image_cache = {}  # {msg_id: {"data_len": int, "to_wxid": str, "timestamp": float}}


    # ==================== HTTP客户端和工具函数 ====================

    async def get_session(self):
        """获取或创建httpx异步客户端"""
        async with self._client_lock:
            if self._client is None or self._client.is_closed:
                self._client = httpx.AsyncClient(
                    timeout=httpx.Timeout(connect=10.0, read=20.0, write=20.0, pool=5.0),
                    verify=False, follow_redirects=True,
                    limits=httpx.Limits(max_connections=20, max_keepalive_connections=10),
                )
            return self._client

    async def close_session(self):
        """关闭httpx客户端"""
        async with self._client_lock:
            if self._client and not self._client.is_closed:
                await self._client.aclose()
                self._client = None

    def _generate_flow_trace(self) -> str:
        """生成随机的x-flow-trace"""
        return f"04-{uuid.uuid4().hex[:16]}-{uuid.uuid4().hex[:16]}-01"

    def _get_base_headers(self, content_type: str = "application/json") -> Dict[str, str]:
        """获取基础请求头"""
        return {
            "Accept": "*/*", "Accept-Encoding": "gzip, deflate",
            "Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
            "Connection": "keep-alive", "Content-Type": content_type,
            "Cookie": self.cookies, "Host": "www.doubao.com",
            "Origin": "https://www.doubao.com", "Referer": "https://www.doubao.com/chat/",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "x-flow-trace": self._generate_flow_trace(), "Agw-Js-Conv": "str",
            "X-Requested-With": "mark.via", "last-event-id": "undefined",
            "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty"
        }

    def _parse_cookies(self, cookie_string: str) -> Dict[str, str]:
        """解析Cookie字符串"""
        return {key: value for item in cookie_string.split(';')
                if '=' in item for key, value in [item.strip().split('=', 1)]}

    def _check_user_limit(self, wxid: str, user_wxid: str) -> float:
        """检查用户请求限制并更新时间戳"""
        current_time = time.time()
        if wxid not in self._user_limits:
            self._user_limits[wxid] = {}

        last_request = self._user_limits[wxid].get(user_wxid, 0)
        wait_time = max(0, self.min_request_interval - (current_time - last_request))

        if wait_time == 0:
            self._user_limits[wxid][user_wxid] = current_time
        return wait_time

    # ==================== 消息处理 ====================

    async def handle_message(self, msg) -> bool:
        """处理消息"""
        try:
            # 如果是图片消息，缓存图片信息
            if msg.type == Constants.MSG_TYPE_IMAGE:
                await self._cache_image_info(msg)
                return False  # 图片消息只缓存，不处理

            # 检查是否是图片识别请求
            if not self._is_image_recognition_request(msg):
                return False

            logger.info(f"插件 {self.name} 处理图片识别消息")
            await self._handle_image_recognition_request(msg)
            return True

        except Exception as e:
            logger.error(f"插件 {self.name} 处理消息时出错: {e}", exc_info=True)
            return True

    def _is_image_recognition_request(self, msg) -> bool:
        """判断是否是图片识别请求"""
        # 使用标准方法检查引用消息
        if msg.type != Constants.MSG_TYPE_REFERENCE or not self._is_reference_message(msg.content):
            return False

        # 检查引用的是否是图片
        if self._get_reference_type(msg.content) != "3":
            return False

        # 检查指令中是否包含识图关键词
        prompt = self._extract_prompt_from_reference(msg.content)
        if prompt:
            title_text = prompt.strip().lower()
            return any(keyword.lower() in title_text for keyword in Constants.IMAGE_KEYWORDS)

        return False

    # 使用基类的 _parse_xml_safely 方法替代 _parse_xml_content

    def _extract_and_clean_prompt(self, content: str) -> str:
        """从引用消息中提取并清理提示词"""
        try:
            # 使用标准方法提取提示词
            prompt = self._extract_prompt_from_reference(content)
            if not prompt:
                return Constants.DEFAULT_PROMPT

            # 清理提示词，移除识图关键词
            cleaned_prompt = prompt.strip()
            sorted_keywords = sorted(Constants.IMAGE_KEYWORDS, key=len, reverse=True)

            for keyword in sorted_keywords:
                pattern = r'(?:^|\s|[，。！？,!?])\s*' + re.escape(keyword) + r'\s*(?=\s|[，。！？,!?]|$)'
                cleaned_prompt = re.sub(pattern, ' ', cleaned_prompt, flags=re.IGNORECASE)

            # 清理多余的空格和标点
            cleaned_prompt = re.sub(r'\s+', ' ', cleaned_prompt).strip().strip("，。！？,!?").strip()
            return cleaned_prompt if cleaned_prompt else Constants.DEFAULT_PROMPT

        except Exception as e:
            logger.error(f"提取和清理提示词失败: {e}")
            return Constants.DEFAULT_PROMPT



    # ==================== 图片识别处理 ====================

    async def _handle_image_recognition_request(self, msg):
        """处理图片识别请求"""
        try:
            target_id = msg.roomid if msg.from_group() else msg.sender

            # 检查用户限制
            if self._check_user_limit(target_id, msg.sender) > 0:
                return

            # 获取图片数据和查询文本
            image_data, query_text = await self._prepare_recognition_data(msg)
            if not image_data:
                logger.warning(f"[DoubaoImageRecognition] 无法获取图片数据")
                return

            # 调用豆包API识别图片并发送结果
            result = await self.recognize_image(image_data, query_text)
            if result and result.get("type") == "text" and result.get("text"):
                await self.dp.sendText(result["text"], target_id, msg.self_wxid)

        except Exception as e:
            logger.error(f"处理图片识别请求失败: {e}", exc_info=True)

    async def _prepare_recognition_data(self, msg) -> Tuple[Optional[bytes], str]:
        """准备识别数据：图片数据和查询文本"""
        if msg.type != Constants.MSG_TYPE_REFERENCE:
            return None, Constants.DEFAULT_PROMPT

        image_data = await self._get_image_from_refer_message(msg)
        query_text = self._extract_and_clean_prompt(msg.content)
        return image_data, query_text

    async def _cache_image_info(self, msg):
        """缓存图片消息的下载信息"""
        try:
            msg_id = str(msg.id)
            self.image_cache[msg_id] = {
                "msg_id": msg_id,
                "to_wxid": msg.roomid if msg.from_group() else msg.sender,
                "wxid": msg.self_wxid,
                "timestamp": time.time(),
                "content": msg.content
            }
            logger.info(f"[DoubaoImageRecognition] 缓存图片信息: MsgId={msg_id}")
            self._cleanup_expired_cache()
        except Exception as e:
            logger.error(f"缓存图片信息失败: {e}")

    def _cleanup_expired_cache(self):
        """清理过期缓存（保留最近1小时）"""
        current_time = time.time()
        expired_keys = [key for key, info in self.image_cache.items()
                       if current_time - info["timestamp"] > 3600]
        for key in expired_keys:
            del self.image_cache[key]






    # ==================== 图片下载处理 ====================

    async def _get_image_from_message(self, msg) -> Optional[bytes]:
        """从消息中获取图片数据"""
        try:
            if msg.type != Constants.MSG_TYPE_IMAGE or not msg.content:
                return None

            image_path = msg.content.strip()
            if image_path.startswith('http'):
                return await self._download_from_url(image_path)
            elif os.path.exists(image_path):
                return await self._read_local_file(image_path)
            return None
        except Exception as e:
            logger.error(f"获取图片数据失败: {e}")
            return None

    async def _download_from_url(self, url: str) -> Optional[bytes]:
        """从URL下载图片"""
        try:
            client = await self.get_session()
            response = await client.get(url)
            return response.content if response.status_code == 200 else None
        except Exception as e:
            logger.error(f"从URL下载图片失败: {e}")
            return None

    async def _read_local_file(self, file_path: str) -> Optional[bytes]:
        """读取本地文件"""
        try:
            with open(file_path, 'rb') as f:
                return f.read()
        except Exception as e:
            logger.error(f"读取本地文件失败: {e}")
            return None

    async def _get_image_from_refer_message(self, msg) -> Optional[bytes]:
        """从引用消息中获取图片数据"""
        # 使用基类的标准方法
        return await self._get_reference_image(msg.content, msg.self_wxid)

    # 使用基类的 _download_image_with_cdn_api 方法

    def _decode_base64_image(self, base64_data: str) -> Optional[bytes]:
        """解码base64图片数据"""
        try:
            image_bytes = base64.b64decode(base64_data)
            if len(image_bytes) > 1000:
                logger.info(f"[DoubaoImageRecognition] CDN下载成功，大小: {len(image_bytes)} bytes")
                return image_bytes
        except Exception as e:
            logger.error(f"解码base64图片失败: {e}")
        return None



    # ==================== 图片上传处理 ====================

    async def upload_image_aws(self, image_data: bytes) -> Optional[str]:
        """上传图片到AWS"""
        try:
            client = await self.get_session()

            file_size = len(image_data)
            crc32_value = self._calculate_crc32(image_data)
            logger.info(f"[DoubaoImageRecognition] 开始上传图片: 大小={file_size}, CRC32={crc32_value:08x}")

            # 执行上传流程：认证 -> 申请权限 -> 上传文件 -> 提交完成
            upload_auth = await self._get_upload_auth(client)
            if not upload_auth:
                return None

            upload_info = await self._apply_upload_permission(client, upload_auth, file_size)
            if not upload_info:
                return None

            if not await self._upload_image_file(client, upload_info, image_data, crc32_value):
                return None

            final_uri = await self._commit_upload(client, upload_auth, upload_info)
            if final_uri:
                logger.info(f"图片上传完成: {final_uri}")
            return final_uri

        except Exception as e:
            logger.error(f"上传图片时发生错误: {e}")
            return None

    async def _get_upload_auth(self, client: httpx.AsyncClient) -> Optional[Dict]:
        """获取上传认证信息"""
        try:
            params = {
                'version_code': '20800', 'language': 'zh', 'device_platform': 'web',
                'aid': '497858', 'real_aid': '497858', 'pkg_type': 'release_version',
                'device_id': Constants.DEVICE_ID, 'web_id': Constants.WEB_ID,
                'tea_uuid': Constants.TEA_UUID, 'use-olympus-account': '1',
                'region': 'CN', 'sys_region': 'CN', 'samantha_web': '1', 'pc_version': '2.24.2',
            }
            data = {"tenant_id": "5", "scene_id": "5", "resource_type": 2}

            # 设置cookies
            cookies = self._parse_cookies(self.cookies)

            response = await client.post(
                Constants.UPLOAD_AUTH_URL,
                params=params,
                json=data,
                cookies=cookies,
                timeout=30.0
            )
            if response.status_code != 200:
                logger.error(f"获取认证失败: {response.status_code}")
                return None

            result = response.json()
            if result.get('code') != 0:
                logger.error(f"认证API返回错误: {result}")
                return None

            logger.info("获取认证成功")
            return result['data']['upload_auth_token']

        except Exception as e:
            logger.error(f"获取上传认证失败: {e}")
            return None

    async def _apply_upload_permission(self, client: httpx.AsyncClient, upload_auth: Dict, file_size: int) -> Optional[Dict]:
        """申请上传权限"""
        try:
            upload_params = {
                'Action': 'ApplyImageUpload', 'Version': '2018-08-01',
                'ServiceId': Constants.SERVICE_ID, 'NeedFallback': 'true',
                'FileSize': str(file_size), 'FileExtension': Constants.FILE_EXT,
                's': 'yy49d6n7o6p'
            }

            apply_url = f"{Constants.IMAGEX_BASE_URL}?{urlencode(upload_params)}"
            headers = self._get_upload_base_headers()
            headers.update(self._generate_aws_signature('GET', apply_url, upload_auth))

            response = await client.get(apply_url, headers=headers, timeout=30.0)
            if response.status_code != 200:
                logger.error(f"申请上传失败: {response.status_code}")
                return None

            upload_info = response.json()
            if 'Result' not in upload_info:
                logger.error(f"申请上传响应异常: {upload_info}")
                return None

            logger.info("获取上传权限成功")
            return upload_info['Result']

        except Exception as e:
            logger.error(f"申请上传权限失败: {e}")
            return None

    def _get_upload_base_headers(self) -> Dict[str, str]:
        """获取上传基础请求头"""
        return {
            'User-Agent': 'Mozilla/5.0 (Linux; Android 10; V2002A Build/QP1A.190711.020) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Mobile Safari/537.36',
            'Accept': '*/*', 'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
            'Accept-Encoding': 'gzip, deflate', 'Origin': 'https://www.doubao.com',
            'Referer': 'https://www.doubao.com/chat/create-image', 'X-Requested-With': 'mark.via',
        }

    async def _upload_image_file(self, client: httpx.AsyncClient, upload_info: Dict, image_data: bytes, crc32_value: int) -> bool:
        """上传图片文件"""
        try:
            store_info = upload_info['UploadAddress']['StoreInfos'][0]
            upload_host = upload_info['UploadAddress']['UploadHosts'][0]
            upload_url = f"https://{upload_host}/upload/v1/{store_info['StoreUri']}"

            upload_headers = {
                'Authorization': store_info['Auth'], 'Content-CRC32': f"{crc32_value:08x}",
                'Content-Type': 'application/octet-stream', 'X-Storage-U': store_info['UploadID'],
                'Origin': 'https://www.doubao.com', 'Referer': 'https://www.doubao.com/chat/',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            }

            # 图片上传需要更长的超时时间，根据文件大小动态调整
            upload_timeout = max(60.0, len(image_data) / (1024 * 1024) * 10)  # 每MB给10秒，最少60秒
            response = await client.post(
                upload_url,
                content=image_data,
                headers=upload_headers,
                timeout=httpx.Timeout(connect=10.0, read=upload_timeout, write=upload_timeout, pool=10.0)
            )
            if response.status_code != 200:
                logger.error(f"上传文件失败: {response.status_code}")
                return False

            upload_result = response.json()
            if upload_result.get('code') != 2000:
                logger.error(f"上传文件响应异常: {upload_result}")
                return False

            logger.info("文件上传成功")
            return True

        except Exception as e:
            logger.error(f"上传文件失败: {e}")
            return False

    async def _commit_upload(self, client: httpx.AsyncClient, upload_auth: Dict, upload_info: Dict) -> Optional[str]:
        """提交上传完成"""
        try:
            commit_params = {'Action': 'CommitImageUpload', 'Version': '2018-08-01', 'ServiceId': Constants.SERVICE_ID}
            commit_url = f"{Constants.IMAGEX_BASE_URL}?{urlencode(commit_params)}"

            session_key = upload_info['UploadAddress']['SessionKey']
            commit_payload = json.dumps({"SessionKey": session_key})

            commit_headers = {
                'Content-Type': 'application/json', 'Accept': '*/*',
                'Origin': 'https://www.doubao.com', 'Referer': 'https://www.doubao.com/chat/',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            }
            commit_headers.update(self._generate_aws_signature('POST', commit_url, upload_auth, commit_payload))

            response = await client.post(commit_url, content=commit_payload, headers=commit_headers)
            if response.status_code != 200:
                logger.error(f"提交上传失败: {response.status_code}")
                return None

            commit_result = response.json()
            if 'Result' not in commit_result:
                logger.error(f"提交上传响应异常: {commit_result}")
                return None

            return commit_result['Result']['Results'][0]['Uri']

        except Exception as e:
            logger.error(f"提交上传失败: {e}")
            return None

    def _calculate_crc32(self, data: bytes) -> int:
        """计算文件CRC32校验值"""
        return zlib.crc32(data) & 0xffffffff

    def _generate_aws_signature(self, method: str, url: str, upload_auth: Dict, payload: str = "") -> Dict[str, str]:
        """生成AWS4-HMAC-SHA256签名"""
        access_key, secret_key, session_token = upload_auth['access_key'], upload_auth['secret_key'], upload_auth['session_token']

        # 解析URL和时间戳
        parsed = urlparse(url)
        host, path, query = parsed.netloc, parsed.path or '/', parsed.query

        t = dt.datetime.now(dt.timezone.utc)
        amz_date, date_stamp = t.strftime('%Y%m%dT%H%M%SZ'), t.strftime('%Y%m%d')

        # 构建规范化请求
        canonical_querystring = self._normalize_query_string(query) if query else ''
        canonical_headers = f'host:{host}\nx-amz-date:{amz_date}\nx-amz-security-token:{session_token}\n'
        signed_headers = 'host;x-amz-date;x-amz-security-token'
        payload_hash = hashlib.sha256(payload.encode('utf-8')).hexdigest()

        canonical_request = f'{method}\n{path}\n{canonical_querystring}\n{canonical_headers}\n{signed_headers}\n{payload_hash}'

        # 创建签名字符串
        algorithm = 'AWS4-HMAC-SHA256'
        credential_scope = f'{date_stamp}/cn-north-1/imagex/aws4_request'
        string_to_sign = f'{algorithm}\n{amz_date}\n{credential_scope}\n{hashlib.sha256(canonical_request.encode()).hexdigest()}'

        # 计算签名
        signing_key = self._get_signature_key(secret_key, date_stamp, 'cn-north-1', 'imagex')
        signature = hmac.new(signing_key, string_to_sign.encode('utf-8'), hashlib.sha256).hexdigest()

        # 构建认证头部
        authorization_header = f'{algorithm} Credential={access_key}/{credential_scope}, SignedHeaders={signed_headers}, Signature={signature}'

        return {'Authorization': authorization_header, 'X-Amz-Date': amz_date, 'x-amz-security-token': session_token}

    def _normalize_query_string(self, query: str) -> str:
        """规范化查询字符串"""
        query_params = [(param.split('=', 1) if '=' in param else (param, '')) for param in query.split('&')]
        query_params.sort()
        return '&'.join([f'{k}={v}' for k, v in query_params])

    def _get_signature_key(self, key: str, date_stamp: str, region_name: str, service_name: str) -> bytes:
        """获取签名密钥"""
        def sign(key, msg):
            return hmac.new(key.encode('utf-8') if isinstance(key, str) else key,
                          msg.encode('utf-8'), hashlib.sha256).digest()

        k_date = sign('AWS4' + key, date_stamp)
        k_region = sign(k_date, region_name)
        k_service = sign(k_region, service_name)
        return sign(k_service, 'aws4_request')



    # ==================== 图片识别处理 ====================

    async def recognize_image(self, image_data: bytes, query_text: str = Constants.DEFAULT_PROMPT) -> Optional[Dict[str, Any]]:
        """识别图片内容"""
        try:
            # 上传图片并构造请求
            image_uri = await self.upload_image_aws(image_data)
            if not image_uri:
                logger.error("[DoubaoImageRecognition] 图片上传失败")
                return None

            request_data = self._build_recognition_request(image_uri, query_text)
            response = await self._send_recognition_request(request_data)

            return await self._process_stream(response) if response else None

        except Exception as e:
            logger.error(f"[DoubaoImageRecognition] 识图失败: {e}")
            return None

    def _build_recognition_request(self, image_uri: str, query_text: str) -> Dict[str, Any]:
        """构造识图请求数据"""
        image_info = {
            "type": "image", "key": image_uri,
            "extra": {"refer_types": "overall"}, "identifier": str(uuid.uuid4())
        }

        return {
            "messages": [{
                "content": json.dumps({"text": query_text}),
                "content_type": 2001, "attachments": [image_info], "references": []
            }],
            "completion_option": {
                "is_regen": False, "with_suggest": True, "need_create_conversation": True,
                "launch_stage": 1, "is_replace": False, "is_delete": False,
                "message_from": 0, "event_id": "0"
            },
            "conversation_id": "0", "local_message_id": str(uuid.uuid4())
        }

    async def _send_recognition_request(self, request_data: Dict[str, Any]) -> Optional[HttpResponse]:
        """发送识图请求"""
        try:
            params = {
                "aid": "497858", "device_id": Constants.DEVICE_ID, "device_platform": "web",
                "language": "zh", "pc_version": "1.51.90", "pkg_type": "release_version",
                "real_aid": "497858", "region": "CN", "version_code": "20800", "web_id": Constants.WEB_ID
            }

            headers = self._get_base_headers("application/json")
            headers["Accept"] = "text/event-stream"

            client = await self.get_session()
            response = await client.post(
                f"{Constants.API_BASE_URL}?{urlencode(params)}", json=request_data, headers=headers,
                timeout=httpx.Timeout(connect=10.0, read=60.0, write=30.0, pool=10.0)
            )

            if response.status_code != 200:
                logger.error(f"[DoubaoImageRecognition] 识图请求失败，状态码: {response.status_code}")
                return None

            return HttpResponse(response.status_code, response.content, dict(response.headers))

        except Exception as e:
            logger.error(f"发送识图请求失败: {e}")
            return None













    # ==================== SSE流处理 ====================

    async def _process_stream(self, response) -> Optional[Dict[str, Any]]:
        """处理SSE响应流"""
        try:
            buffer = self._decode_response_body(response.body)
            if not buffer:
                return None

            result_text = ""
            result_data = {"type": "text", "text": ""}

            # 处理完整的SSE事件
            while "\n\n" in buffer:
                event, buffer = buffer.split("\n\n", 1)
                if event.strip():
                    text_update = self._process_sse_event(event)
                    if text_update:
                        result_text += text_update
                        result_data["text"] = result_text

            return result_data if result_text else {"type": "text", "text": ""}

        except Exception as e:
            logger.error(f"[DoubaoImageRecognition] 处理响应流失败: {e}")
            return None

    def _decode_response_body(self, body) -> Optional[str]:
        """解码响应体"""
        try:
            chunk = body.encode('utf-8') if isinstance(body, str) else body
            return chunk.decode('utf-8', errors='ignore')
        except UnicodeDecodeError:
            return None

    def _process_sse_event(self, event: str) -> Optional[str]:
        """处理单个SSE事件"""
        try:
            data_line = self._extract_data_line(event)
            if not data_line:
                return None

            event_data = json.loads(data_line)
            if not isinstance(event_data, dict) or "event_type" not in event_data:
                return None

            event_type = event_data["event_type"]

            # 处理结束事件
            if event_type == 2003:
                return event_data.get("tts_content", "")
            # 处理正常消息事件
            elif event_type == 2001 and "event_data" in event_data:
                return self._process_message_event(event_data["event_data"])

        except (json.JSONDecodeError, Exception):
            pass
        return None

    def _extract_data_line(self, event: str) -> Optional[str]:
        """从事件中提取data行"""
        for line in event.split("\n"):
            if line.startswith("data:"):
                return line[5:].strip()
        return None

    def _process_message_event(self, event_data_str: str) -> Optional[str]:
        """处理消息事件"""
        try:
            inner_data = json.loads(event_data_str)
            message = inner_data.get("message")

            if not message or "content_type" not in message or "content" not in message:
                return None

            content_type = message["content_type"]
            content = json.loads(message["content"])

            # 处理不同类型的内容
            if content_type in [2001, 2030, 2008, 2018] and "text" in content:
                text = content["text"]
                is_finish = inner_data.get("is_finish", False)

                # 如果事件完成且有tts_content，优先使用tts_content
                if is_finish and "tts_content" in inner_data:
                    full_text = inner_data["tts_content"]
                    if full_text and len(full_text) > len(text):
                        return full_text
                return text

        except (json.JSONDecodeError, Exception):
            pass
        return None


