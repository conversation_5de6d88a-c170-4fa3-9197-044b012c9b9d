# 豆包图片识别插件

## 功能说明

这个插件支持使用豆包AI来识别和分析图片内容。

## 支持的使用方式

### 1. 引用图片识图（微信内置功能）
- 长按图片选择"引用"
- 在弹出的识图界面中发送

### 2. 引用图片+识图关键词+自定义提示词（主要功能）
- 长按图片选择"引用"
- 在输入框中输入：`识图关键词 + 自定义提示词`
- 识图关键词：`识图`、`识别图片`、`这张图`、`图里有什么`、`分析图片`、`看图`
- 示例：
  - "识图 这张图片里有什么动物？"
  - "看图 帮我翻译图片中的文字"
  - "分析图片 这张图的构图和色彩如何？"
  - "这张图 是什么品牌的产品？"
  - "图里有什么 人在做什么？"

## 使用示例

### 基础识图
1. 长按图片，选择"引用"
2. 在输入框中输入："识图"
3. 发送消息
4. 机器人会分析图片并返回结果

### 自定义提示词识图
1. 长按图片，选择"引用"
2. 在输入框中输入："识图 这张图片里的文字是什么意思？"
3. 发送消息
4. 机器人会根据你的提示词来分析图片

## 配置说明

插件需要配置豆包的cookies才能正常工作。请在 `config.toml` 文件中配置：

```toml
# 插件启用状态
enabled = true

# 豆包API配置
[API]
cookies = "你的豆包cookies"
```

## 注意事项

1. 需要有效的豆包账号cookies
2. 图片识别有频率限制（默认3秒间隔）
3. 支持的图片格式：JPG、PNG等常见格式
4. 图片大小建议不超过10MB

## 更新日志

### v1.0.0
- 支持基础图片识别
- 支持微信引用图片识图
- 新增：支持引用图片+自定义提示词功能
