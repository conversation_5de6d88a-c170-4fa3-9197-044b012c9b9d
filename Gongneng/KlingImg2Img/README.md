# KlingImg2Img 插件

## 功能说明

可灵AI图生图插件，支持通过引用图片+可灵关键词+提示词的方式生成新图片。

## 主要功能

1. **引用图片识别**：自动识别微信中引用的图片消息
2. **关键词触发**：只有包含"可灵"关键词的消息才会触发
3. **图生图处理**：基于引用图片和用户提示词生成新图片
4. **人脸识别**：自动检测图片中的人脸信息
5. **异步处理**：完整的异步处理流程，不阻塞其他功能

## 使用方法

### 基本用法

1. **引用图片**：长按要处理的图片，选择"引用"
2. **输入指令**：在输入框中输入包含"可灵"关键词的提示词
3. **等待处理**：插件会自动处理并返回生成的图片

### 示例

```
可灵 换个夏日服装搭配
可灵 改成古装风格
可灵 变成卡通风格
kl 换个发型
```

### 触发条件

插件需要同时满足以下三个条件才会触发：

1. ✅ **引用图片**：消息必须引用一张图片
2. ✅ **可灵关键词**：消息内容必须包含可灵关键词
3. ✅ **提示词内容**：除了关键词外还要有具体的处理指令

### 支持的关键词

- `可灵` - 基础触发词
- `可灵ai` - 完整触发词  
- `kl` - 简化触发词
- `KL` - 大写简化触发词

## 配置说明

### config.toml

```toml
# 插件启用状态
enabled = true

# 触发关键词列表
keywords = ["可灵", "可灵ai", "kl", "KL"]

# 冷却时间（秒）
cooldown_time = 60

# API配置
[api]
base_url = "https://api-app-cn.klingai.com"
upload_base_url = "https://upload.kuaishouzt.com"

# Cookie配置（已从旧插件迁移）
[cookie]
cookie = "your_cookie_here"

# 生成参数配置
[generation]
default_prompt = "换个夏日服装搭配"
aspect_ratio = "9:16"
image_count = "1"
kolors_version = "1.5"
style = "默认"

# 轮询配置
[polling]
max_attempts = 60
interval = 10
timeout = 30
```

### Cookie状态

✅ **Cookie已从旧插件自动迁移**，无需手动配置

⚠️ 如果Cookie过期，请重新获取并更新配置文件

## 处理流程

1. **消息检测**：检测引用图片+可灵关键词的消息
2. **图片下载**：使用DPbot的CDN接口下载引用图片
3. **图片上传**：将图片上传到可灵AI平台
4. **人脸识别**：检测图片中的人脸信息
5. **任务提交**：提交图生图任务
6. **状态轮询**：轮询任务状态直到完成
7. **结果返回**：下载并发送生成的图片

## 注意事项

1. **Cookie有效性**：需要有效的可灵AI账号Cookie
2. **冷却时间**：每个用户有60秒的冷却时间
3. **处理时间**：图生图需要较长时间，请耐心等待
4. **图片质量**：建议使用清晰的人像图片获得更好效果
5. **网络连接**：需要稳定的网络连接访问可灵AI服务

## 错误排查

### 常见问题

1. **Cookie失效**
   - 症状：提示"插件配置错误：缺少Cookie配置"
   - 解决：重新获取Cookie并更新配置文件

2. **图片下载失败**
   - 症状：提示"无法获取引用的图片"
   - 解决：检查网络连接，重新引用图片

3. **任务提交失败**
   - 症状：提示"图生图处理失败"
   - 解决：检查Cookie有效性和网络连接

4. **处理超时**
   - 症状：长时间无响应
   - 解决：等待或重新尝试，可能是服务器繁忙

### 调试信息

插件会在日志中记录详细的处理信息：

- `[KlingImg2Img]` 开头的日志为插件专用日志
- 可通过日志查看具体的处理步骤和错误信息

## 更新日志

- **v1.0.0**: 从旧插件完整迁移，支持引用图片+关键词+提示词触发模式

## 技术特点

- ✅ **完全异步**：所有API调用都是异步的
- ✅ **错误处理**：完善的错误处理和用户提示
- ✅ **日志记录**：详细的日志记录便于调试
- ✅ **配置管理**：灵活的TOML配置文件
- ✅ **用户限制**：防止频繁请求的冷却机制
- ✅ **引用处理**：标准的引用消息处理流程

## 依赖要求

- DPbot插件系统
- 有效的可灵AI账号和Cookie
- 稳定的网络连接

---

**祝您使用愉快！** 🎨
