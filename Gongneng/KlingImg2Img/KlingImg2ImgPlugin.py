from Plugins._Tools import Tools
from Config.logger import logger
from Core.PluginBase import PluginBase
import os
import time
import json
import hashlib
import base64
import asyncio
from typing import Optional, Dict, Any
import xml.etree.ElementTree as ET


class KlingImg2ImgPlugin(PluginBase):
    def __init__(self):
        super().__init__()
        # 基本信息（重要：name必须与目录名一致）
        self.name = "KlingImg2Img"
        self.description = "可灵AI图生图插件 - 支持引用图片+可灵关键词+提示词"
        self.version = "1.0.0"
        self.author = "DPbot"

        # 工具类实例
        self.tools = Tools()

        # 加载配置文件
        self.configData = self.tools.returnConfigData(os.path.dirname(__file__))
        self.keywords = self.configData.get('keywords', ['可灵'])
        
        # API配置
        api_config = self.configData.get('api', {})
        self.api_base_url = api_config.get('base_url', 'https://api-app-cn.klingai.com')
        self.upload_base_url = api_config.get('upload_base_url', 'https://upload.kuaishouzt.com')
        
        # Cookie配置
        cookie_config = self.configData.get('cookie', {})
        self.cookie = cookie_config.get('cookie', '')
        
        # 生成参数配置
        generation_config = self.configData.get('generation', {})
        self.default_prompt = generation_config.get('default_prompt', '换个夏日服装搭配')
        
        # 用户限制
        self.user_limits = {}
        self.cooldown_time = self.configData.get('cooldown_time', 60)  # 60秒冷却时间
        
        # 图片缓存（用于处理引用图片）
        self.image_cache = {}

    async def handle_message(self, msg) -> bool:
        """
        处理消息 - 检测引用图片+可灵关键词+提示词的组合
        """
        target_id = msg.roomid if msg.from_group() else msg.sender

        try:
            # 只处理引用消息（type=49）
            if msg.type != 49:
                return False

            # 检查是否是引用图片的消息
            if not self._is_reference_message(msg.content):
                logger.debug(f"[KlingImg2Img] 不是引用消息，跳过处理")
                return False

            # 提取用户指令
            prompt = self._extract_prompt_from_reference(msg.content)
            if not prompt:
                logger.debug(f"[KlingImg2Img] 无法提取用户指令，跳过处理")
                return False

            # 检查是否包含可灵关键词
            if not self._contains_kling_keyword(prompt):
                logger.debug(f"[KlingImg2Img] 不包含可灵关键词，跳过处理")
                return False

            # 检查引用的是否是图片
            if self._get_reference_type(msg.content) != "3":
                logger.debug(f"[KlingImg2Img] 引用的不是图片消息，跳过处理")
                return False

            logger.info(f"[KlingImg2Img] 检测到有效的图生图请求: {prompt}")

            # 检查Cookie配置
            if not self.cookie:
                await self.dp.sendText("❌ 插件配置错误：缺少Cookie配置", target_id, msg.self_wxid)
                logger.error("[KlingImg2Img] Cookie未配置")
                return True

            # 检查冷却时间
            user_key = f"{target_id}_{msg.sender}"
            current_time = time.time()
            last_request = self.user_limits.get(user_key, 0)

            if current_time - last_request < self.cooldown_time:
                wait_time = self.cooldown_time - (current_time - last_request)
                await self.dp.sendText(f"⏰ 请等待 {wait_time:.1f} 秒后再试", target_id, msg.self_wxid)
                return True

            self.user_limits[user_key] = current_time

            # 处理图生图请求
            await self._handle_img2img_request(msg, prompt)
            return True

        except Exception as e:
            logger.error(f"插件 {self.name} 处理消息时出错: {e}", exc_info=True)
            await self._send_error_message(target_id, msg.self_wxid, "处理消息时发生错误")
            return True

    async def _send_error_message(self, target_id: str, self_wxid: str, error_msg: str):
        """发送错误消息"""
        try:
            await self.dp.sendText(f"❌ {error_msg}，请稍后再试", target_id, self_wxid)
        except Exception as e:
            logger.error(f"发送错误消息失败: {e}")

    def _is_reference_message(self, content: str) -> bool:
        """检查是否是引用消息"""
        return content.startswith('<?xml') and 'refermsg' in content

    def _extract_prompt_from_reference(self, content: str) -> Optional[str]:
        """从引用消息中提取用户指令"""
        try:
            root = ET.fromstring(content)
            appmsg = root.find('appmsg')
            if appmsg is not None:
                title = appmsg.find('title')
                if title is not None and title.text:
                    return title.text.strip()
            return None
        except Exception as e:
            logger.debug(f"提取指令失败: {e}")
            return None

    def _get_reference_type(self, content: str) -> Optional[str]:
        """获取引用消息的类型"""
        try:
            root = ET.fromstring(content)
            refermsg = root.find('.//refermsg')
            if refermsg is not None:
                refer_type = refermsg.find('type')
                if refer_type is not None:
                    return refer_type.text  # "1"=文本, "3"=图片, "43"=视频等
            return None
        except Exception:
            return None

    def _contains_kling_keyword(self, prompt: str) -> bool:
        """检查提示词中是否包含可灵关键词"""
        if not prompt:
            return False

        prompt_lower = prompt.lower().strip()

        # 检查是否包含任何可灵关键词
        for keyword in self.keywords:
            if keyword.lower() in prompt_lower:
                logger.debug(f"[KlingImg2Img] 检测到可灵关键词: '{keyword}' in '{prompt}'")
                return True

        logger.debug(f"[KlingImg2Img] 未检测到可灵关键词: '{prompt}'")
        return False

    async def _handle_img2img_request(self, msg, prompt: str):
        """处理图生图请求"""
        target_id = msg.roomid if msg.from_group() else msg.sender

        try:
            # 发送开始处理的消息
            await self.dp.sendText("🎨 正在处理图生图请求，请稍候...", target_id, msg.self_wxid)

            # 获取引用的图片数据
            logger.info("[KlingImg2Img] 开始获取引用图片数据")
            image_data = await self._get_reference_image(msg.content, msg.self_wxid)
            if not image_data:
                await self._send_error_message(target_id, msg.self_wxid, "无法获取引用的图片")
                return

            # 从提示词中移除可灵关键词，获取实际的生图提示词
            actual_prompt = self._extract_actual_prompt(prompt)
            if not actual_prompt:
                actual_prompt = self.default_prompt

            logger.info(f"[KlingImg2Img] 开始图生图处理，提示词: {actual_prompt}")

            # 执行图生图流程
            result_image = await self._process_img2img(image_data, actual_prompt)

            if result_image:
                # 发送生成的图片
                logger.info("[KlingImg2Img] 图生图成功，发送结果")
                await self.dp.sendImage(result_image, target_id, msg.self_wxid)
                await self.dp.sendText(f"✅ 图生图完成！\n📝 提示词: {actual_prompt}", target_id, msg.self_wxid)
            else:
                await self._send_error_message(target_id, msg.self_wxid, "图生图处理失败")

        except Exception as e:
            logger.error(f"处理图生图请求失败: {e}", exc_info=True)
            await self._send_error_message(target_id, msg.self_wxid, "图生图处理异常")

    def _extract_actual_prompt(self, prompt: str) -> str:
        """从用户输入中提取实际的生图提示词（移除可灵关键词）"""
        if not prompt:
            return self.default_prompt

        original_prompt = prompt.strip()

        # 按关键词长度排序，优先匹配长的关键词（避免"可灵ai"被"可灵"截断）
        sorted_keywords = sorted(self.keywords, key=len, reverse=True)

        # 移除可灵关键词，保留实际提示词
        for keyword in sorted_keywords:
            keyword_lower = keyword.lower()
            prompt_lower = original_prompt.lower()

            if keyword_lower in prompt_lower:
                # 找到关键词位置并移除
                start_idx = prompt_lower.find(keyword_lower)
                if start_idx != -1:
                    # 移除关键词及其前后的空格
                    before = original_prompt[:start_idx].strip()
                    after = original_prompt[start_idx + len(keyword):].strip()

                    # 合并前后部分，处理多余的空格
                    if before and after:
                        result = f"{before} {after}".strip()
                    elif before:
                        result = before
                    elif after:
                        result = after
                    else:
                        result = ""

                    # 如果移除关键词后还有内容，使用它；否则使用默认提示词
                    if result:
                        logger.info(f"[KlingImg2Img] 提取实际提示词: '{result}' (原始: '{original_prompt}')")
                        return result
                    else:
                        logger.info(f"[KlingImg2Img] 只有关键词，使用默认提示词: '{self.default_prompt}'")
                        return self.default_prompt

        # 如果没有找到关键词，返回原始提示词
        logger.info(f"[KlingImg2Img] 未找到关键词，使用原始提示词: '{original_prompt}'")
        return original_prompt

    async def _get_reference_image(self, content: str, wxid: str) -> Optional[bytes]:
        """从引用消息中获取图片数据"""
        try:
            xml_root = self._parse_xml_content(content)
            image_params = self._extract_image_params(xml_root) if xml_root else None

            if not image_params:
                logger.warning("无法从引用消息中提取图片参数")
                return None

            cdn_url, aes_key = image_params
            logger.info(f"[KlingImg2Img] 开始下载引用图片: CDN={cdn_url[:50]}..., AESKey={aes_key[:20]}...")
            return await self._download_image_with_cdn_api(cdn_url, aes_key, wxid)

        except Exception as e:
            logger.error(f"从引用消息获取图片数据失败: {e}")
            return None

    def _parse_xml_content(self, content: str) -> Optional[ET.Element]:
        """解析XML内容"""
        try:
            if 'wxid_' in content and '<?xml' in content:
                xml_start = content.find('<?xml')
                if xml_start != -1:
                    xml_part = content[xml_start:]
                    return ET.fromstring(xml_part)
            return ET.fromstring(content)
        except Exception as e:
            logger.error(f"XML解析失败: {e}")
            return None

    def _extract_image_params(self, xml_root: ET.Element) -> Optional[tuple]:
        """从XML中提取图片下载参数"""
        try:
            appmsg = xml_root.find('appmsg')
            refermsg = appmsg.find('refermsg') if appmsg is not None else None
            content = refermsg.find('content') if refermsg is not None else None

            if content is None or not content.text:
                return None

            # 解析引用消息的内容（也是XML）
            refer_root = ET.fromstring(content.text)
            img = refer_root.find('img')
            if img is None:
                return None

            # 获取图片下载参数，优先级：大图 > 中图 > 缩略图
            aeskey = img.get('aeskey')
            cdnthumbaeskey = img.get('cdnthumbaeskey')
            cdnbigimgurl = img.get('cdnbigimgurl')
            cdnmidimgurl = img.get('cdnmidimgurl')
            cdnthumburl = img.get('cdnthumburl')

            # 优先使用大图，然后中图，最后缩略图
            cdn_url = cdnbigimgurl or cdnmidimgurl or cdnthumburl
            final_aeskey = aeskey or cdnthumbaeskey

            if cdn_url and final_aeskey:
                return (cdn_url, final_aeskey)

            return None

        except Exception as e:
            logger.error(f"提取图片参数失败: {e}")
            return None

    async def _download_image_with_cdn_api(self, cdn_url: str, aes_key: str, wxid: str) -> Optional[bytes]:
        """使用DPbot的CDN下载接口下载图片"""
        try:
            from WeChatApi.Base import sendPostReq

            data = {"FileAesKey": aes_key, "FileNo": cdn_url, "Wxid": wxid}
            logger.info(f"[KlingImg2Img] 调用CDN下载接口: FileNo={cdn_url[:50]}..., AESKey={aes_key[:20]}...")

            result = await sendPostReq("Tools/CdnDownloadImage", data)
            if not (result and result.get("Success")):
                logger.error(f"CDN下载接口失败: {result}")
                return None

            return self._process_cdn_response(result.get("Data"))

        except Exception as e:
            logger.error(f"CDN下载接口调用失败: {e}")
            return None

    def _process_cdn_response(self, data) -> Optional[bytes]:
        """处理CDN响应数据"""
        try:
            if isinstance(data, dict) and "Image" in data:
                # 格式1: {"Image": "base64_data"}
                img_data = data["Image"]
                image_bytes = base64.b64decode(img_data)
                if len(image_bytes) > 1000:  # 验证图片大小
                    logger.info(f"[KlingImg2Img] 成功下载图片，大小: {len(image_bytes)} 字节")
                    return image_bytes

            elif isinstance(data, str):
                # 格式2: 直接的base64字符串
                image_bytes = base64.b64decode(data)
                if len(image_bytes) > 1000:
                    logger.info(f"[KlingImg2Img] 成功下载图片，大小: {len(image_bytes)} 字节")
                    return image_bytes

            elif isinstance(data, bytes):
                # 格式3: 直接的字节数据
                if len(data) > 1000:
                    logger.info(f"[KlingImg2Img] 成功下载图片，大小: {len(data)} 字节")
                    return data

            logger.error(f"CDN响应数据格式不正确或图片太小: {type(data)}")
            return None

        except Exception as e:
            logger.error(f"处理CDN响应数据失败: {e}")
            return None

    async def _process_img2img(self, image_data: bytes, prompt: str) -> Optional[str]:
        """处理图生图请求，返回生成的图片base64"""
        try:
            logger.info(f"[KlingImg2Img] 开始处理图生图: prompt={prompt}, image_size={len(image_data)}")

            # 步骤1: 上传图片到可灵
            upload_result = await self._upload_image_to_kling(image_data)
            if not upload_result:
                logger.error("图片上传失败")
                return None

            image_url = upload_result.get('url')
            if not image_url:
                logger.error("上传成功但未获取到图片URL")
                return None

            logger.info(f"[KlingImg2Img] 图片上传成功: {image_url}")

            # 步骤2: 设置图片参考介绍
            await self._set_image_reference_introduction(True)

            # 步骤3: 提交图生图任务（简化版，不需要复杂的人脸识别）
            task_result = await self._submit_img2img_task(image_url, prompt)
            if not task_result:
                logger.error("图生图任务提交失败")
                return None

            task_data = task_result.get('data', {})
            task_info = task_data.get('task', {})
            task_id = task_info.get('id', '')

            if not task_id:
                logger.error("任务提交成功但未获取到任务ID")
                return None

            logger.info(f"[KlingImg2Img] 图生图任务提交成功: {task_id}")

            # 步骤5: 轮询任务状态直到完成
            result_image = await self._poll_task_until_complete(task_id)

            return result_image

        except Exception as e:
            logger.error(f"处理图生图请求失败: {e}")
            return None

    async def _upload_image_to_kling(self, image_data: bytes) -> Optional[Dict[str, Any]]:
        """上传图片到可灵平台"""
        try:
            # 生成文件信息
            file_info = self._get_file_info(image_data)
            logger.info(f"[KlingImg2Img] 文件信息: size={file_info['size']}, md5={file_info['md5']}")

            # 步骤1: 获取上传token
            token_result = await self._get_upload_token(file_info['name'])
            if not token_result:
                return None

            token = token_result.get('token')
            if not token:
                return None

            # 步骤2: 检查上传状态
            status_result = await self._check_upload_status(token)
            if not status_result:
                return None

            # 如果文件已存在，直接返回
            if status_result.get('existed', False):
                logger.info("[KlingImg2Img] 文件已存在，无需重复上传")
                return await self._verify_upload_token(token)

            # 步骤3: 上传文件片段
            upload_result = await self._upload_file_fragment(token, image_data)
            if not upload_result:
                return None

            # 步骤4: 完成上传确认
            complete_result = await self._complete_upload(token, 1)
            if not complete_result:
                return None

            # 步骤5: 验证token并获取图片URL
            verify_result = await self._verify_upload_token(token)
            return verify_result

        except Exception as e:
            logger.error(f"上传图片到可灵失败: {e}")
            return None

    def _get_file_info(self, image_data: bytes) -> Dict[str, Any]:
        """获取文件信息"""
        md5_hash = hashlib.md5(image_data).hexdigest()
        return {
            'name': f"{md5_hash}.jpg",
            'size': len(image_data),
            'md5': md5_hash,
            'content': image_data
        }

    async def _get_upload_token(self, filename: str) -> Optional[Dict[str, Any]]:
        """获取上传token"""
        try:
            # 修复：使用正确的API端点和GET请求方式
            url = f"{self.api_base_url}/api/upload/issue/token"

            params = {'filename': filename}
            headers = self._get_request_headers()

            response = await self.tools.async_get(
                url=url,
                params=params,
                headers=headers,
                timeout=30
            )

            if response and response.get('result') == 1:
                data = response.get('data', {})
                token = data.get('token')
                endpoints = data.get('httpEndpoints', [])
                if token:
                    logger.info(f"[KlingImg2Img] 获取上传token成功: {token[:50]}...")
                    logger.info(f"[KlingImg2Img] 上传端点: {endpoints}")
                    return {'token': token, 'endpoints': endpoints}

            logger.error(f"获取上传token失败: {response}")
            return None

        except Exception as e:
            logger.error(f"获取上传token异常: {e}")
            return None

    def _get_request_headers(self) -> Dict[str, str]:
        """获取请求头"""
        headers_config = self.configData.get('headers', {})
        return {
            'Accept': headers_config.get('accept', 'application/json, text/plain, */*'),
            'Time-Zone': headers_config.get('time_zone', 'Asia/Shanghai'),
            'Accept-Language': headers_config.get('accept_language', 'zh'),
            'User-Agent': headers_config.get('user_agent', 'Mozilla/5.0 (Linux; Android 10; V2002A Build/QP1A.190711.020) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Mobile Safari/537.36'),
            'Origin': headers_config.get('origin', 'https://app.klingai.com'),
            'X-Requested-With': headers_config.get('x_requested_with', 'mark.via'),
            'Sec-Fetch-Site': headers_config.get('sec_fetch_site', 'same-site'),
            'Sec-Fetch-Mode': headers_config.get('sec_fetch_mode', 'cors'),
            'Sec-Fetch-Dest': headers_config.get('sec_fetch_dest', 'empty'),
            'Referer': headers_config.get('referer', 'https://app.klingai.com/cn/image-to-image/single/new'),
            'Accept-Encoding': headers_config.get('accept_encoding', 'gzip, deflate'),
            'Cookie': self.cookie
        }

    async def _check_upload_status(self, token: str) -> Optional[Dict[str, Any]]:
        """检查上传状态"""
        try:
            url = f"{self.upload_base_url}/api/upload/resume"

            # 修复：使用正确的参数名和GET请求
            params = {'upload_token': token}
            headers = self._get_request_headers()

            response = await self.tools.async_get(
                url=url,
                params=params,
                headers=headers,
                timeout=30
            )

            if response and response.get('result') == 1:
                logger.info("[KlingImg2Img] 检查上传状态成功")
                return response

            logger.error(f"检查上传状态失败: {response}")
            return None

        except Exception as e:
            logger.error(f"检查上传状态异常: {e}")
            return None

    async def _upload_file_fragment(self, token: str, image_data: bytes) -> Optional[Dict[str, Any]]:
        """上传文件片段"""
        try:
            url = f"{self.upload_base_url}/api/upload/fragment"

            # 修复：使用正确的参数名和上传方式
            params = {
                'upload_token': token,
                'fragment_id': '0'  # 片段索引
            }

            file_size = len(image_data)
            headers = self._get_request_headers()

            # 修复：使用application/octet-stream直接上传文件数据
            headers['Content-Type'] = 'application/octet-stream'
            headers['Content-Range'] = f'bytes 0-{file_size-1}/{file_size}'

            # 使用同步requests进行文件上传
            import requests
            response = requests.post(
                url,
                params=params,
                headers=headers,
                data=image_data,
                timeout=60
            )

            if response.status_code == 200:
                result = response.json()
                if result.get('result') == 1:
                    logger.info("[KlingImg2Img] 文件片段上传成功")
                    return result

            logger.error(f"上传文件片段失败: {response.status_code}, {response.text}")
            return None

        except Exception as e:
            logger.error(f"上传文件片段异常: {e}")
            return None

    async def _complete_upload(self, token: str, fragment_count: int) -> Optional[Dict[str, Any]]:
        """完成上传确认"""
        try:
            url = f"{self.upload_base_url}/api/upload/complete"

            # 修复：使用正确的参数名和POST请求
            params = {
                'upload_token': token,
                'fragment_count': str(fragment_count)
            }

            headers = self._get_request_headers()

            # 使用同步requests进行POST请求
            import requests
            response = requests.post(url, params=params, headers=headers, timeout=30)

            if response.status_code == 200:
                result = response.json()
                if result.get('result') == 1:
                    logger.info("[KlingImg2Img] 上传确认成功")
                    return result

            logger.error(f"上传确认失败: {response.status_code}, {response.text if hasattr(response, 'text') else 'No response'}")
            return None

        except Exception as e:
            logger.error(f"上传确认异常: {e}")
            return None

    async def _verify_upload_token(self, token: str) -> Optional[Dict[str, Any]]:
        """验证token并获取图片URL"""
        try:
            url = f"{self.api_base_url}/api/upload/verify/token"

            # 修复：使用GET请求和params参数
            params = {'token': token}
            headers = self._get_request_headers()

            response = await self.tools.async_get(
                url=url,
                params=params,
                headers=headers,
                timeout=30
            )

            if response and response.get('result') == 1:
                data_info = response.get('data', {})
                image_url = data_info.get('url', '')
                if image_url:
                    logger.info(f"[KlingImg2Img] Token验证成功，图片URL: {image_url}")
                    return {'url': image_url, 'data': data_info}

            logger.error(f"Token验证失败: {response}")
            return None

        except Exception as e:
            logger.error(f"Token验证异常: {e}")
            return None

    async def _recognize_face(self, image_url: str) -> Optional[Dict[str, Any]]:
        """人脸识别检测"""
        try:
            url = f"{self.api_base_url}/api/task/recognize"

            data = {
                'url': image_url,
                'type': 'face'
            }

            headers = self._get_request_headers()

            response = await self.tools.async_post(
                url=url,
                json_data=data,
                headers=headers,
                timeout=30
            )

            # 修复：检查正确的响应格式，并处理服务器错误
            if response and response.get('result') == 1:
                # 检查是否有服务器错误
                if response.get('status') == 500:
                    logger.warning(f"[KlingImg2Img] 人脸识别服务器繁忙: {response.get('message', '未知错误')}")
                    return None

                face_data = response.get('data', {})
                if face_data:  # 确保data不为None
                    face_items = face_data.get('faceItems', [])
                    logger.info(f"[KlingImg2Img] 人脸识别成功，检测到 {len(face_items)} 张人脸")
                    return response
                else:
                    logger.warning("[KlingImg2Img] 人脸识别返回空数据")
                    return None

            logger.error(f"人脸识别失败: {response}")
            return None

        except Exception as e:
            logger.error(f"人脸识别异常: {e}")
            return None

    async def _set_image_reference_introduction(self, status: bool) -> Optional[Dict[str, Any]]:
        """设置图片参考介绍"""
        try:
            url = f"{self.api_base_url}/api/user/extra-details/image-reference-introductions"

            data = {'status': status}
            headers = self._get_request_headers()

            response = await self.tools.async_post(
                url=url,
                json_data=data,
                headers=headers,
                timeout=30
            )

            # 修复：检查正确的响应格式
            if response and response.get('result') == 1 and response.get('status') == 200:
                logger.info("[KlingImg2Img] 图片参考介绍设置成功")
                return response

            logger.error(f"设置图片参考介绍失败: {response}")
            return None

        except Exception as e:
            logger.error(f"设置图片参考介绍异常: {e}")
            return None

    async def _submit_img2img_task(self, image_url: str, prompt: str) -> Optional[Dict[str, Any]]:
        """提交图生图任务"""
        try:
            url = f"{self.api_base_url}/api/task/submit"

            # 构建查询参数（从旧插件移植）
            params = {
                '__NS_hxfalcon': 'HUDR_sFnX-FFuAW5VsfDNK0XOP6snthhLc_IxjxBz8_r61EvYFIc7AGaHwMmlb_Lw36QFxBn0Bj4EKN4Zam4e3VuXvsYqg9MYxFklc37VMFPhmNyIlhW9Lap2cj0wiLovVU8JAvEnehn95vqdLV8O603AWy6J_0JP6IsYwdw1pyS9mspS5JAUESzgixsf9lnypQtkWlIbuMUFfLnx1CZ3fMOirJgOJB47umbxOucEsawk2fnmf4QhZ7e9X4YOdXaoWDjvoha-P4sHvw_o_-Q4FlgECrJ4ZHqVp8VG1h6c0F0KRFNV7tidPMWDtbSr1rvILnyuza3Ua-A9VfHcET2yMkFw3XaVXuT25sPENvhRkLMDFu_ZJPbBkSJQilvTsgB63yYIYd3LQ6sgvcY6eSA18dijdwT8nr-l0L3i8-TPts3nRLkkE680CHgxvlLiyHXnBo2dqOIa4QPVtfcyPZ4UrfitxGxZtwKf6RmY0cSXmO3QURDBs5UjqKolKjqG5RenkrZSaNe1GZOAMzom7XfvmknOblhm-4NyG_cfox9wI-_qd-NFoQ5eOvE8FRrsQletkdiDxFrldE1kOXicjrFN18wFYW9yHpNRpCxLuMR6ZSjBTkzXAKm1j6JP8VieRA..$HE_c7d84025277ec5ee98148d2045390731c18d8c8c8c8db21420369651ae3ccd4da250148d17dab256f7dab2648c',
                'caver': '2'
            }

            # 简化版：构建基本的输入参数，不依赖复杂的人脸识别
            inputs = [{
                "inputType": "URL",
                "url": image_url,
                "name": "input"
            }]

            # 简化的回调载荷，只包含基本信息
            callback_payloads = []

            # 添加基本参数
            generation_config = self.configData.get('generation', {})
            arguments = [
                {"name": "biz", "value": "klingai"},
                {"name": "prompt", "value": prompt},
                {"name": "imageCount", "value": generation_config.get('image_count', '1')},
                {"name": "kolors_version", "value": generation_config.get('kolors_version', '1.5')},
                {"name": "style", "value": generation_config.get('style', '默认')},
                {"name": "fidelity", "value": "0.5"},
                {"name": "humanFidelity", "value": "0.65"},
                {"name": "aspect_ratio", "value": generation_config.get('aspect_ratio', '9:16')},
                {"name": "referenceType", "value": "mmu_img2img_aiweb_v15_portrait"}
            ]

            # 构建请求数据（简化版）
            request_data = {
                "type": "mmu_img2img_aiweb",
                "inputs": inputs,
                "arguments": arguments,
                "callbackPayloads": callback_payloads
            }

            headers = self._get_request_headers()

            # 修复：将params拼接到URL中，因为async_post不支持params参数
            import urllib.parse
            url_with_params = f"{url}?{urllib.parse.urlencode(params)}"

            # 发送请求
            response = await self.tools.async_post(
                url=url_with_params,
                json_data=request_data,
                headers=headers,
                timeout=30
            )

            # 修复：检查正确的响应格式
            if response and response.get('result') == 1 and response.get('status') == 200:
                logger.info("[KlingImg2Img] 图生图任务提交成功")
                return response

            logger.error(f"图生图任务提交失败: {response}")
            return None

        except Exception as e:
            logger.error(f"图生图任务提交异常: {e}")
            return None

    async def _poll_task_until_complete(self, task_id: str) -> Optional[str]:
        """轮询任务状态直到完成，返回生成的图片base64"""
        try:
            polling_config = self.configData.get('polling', {})
            max_attempts = polling_config.get('max_attempts', 60)
            interval = polling_config.get('interval', 10)

            logger.info(f"[KlingImg2Img] 开始轮询任务状态: {task_id}, 最大尝试次数: {max_attempts}")

            for attempt in range(1, max_attempts + 1):
                logger.info(f"[KlingImg2Img] 第 {attempt}/{max_attempts} 次查询任务状态")

                # 查询任务状态
                status_result = await self._check_task_status(task_id)
                if status_result:
                    task_data = status_result.get('data', {})
                    status = task_data.get('status', 0)
                    works = task_data.get('works', [])

                    logger.info(f"[KlingImg2Img] 任务状态: {status}")

                    # 检查任务是否完成
                    if status == 20:  # 已完成
                        logger.info("[KlingImg2Img] 任务状态显示已完成！")

                        # 检查作品
                        for work in works:
                            work_status = work.get('status', 0)
                            resource = work.get('resource', {})
                            resource_url = resource.get('resource', '')

                            if work_status == 20 and resource_url:  # 作品完成
                                logger.info(f"[KlingImg2Img] 找到完成的作品: {resource_url}")
                                # 下载生成的图片
                                image_data = await self._download_result_image(resource_url)
                                if image_data:
                                    return base64.b64encode(image_data).decode('utf-8')

                    elif status < 0:  # 任务失败
                        logger.error(f"[KlingImg2Img] 任务失败，状态: {status}")
                        return None

                # 等待下次查询
                if attempt < max_attempts:
                    await asyncio.sleep(interval)

            logger.warning(f"[KlingImg2Img] 轮询超时，任务可能仍在处理中: {task_id}")
            return None

        except Exception as e:
            logger.error(f"轮询任务状态异常: {e}")
            return None

    async def _check_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """检查任务状态"""
        try:
            url = f"{self.api_base_url}/api/task/status"

            data = {'taskId': task_id}
            headers = self._get_request_headers()

            response = await self.tools.async_post(
                url=url,
                json_data=data,
                headers=headers,
                timeout=30
            )

            # 修复：检查正确的响应格式
            if response and response.get('result') == 1 and response.get('status') == 200:
                return response

            logger.error(f"检查任务状态失败: {response}")
            return None

        except Exception as e:
            logger.error(f"检查任务状态异常: {e}")
            return None

    async def _download_result_image(self, image_url: str) -> Optional[bytes]:
        """下载生成的结果图片"""
        try:
            logger.info(f"[KlingImg2Img] 下载结果图片: {image_url}")

            headers = self._get_request_headers()

            image_data = await self.tools.async_get(
                url=image_url,
                headers=headers,
                timeout=60,
                return_base64=False
            )

            if image_data and len(image_data) > 1000:
                logger.info(f"[KlingImg2Img] 结果图片下载成功，大小: {len(image_data)} 字节")
                return image_data

            logger.error("结果图片下载失败或文件太小")
            return None

        except Exception as e:
            logger.error(f"下载结果图片异常: {e}")
            return None
