# 插件启用状态
enabled = true

# 触发关键词列表
keywords = ["可灵", "可灵ai", "kl", "KL"]

# 冷却时间（秒）
cooldown_time = 60

# API配置
[api]
base_url = "https://api-app-cn.klingai.com"
upload_base_url = "https://upload.kuaishouzt.com"
login_base_url = "https://id.klingai.com"

# Cookie配置（已从旧插件迁移）
[cookie]
cookie = "__risk_web_device_id=u207y5ze1742175243903y91; did=web_20837da80a348f94b53562717fc82ff2559b; _uetvid=e9918d3002cf11f0bcd85d4c08744fb2; _clck=vw6pi4%7C2%7Cfvq%7C0%7C1902; KLING_LAST_ACCESS_REGION=cn; userId=6217807; kuaishou.ai.portal_st=ChVrdWFpc2hvdS5haS5wb3J0YWwuc3QSoAE7U7ChMsodizGDT0Q1R7LNRSDKMgUinlU03suhOirOg0qskLg_quweY-MWVLxzggOO0n1xNpF8InVWfgG13UKz7gmnLKo8-7bmXjVfxYsdasOC2ivIaDVMfzz_QD6ivMKYqjjaPD7TOaEyv6W6u3Jna-VgwXhb2BGf3RNi5KIihfkDH6Kxe5Ql8hAYUZm2ZBNIixug3UHayxJM6hewjzOUGhJaTSf_z6v_i70Q1ZuLG30vAZsiIJibv75oMRBmFqtTHEO5QRZyxpU_k9PIOTsP6TidOQNXKAUwAQ"

# 生成参数配置
[generation]
default_prompt = "换个夏日服装搭配"
aspect_ratio = "9:16"
image_count = "1"
kolors_version = "1.5"
style = "默认"

# 请求头配置
[headers]
accept = "application/json, text/plain, */*"
time_zone = "Asia/Shanghai"
accept_language = "zh"
user_agent = "Mozilla/5.0 (Linux; Android 10; V2002A Build/QP1A.190711.020) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Mobile Safari/537.36"
origin = "https://app.klingai.com"
x_requested_with = "mark.via"
sec_fetch_site = "same-site"
sec_fetch_mode = "cors"
sec_fetch_dest = "empty"
referer = "https://app.klingai.com/cn/image-to-image/single/new"
accept_encoding = "gzip, deflate"

# 轮询配置
[polling]
max_attempts = 60
interval = 10
timeout = 30
