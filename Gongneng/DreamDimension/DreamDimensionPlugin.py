from Plugins._Tools import Tools
from Config.logger import logger
from Core.PluginBase import PluginBase
import os
import time
import random
import json
import asyncio
from typing import Dict, List, Optional, Tuple
from pathlib import Path

class DreamDimensionPlugin(PluginBase):
    def __init__(self):
        super().__init__()
        # 基本信息
        self.name = "DreamDimension"
        self.description = "Ideaflow AI角色对话插件"
        self.version = "1.0.0"
        self.author = "移植自XYBot"

        # 工具类实例
        self.tools = Tools()

        # 创建临时目录
        self.temp_dir = Path("App/Plugins/DreamDimension/temp")
        self.temp_dir.mkdir(parents=True, exist_ok=True)

        # 加载配置文件
        self.configData = self.tools.returnConfigData(os.path.dirname(__file__))
        self._load_config()

        # 用户冷却时间记录
        self.user_last_request = {}

        # 会话管理
        self._chat_sessions = {}
        self._selection_sessions = {}

        # 初始化组件
        self._init_ideaflow_client()
        self._init_category_map()

    def _load_config(self):
        """加载配置"""
        self.enabled = self.configData.get('enabled', True)
        self.command = self.configData.get('command', ["造梦", "次元", "造梦次元"])
        self.command_format = self.configData.get('command_format', "🎭 造梦次元 - AI角色对话\n发送「造梦次元」开始选择角色")
        self.cooldown = self.configData.get('cooldown', 10)
        self.session_timeout = self.configData.get('session_timeout', 1800)

        # 语音回复配置
        self.voice_priority = self.configData.get('voice_priority', True)
        self.voice_fallback_text = self.configData.get('voice_fallback_text', False)

        # Ideaflow配置
        self.token = self.configData.get('token', "")
        self.uid = self.configData.get('uid', "")
        self.base_url = "https://cyapi.ideaflow.pro"

    def _init_ideaflow_client(self):
        """初始化Ideaflow客户端"""
        self.session_headers = {
            'Authorization': f'metatube-{self.token}',
            'Content-Type': 'application/json',
            'Accept': '*/*',
            'Origin': 'https://ciyuan.ideaflow.pro',
            'User-Agent': 'Mozilla/5.0 (Linux; Android 10; V2002A Build/QP1A.190711.020) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Mobile Safari/537.36',
            'uid': self.uid,
            'pg': '7',
            'c': 'h5',
            'env': 'ideaflow'
        }

    def _init_category_map(self):
        """初始化角色分类映射"""
        self.category_map = {
            "1": {"id": "000004234382623419957248", "name": "女性角色", "emoji": "1️⃣"},
            "2": {"id": "000004234380287360417793", "name": "男性角色", "emoji": "2️⃣"},
            "3": {"id": "000004229259323849064450", "name": "恋爱剧情", "emoji": "3️⃣"},
            "4": {"id": "000004229259485833101313", "name": "挑战类型", "emoji": "4️⃣"},
            "5": {"id": "000004229259738950975488", "name": "模拟器", "emoji": "5️⃣"},
            "6": {"id": "000004229260203109416961", "name": "学习助手", "emoji": "6️⃣"},
            "7": {"id": "000004229260703926075393", "name": "实用工具", "emoji": "7️⃣"},
            "8": {"id": "000004229258999495180288", "name": "最新角色", "emoji": "8️⃣"},
            # 支持中文名称
            "女性": {"id": "000004234382623419957248", "name": "女性角色", "emoji": "1️⃣"},
            "男性": {"id": "000004234380287360417793", "name": "男性角色", "emoji": "2️⃣"},
            "恋爱": {"id": "000004229259323849064450", "name": "恋爱剧情", "emoji": "3️⃣"},
            "挑战": {"id": "000004229259485833101313", "name": "挑战类型", "emoji": "4️⃣"},
            "模拟": {"id": "000004229259738950975488", "name": "模拟器", "emoji": "5️⃣"},
            "学习": {"id": "000004229260203109416961", "name": "学习助手", "emoji": "6️⃣"},
            "工具": {"id": "000004229260703926075393", "name": "实用工具", "emoji": "7️⃣"},
            "最新": {"id": "000004229258999495180288", "name": "最新角色", "emoji": "8️⃣"},
        }

    def _get_session_key(self, roomid: str, sender: str) -> str:
        """生成会话键"""
        return f"{roomid}_{sender}"

    def _is_exit_command(self, content: str) -> bool:
        """检查是否是退出命令"""
        exit_commands = ["结束", "退出", "再见", "拜拜", "quit", "exit", "bye"]
        return content.strip().lower() in exit_commands

    def _cleanup_expired_sessions(self):
        """清理过期会话"""
        current_time = time.time()
        expired_keys = []

        for key, session in self._chat_sessions.items():
            if current_time - session.get("last_active", 0) > self.session_timeout:
                expired_keys.append(key)

        for key in expired_keys:
            self._chat_sessions.pop(key, None)

        # 清理选择会话
        expired_selection_keys = []
        for key, session in self._selection_sessions.items():
            if current_time - session.get("created_time", 0) > 300:  # 5分钟选择超时
                expired_selection_keys.append(key)

        for key in expired_selection_keys:
            self._selection_sessions.pop(key, None)

    async def handle_message(self, msg) -> bool:
        """
        处理群聊消息
        Args:
            msg: 消息对象（WxMsg类）
        Returns:
            bool: True表示已处理消息，False表示未处理
        """
        # 只处理文本消息
        if msg.type != 1:
            return False
            
        # 检查插件是否启用
        if not self.enabled:
            return False

        content = str(msg.content).strip()
        session_key = self._get_session_key(msg.roomid or msg.sender, msg.sender)

        # 清理过期会话
        self._cleanup_expired_sessions()

        # 检查是否在角色选择模式
        if session_key in self._selection_sessions:
            await self._handle_character_selection(msg, content)
            return True

        # 检查是否在对话模式
        if session_key in self._chat_sessions:
            await self._handle_conversation(msg, content)
            return True

        # 检查是否是插件命令
        command = content.split(" ", 1)
        if command[0] not in self.command:
            return False

        logger.info(f"插件 {self.name} 处理造梦次元消息: {msg.content}")

        # 限流检查
        wait_time = self._check_user_limit(msg.roomid or msg.sender, msg.sender)
        if wait_time > 0:
            target_id = msg.roomid if msg.from_group() else msg.sender
            await self.dp.sendText(f"请等待 {wait_time:.1f} 秒", target_id, msg.self_wxid)
            return True

        # 启动角色选择流程
        await self._start_character_selection(msg)
        return True

    async def handle_private_message(self, msg) -> bool:
        """
        处理私聊消息
        复用群聊消息处理逻辑
        """
        return await self.handle_message(msg)

    def _check_user_limit(self, roomid: str, sender: str) -> float:
        """检查用户冷却时间"""
        user_key = f"{roomid}_{sender}"
        last_request = self.user_last_request.get(user_key, 0)
        current_time = time.time()
        elapsed = current_time - last_request
        self.user_last_request[user_key] = current_time

        if elapsed < self.cooldown:
            return self.cooldown - elapsed

        return 0

    async def refresh_token(self) -> bool:
        """刷新JWT Token"""
        try:
            url = f"{self.base_url}/user/token/flush"
            data = await self.tools.async_post(url, headers=self.session_headers)

            if data and data.get('code') == 0:
                new_token = data['data']['token']
                self.token = new_token
                self.session_headers['Authorization'] = f'metatube-{new_token}'
                return True

            logger.error(f"[{self.name}] Token刷新失败")
            return False

        except Exception as e:
            logger.error(f"[{self.name}] Token刷新异常: {e}")
            return False

    async def get_pipe_list(self, pipe_id: str, page_no: int = 1) -> Optional[List[Dict]]:
        """获取管道内容列表"""
        try:
            url = f"{self.base_url}/distribute/pipe/{pipe_id}"
            params = {'pageNo': page_no}

            data = await self.tools.async_get(url, params=params, headers=self.session_headers)

            if data and data.get('code') == 0:
                return data.get('data', [])

            logger.error(f"[{self.name}] 获取管道列表失败")
            return None

        except Exception as e:
            logger.error(f"[{self.name}] 获取管道列表异常: {e}")
            return None

    async def get_pipe_detail(self, pipe_id: str) -> Optional[Dict]:
        """获取管道详细信息"""
        try:
            url = f"{self.base_url}/pipe/detail/{pipe_id}"
            data = await self.tools.async_get(url, headers=self.session_headers)

            if data and data.get('code') == 0:
                return data.get('data')

            logger.error(f"[{self.name}] 获取管道详情失败")
            return None

        except Exception as e:
            logger.error(f"[{self.name}] 获取管道详情异常: {e}")
            return None

    async def send_message(self, pipe_id: str, message: str) -> Optional[str]:
        """发送消息给AI角色"""
        try:
            url = f"{self.base_url}/pipe/chat"
            payload = {
                "pipeId": pipe_id,
                "in": [{"name": "k_1", "type": "str", "val": message}]
            }

            data = await self.tools.async_post(url, json_data=payload, headers=self.session_headers)

            if data and data.get('code') == 0:
                return data.get('data')

            logger.error(f"[{self.name}] 发送消息失败")
            return None

        except Exception as e:
            logger.error(f"[{self.name}] 发送消息异常: {e}")
            return None

    async def poll_response(self, album_id: str, max_attempts: int = 30) -> Optional[Dict]:
        """轮询获取AI回复"""
        try:
            url = f"{self.base_url}/cutebox/pipe/album"
            params = {'albumId': album_id}

            # 添加轮询专用的header
            headers = self.session_headers.copy()
            headers['appid'] = '000003523433300180353024'

            for _ in range(max_attempts):
                data = await self.tools.async_get(url, params=params, headers=headers)

                if data and data.get('code') == 0:
                    progress = data['data']['progress']

                    if progress == 'completed':
                        return data['data']
                    elif progress == 'handing':
                        await asyncio.sleep(2)
                        continue
                    else:
                        return None

                await asyncio.sleep(2)

            return None

        except Exception as e:
            logger.error(f"[{self.name}] 轮询异常: {e}")
            return None

    def parse_ai_response(self, content_str: str) -> Tuple[str, Optional[str]]:
        """解析AI回复内容"""
        try:
            content_list = json.loads(content_str)

            for item in content_list:
                if isinstance(item, dict) and 'content' in item:
                    text_reply = ""
                    audio_url = None

                    for content_item in item['content']:
                        if content_item.get('type') == 'str' and content_item.get('outName') == 'reply':
                            text_reply = content_item.get('val', '')
                        elif content_item.get('type') == 'audio':
                            audio_url = content_item.get('val')

                    if text_reply:
                        return text_reply, audio_url

            return "AI回复解析失败", None

        except Exception as e:
            return f"AI回复解析异常: {e}", None

    async def _start_character_selection(self, msg):
        """启动角色选择流程"""
        session_key = self._get_session_key(msg.roomid or msg.sender, msg.sender)

        # 创建选择会话
        self._selection_sessions[session_key] = {
            "created_time": time.time(),
            "step": "category_selection"
        }

        # 发送角色分类选择菜单
        menu_text = (
            "🎭 欢迎来到造梦次元！请选择角色类型：\n\n"
            "1️⃣ 女性角色    2️⃣ 男性角色\n"
            "3️⃣ 恋爱剧情    4️⃣ 挑战类型\n"
            "5️⃣ 模拟器      6️⃣ 学习助手\n"
            "7️⃣ 实用工具    8️⃣ 最新角色\n\n"
            "💡 直接发送数字或类型名称即可选择\n"
            "💡 发送「取消」退出选择"
        )

        target_id = msg.roomid if msg.from_group() else msg.sender
        await self.dp.sendText(menu_text, target_id, msg.self_wxid)

    async def _handle_character_selection(self, msg, content: str):
        """处理角色选择"""
        session_key = self._get_session_key(msg.roomid or msg.sender, msg.sender)
        selection_session = self._selection_sessions.get(session_key)

        if not selection_session:
            return

        # 检查取消命令
        if content.strip() in ["取消", "退出", "cancel"]:
            self._selection_sessions.pop(session_key, None)
            target_id = msg.roomid if msg.from_group() else msg.sender
            await self.dp.sendText("已取消角色选择", target_id, msg.self_wxid)
            return

        if selection_session["step"] == "category_selection":
            await self._handle_category_selection(msg, content)
        elif selection_session["step"] == "character_selection":
            await self._handle_specific_character_selection(msg, content)

    async def _handle_category_selection(self, msg, content: str):
        """处理分类选择"""
        session_key = self._get_session_key(msg.roomid or msg.sender, msg.sender)

        # 查找匹配的分类
        selected_category = None
        content_lower = content.strip().lower()

        for key, category in self.category_map.items():
            if key == content.strip() or key.lower() == content_lower:
                selected_category = category
                break

        target_id = msg.roomid if msg.from_group() else msg.sender

        if not selected_category:
            await self.dp.sendText("❌ 无效选择，请重新选择或发送「取消」退出", target_id, msg.self_wxid)
            return

        try:
            # 获取该分类的角色列表
            pipe_data = await self.get_pipe_list(selected_category["id"], 1)
            if not pipe_data:
                await self.dp.sendText("获取角色列表失败，请稍后重试", target_id, msg.self_wxid)
                self._selection_sessions.pop(session_key, None)
                return

            # 显示第一页角色列表
            await self._show_character_page(msg, session_key, selected_category, pipe_data, 1)

        except Exception as e:
            logger.error(f"[{self.name}] 获取角色列表异常: {e}")
            await self.dp.sendText("获取角色列表时出现异常，请稍后重试", target_id, msg.self_wxid)
            self._selection_sessions.pop(session_key, None)

    async def _show_character_page(self, msg, session_key: str, selected_category: dict, all_characters: list, page: int):
        """显示角色列表的指定页面"""
        page_size = 10  # 每页显示10个角色
        total_count = len(all_characters)
        total_pages = (total_count + page_size - 1) // page_size  # 向上取整

        # 计算当前页的角色范围
        start_idx = (page - 1) * page_size
        end_idx = min(start_idx + page_size, total_count)
        current_page_characters = all_characters[start_idx:end_idx]

        # 构建角色列表文本
        characters_text = f"🎭 {selected_category['name']} 角色列表 (第{page}/{total_pages}页)：\n\n"

        for i, item in enumerate(current_page_characters, start_idx + 1):
            char_name = item.get('cartoonName', '未知角色')
            theme_name = item.get('name', '未知主题')
            characters_text += f"{i}. {char_name} - {theme_name}\n"

        # 添加操作提示
        characters_text += f"\n📊 共 {total_count} 个角色，当前第 {page}/{total_pages} 页\n\n"
        characters_text += "💡 操作说明：\n"
        characters_text += "• 发送数字选择角色\n"
        characters_text += "• 发送「随机」随机选择角色\n"

        # 翻页操作
        if page > 1:
            characters_text += "• 发送「上一页」查看上一页\n"
        if page < total_pages:
            characters_text += "• 发送「下一页」查看下一页\n"

        characters_text += "• 发送「返回」重新选择分类"

        # 更新选择会话状态
        self._selection_sessions[session_key] = {
            "created_time": time.time(),
            "step": "character_selection",
            "category": selected_category,
            "all_characters": all_characters,
            "current_page": page,
            "total_pages": total_pages,
            "page_size": page_size
        }

        target_id = msg.roomid if msg.from_group() else msg.sender
        await self.dp.sendText(characters_text, target_id, msg.self_wxid)

    async def _handle_specific_character_selection(self, msg, content: str):
        """处理具体角色选择"""
        session_key = self._get_session_key(msg.roomid or msg.sender, msg.sender)
        selection_session = self._selection_sessions.get(session_key)

        if not selection_session:
            return

        content = content.strip()
        target_id = msg.roomid if msg.from_group() else msg.sender

        # 处理返回命令
        if content in ["返回", "back"]:
            await self._start_character_selection(msg)
            return

        # 处理翻页命令
        if content in ["下一页", "next", "下页"]:
            current_page = selection_session.get("current_page", 1)
            total_pages = selection_session.get("total_pages", 1)
            if current_page < total_pages:
                await self._show_character_page(
                    msg, session_key,
                    selection_session["category"],
                    selection_session["all_characters"],
                    current_page + 1
                )
            else:
                await self.dp.sendText("已经是最后一页了", target_id, msg.self_wxid)
            return

        if content in ["上一页", "prev", "上页"]:
            current_page = selection_session.get("current_page", 1)
            if current_page > 1:
                await self._show_character_page(
                    msg, session_key,
                    selection_session["category"],
                    selection_session["all_characters"],
                    current_page - 1
                )
            else:
                await self.dp.sendText("已经是第一页了", target_id, msg.self_wxid)
            return

        # 处理随机选择
        if content in ["随机", "random"]:
            selected_character = random.choice(selection_session["all_characters"])
        else:
            # 处理数字选择
            try:
                choice_num = int(content)
                all_characters = selection_session["all_characters"]
                if 1 <= choice_num <= len(all_characters):
                    selected_character = all_characters[choice_num - 1]
                else:
                    await self.dp.sendText(f"❌ 无效选择，请输入1-{len(all_characters)}之间的数字", target_id, msg.self_wxid)
                    return
            except ValueError:
                await self.dp.sendText("❌ 请输入有效数字、「随机」、「上一页」、「下一页」或「返回」", target_id, msg.self_wxid)
                return

        # 开始对话
        await self._start_conversation_with_character(msg, selected_character)

    async def _start_conversation_with_character(self, msg, character: dict):
        """开始与指定角色的对话"""
        session_key = self._get_session_key(msg.roomid or msg.sender, msg.sender)

        # 清理选择会话
        self._selection_sessions.pop(session_key, None)

        # 创建对话会话
        self._chat_sessions[session_key] = {
            "character": character,
            "character_name": character.get('cartoonName', '未知角色'),
            "theme_name": character.get('name', '未知主题'),
            "pipe_id": character['id'],
            "conversation_count": 0,
            "last_active": time.time()
        }

        target_id = msg.roomid if msg.from_group() else msg.sender

        try:
            # 获取角色详情和开场白
            pipe_detail = await self.get_pipe_detail(character['id'])
            if pipe_detail:
                character_info = pipe_detail.get('pipe', {}).get('cartoon', {})
                character_name = character_info.get('name', character.get('cartoonName', ''))

                # 更新角色名称
                self._chat_sessions[session_key]["character_name"] = character_name

                # 构建介绍消息
                intro_msg = f"🎭 {character_name}\n"

                summary = character_info.get('summary', '')
                if summary and len(summary) > 10:
                    intro_msg += f"📝 {summary}\n"

                # 获取开场白
                wrapper = pipe_detail.get('pipe', {}).get('wrapper', {})
                greetings = wrapper.get('greetings', [])

                greeting_text = ""
                for greeting in greetings:
                    if greeting.get('type') == 'normal':
                        content = greeting.get('content', [])
                        for item in content:
                            if item.get('type') == 'str':
                                greeting_text = item.get('val', '')
                                break
                        if greeting_text:
                            break

                if greeting_text:
                    intro_msg += f"\n💬 {greeting_text}\n"

                intro_msg += (
                    f"\n💡 现在可以直接和我聊天\n"
                    f"💡 发送「结束」退出对话"
                )

                await self.dp.sendText(intro_msg, target_id, msg.self_wxid)
            else:
                # 简单介绍
                character_name = character.get('cartoonName', '角色')
                await self.dp.sendText(
                    f"🎭 开始与 {character_name} 对话\n\n"
                    f"💡 现在可以直接和我聊天\n"
                    f"💡 发送「结束」退出对话",
                    target_id, msg.self_wxid
                )

        except Exception as e:
            logger.error(f"[{self.name}] 获取角色详情异常: {e}")
            character_name = character.get('cartoonName', '角色')
            await self.dp.sendText(
                f"🎭 开始与 {character_name} 对话\n\n"
                f"💡 现在可以直接和我聊天\n"
                f"💡 发送「结束」退出对话",
                target_id, msg.self_wxid
            )

    async def _handle_conversation(self, msg, content: str):
        """处理对话消息"""
        session_key = self._get_session_key(msg.roomid or msg.sender, msg.sender)
        chat_session = self._chat_sessions.get(session_key)

        if not chat_session:
            return

        target_id = msg.roomid if msg.from_group() else msg.sender

        # 检查退出命令
        if self._is_exit_command(content):
            character_name = chat_session["character_name"]
            self._chat_sessions.pop(session_key, None)
            await self.dp.sendText(f"👋 与 {character_name} 的对话结束了", target_id, msg.self_wxid)
            return

        # 更新活跃时间
        chat_session["last_active"] = time.time()

        pipe_id = chat_session["pipe_id"]
        character_name = chat_session["character_name"]

        try:
            # 发送消息
            album_id = await self.send_message(pipe_id, content)
            if not album_id:
                await self.dp.sendText("发送消息失败，请重试", target_id, msg.self_wxid)
                return

            # 轮询获取回复
            response_data = await self.poll_response(album_id)
            if not response_data:
                await self.dp.sendText("获取AI回复失败，请重试", target_id, msg.self_wxid)
                return

            # 解析AI回复
            content_str = response_data.get('content', '')
            if content_str:
                ai_reply, audio_url = self.parse_ai_response(content_str)
                logger.debug(f"[{self.name}] AI回复解析结果 - 文字: {ai_reply[:50]}..., 语音URL: {audio_url}")

                # 优先发送语音回复，不支持时发送文字回复
                if audio_url and self.voice_priority:
                    voice_sent = await self._send_voice_reply(msg, audio_url)
                    if not voice_sent and self.voice_fallback_text:
                        # 语音发送失败，发送文字回复
                        reply_msg = f"🎭 {character_name}: {ai_reply}"
                        await self.dp.sendText(reply_msg, target_id, msg.self_wxid)
                else:
                    # 没有语音URL的情况，直接发送文字回复
                    reply_msg = f"🎭 {character_name}: {ai_reply}"
                    await self.dp.sendText(reply_msg, target_id, msg.self_wxid)

                # 更新对话计数
                chat_session["conversation_count"] += 1

                # 每10轮对话后自动刷新Token
                if chat_session["conversation_count"] % 10 == 0:
                    await self.refresh_token()
            else:
                await self.dp.sendText("AI回复内容为空", target_id, msg.self_wxid)

        except Exception as e:
            logger.error(f"[{self.name}] 对话异常: {e}")
            await self.dp.sendText("对话出现异常，请重试", target_id, msg.self_wxid)

    async def _send_voice_reply(self, msg, audio_url: str) -> bool:
        """发送语音回复"""
        try:
            target_id = msg.roomid if msg.from_group() else msg.sender

            # 下载音频文件
            audio_data = await self._download_audio(audio_url)
            if not audio_data:
                return False

            # 保存临时音频文件
            temp_audio_path = self.temp_dir / f"voice_{int(time.time())}.mp3"

            try:
                with open(temp_audio_path, "wb") as f:
                    f.write(audio_data)

                # 发送语音消息
                result = await self.dp.sendVoice(str(temp_audio_path), target_id, msg.self_wxid)

                # 检查发送结果
                if result:
                    if isinstance(result, dict) and result.get("Success"):
                        return True
                    elif isinstance(result, list) and len(result) > 0 and result[0].get("Success"):
                        return True
                return False

            finally:
                # 清理临时文件
                if temp_audio_path.exists():
                    temp_audio_path.unlink()

        except Exception as e:
            logger.error(f"[{self.name}] 发送语音回复异常: {e}")
            return False

    async def _download_audio(self, audio_url: str) -> Optional[bytes]:
        """下载音频文件"""
        try:
            data = await self.tools.async_get(
                url=audio_url,
                timeout=30,
                return_json=False,
                return_base64=False
            )

            if data and isinstance(data, bytes) and len(data) > 100:
                return data
            return None

        except Exception as e:
            logger.error(f"[{self.name}] 下载音频异常: {e}")
            return None
