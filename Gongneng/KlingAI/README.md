# KlingAI 插件

## 功能说明
可灵AI图片生成插件（国内版），支持通过文本描述生成高质量图片。

## 主要功能
1. **文本生图**：根据文本描述生成图片
2. **多种比例**：支持不同的图片比例设置
3. **用户限制**：防止频繁请求，30秒冷却时间

## 使用方法

### 基本用法
```
可灵 [提示词]
```

### 示例
```
可灵 穿着汉服的少女在樱花树下
可灵 赛博朋克风格的城市夜景
可灵 可爱的小猫咪在花园里玩耍
```

### 支持的关键词
- `可灵` - 基础触发词
- `可灵ai` - 完整触发词
- `kl` - 简化触发词
- `KL` - 大写简化触发词

## 配置说明

### config.toml
```toml
# 插件启用状态
enabled = true

# 触发关键词列表
keywords = ["可灵", "可灵ai", "kl", "KL"]

# 生成参数配置
[config]
aspect_ratio = "9:16"  # 图像比例
image_count = "1"      # 生成图片数量
kolors_version = "1.5" # 可灵AI版本
style = "默认"         # 默认风格

# API相关配置
[api]
base_url = "https://klingai.kuaishou.com"
submit_endpoint = "/app/task/submit"
status_endpoint = "/app/task/status"

# Cookie设置（已从旧插件迁移）
[cookie]
app_st = "ChdrdWFpc2hvdS5rbGluZ2FpLmFwcC5zdBKgA..."  # 已配置
user_id = "6217807"  # 已配置
```

### Cookie状态
✅ Cookie已从旧插件自动迁移，无需手动配置
⚠️ 如果Cookie过期，请重新获取并更新配置文件

## 支持的图片比例
- `1:1` - 正方形
- `3:4` - 竖图
- `4:3` - 横图
- `9:16` - 竖屏（默认）
- `16:9` - 横屏

## 注意事项
1. 需要有效的可灵AI账号和Cookie
2. 每个用户有30秒的冷却时间
3. 生成时间较长，请耐心等待
4. 仅支持国内版可灵AI

## 错误排查
1. **Cookie失效**：重新获取Cookie并更新配置
2. **生成失败**：检查网络连接和提示词内容
3. **请求过频**：等待冷却时间结束

## 更新日志
- v1.0.0: 从旧插件移植完成，Cookie已自动迁移，可直接使用
