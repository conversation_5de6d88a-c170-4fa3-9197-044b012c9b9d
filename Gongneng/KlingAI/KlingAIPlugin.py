from Plugins._Tools import Tools
from Config.logger import logger
from Core.PluginBase import PluginBase
import os
import time
import httpx
import asyncio
import base64
from pathlib import Path


class KlingAIPlugin(PluginBase):
    def __init__(self):
        super().__init__()
        self.name = "KlingAI"
        self.description = "可灵AI图片生成插件（国内版）"
        self.version = "1.0.0"
        self.author = "XYBot"

        # 加载配置
        config = Tools().returnConfigData(os.path.dirname(__file__))
        self.keywords = config.get('keywords', ['可灵', '可灵ai', 'kl', 'KL'])

        # API和生成参数
        api_config = config.get('api', {})
        ai_config = config.get('config', {})
        cookie_config = config.get('cookie', {})

        self.base_url = api_config.get('base_url', 'https://klingai.kuaishou.com')
        self.aspect_ratio = ai_config.get('aspect_ratio', '9:16')
        self.image_count = ai_config.get('image_count', '1')
        self.kolors_version = ai_config.get('kolors_version', '1.5')
        self.style = ai_config.get('style', '默认')
        self.app_st = cookie_config.get('app_st', '')
        self.user_id = cookie_config.get('user_id', '')

        # 简化限流：只保留用户冷却
        self.user_limits = {}
        self.cooldown_time = 30

        # 临时目录
        self.temp_dir = Path("temp/KlingAI")
        self.temp_dir.mkdir(parents=True, exist_ok=True)

        # 通用URL参数
        self.url_params = {
            "mod": "HUAWEI(ALN-AL10)", "appver": "2.2.10.126", "language": "zh-cn",
            "did_tag": "0", "egid": "DFP514B0C94F1B61D9141FC3B8FE74638C5038B1B252A5066284E218759EC6EE",
            "thermal": "10000", "net": "WIFI", "kcv": "5", "app": "0", "kpf": "ANDROID_PHONE",
            "ver": "2.2", "android_os": "0", "oDid": "ANDROID_c65c5f2e304e5870", "kpn": "KLING",
            "newOc": "OPPO", "androidApiLevel": "29", "country_code": "cn", "did_gt": "1741869521914",
            "sys": "ANDROID_10", "max_memory": "384", "client_key": "3c2cd3f3",
            "cold_launch_time_ms": "1741869521485", "oc": "OPPO", "sh": "2376", "sw": "1080",
            "c": "OPPO", "os": "android", "did": "ANDROID_c65c5f2e304e5870"
        }

    async def handle_message(self, msg) -> bool:
        """处理消息"""
        if msg.type != 1:
            return False

        command = msg.content.strip().split(" ", 1)
        if command[0] not in self.keywords:
            return False

        target_id = msg.roomid if msg.from_group() else msg.sender

        if len(command) == 1:
            await self.dp.sendText("使用方法: 可灵 [提示词]\n示例: 可灵 穿着汉服的少女在樱花树下", target_id, msg.self_wxid)
            return True

        # 检查冷却时间
        user_key = f"{target_id}_{msg.sender}"
        current_time = time.time()
        last_request = self.user_limits.get(user_key, 0)

        if current_time - last_request < self.cooldown_time:
            wait_time = self.cooldown_time - (current_time - last_request)
            await self.dp.sendText(f"请等待 {wait_time:.1f} 秒后再试", target_id, msg.self_wxid)
            return True

        self.user_limits[user_key] = current_time

        # 处理生图请求
        await self._handle_drawing_request(msg, command[1].strip())
        return True

    async def _handle_drawing_request(self, msg, prompt: str):
        """处理绘图请求"""
        target_id = msg.roomid if msg.from_group() else msg.sender

        try:
            await self.dp.sendText("正在生成图片...", target_id, msg.self_wxid)

            image_path = await self._process_drawing_request(prompt)

            if image_path:
                with open(image_path, "rb") as f:
                    image_base64 = base64.b64encode(f.read()).decode('utf-8')
                await self.dp.sendImage(image_base64, target_id, msg.self_wxid)
            else:
                await self.dp.sendText("图片生成失败，请稍后再试", target_id, msg.self_wxid)

        except Exception as e:
            logger.error(f"处理绘图请求失败: {e}")
            await self.dp.sendText("处理失败，请稍后再试", target_id, msg.self_wxid)

    async def _process_drawing_request(self, prompt: str) -> str:
        """处理绘图请求，返回图像路径"""
        try:
            task_id = await self._submit_task(prompt)
            if not task_id:
                return ""

            image_url = await self._poll_task_status(task_id)
            if not image_url:
                return ""

            # 下载图像
            filename = f"{int(time.time())}_{prompt[:10].replace(' ', '_')}.png"
            image_path = os.path.join(self.temp_dir, filename)

            if await self._download_image(image_url, image_path):
                return image_path
            return ""

        except Exception as e:
            logger.error(f"处理绘图请求异常: {e}")
            return ""

    def _build_headers(self):
        """构建请求头"""
        return {
            "Connection": "keep-alive",
            "User-Agent": "kwai-android aegon/3.44.1",
            "Accept-Language": "zh-cn",
            "X-REQUESTID": f"{int(time.time()*1000)}{str(int(time.time()*10))[-4:]}",
            "Cookie": f"__NSWJ=;kuaishou.klingai.app_st={self.app_st};userId={self.user_id}"
        }

    async def _submit_task(self, prompt: str) -> str:
        """提交任务"""
        try:
            url_params_str = "&".join([f"{k}={v}" for k, v in self.url_params.items()])
            url = f"{self.base_url}/app/task/submit?{url_params_str}"

            payload = {
                "callbackPayloads": [],
                "inputs": [],
                "arguments": [
                    {"name": "prompt", "value": prompt},
                    {"name": "aspect_ratio", "value": self.aspect_ratio},
                    {"name": "imageCount", "value": self.image_count},
                    {"name": "biz", "value": "klingai"},
                    {"name": "style", "value": self.style},
                    {"name": "kolors_version", "value": self.kolors_version}
                ],
                "type": "mmu_txt2img_aiweb"
            }

            headers = self._build_headers()
            headers["Content-Type"] = "application/json"

            # 设置适当的超时配置
            timeout_config = httpx.Timeout(
                connect=10.0,    # 连接超时
                read=30.0,       # 读取超时
                write=30.0,      # 写入超时
                pool=5.0         # 连接池超时
            )

            async with httpx.AsyncClient(timeout=timeout_config, follow_redirects=True) as client:
                response = await client.post(url, json=payload, headers=headers)
                if response.status_code == 200:
                    data = response.json()
                    if data.get("result") == 1 and data.get("status") == 200:
                        return str(data.get("data", {}).get("task", {}).get("id", ""))
            return ""

        except Exception as e:
            logger.error(f"提交任务异常: {e}")
            return ""

    async def _poll_task_status(self, task_id: str, max_retries: int = 100, interval: float = 2.0) -> str:
        """轮询任务状态"""
        try:
            url_params = self.url_params.copy()
            url_params.update({"taskId": task_id, "earphoneMode": "1"})
            url_params_str = "&".join([f"{k}={v}" for k, v in url_params.items()])
            url = f"{self.base_url}/app/task/status?{url_params_str}"

            for _ in range(max_retries):
                # 设置适当的超时配置
                timeout_config = httpx.Timeout(
                    connect=10.0,    # 连接超时
                    read=30.0,       # 读取超时
                    write=30.0,      # 写入超时
                    pool=5.0         # 连接池超时
                )

                async with httpx.AsyncClient(timeout=timeout_config, follow_redirects=True) as client:
                    response = await client.get(url, headers=self._build_headers())

                    if response.status_code == 200:
                        data = response.json()
                        status = data.get("data", {}).get("status", 0)

                        if status == 99:  # 任务完成
                            works = data.get("data", {}).get("works", [])
                            if works:
                                resource = works[0].get("resource", {}).get("resource", "")
                                if resource:
                                    return resource

                        elif status in [5, 10]:  # 处理中
                            await asyncio.sleep(interval)
                            continue
                        else:
                            return ""

                await asyncio.sleep(interval)
            return ""

        except Exception as e:
            logger.error(f"轮询任务状态异常: {e}")
            return ""

    async def _download_image(self, url: str, file_path: str) -> bool:
        """下载图像"""
        try:
            headers = {
                "User-Agent": "Dalvik/2.1.0 (Linux; U; Android 10; ALN-AL10 Build/HUAWEIALN-AL10)",
                "Connection": "Keep-Alive",
                "Accept-Encoding": "gzip"
            }

            # 设置适当的超时配置，下载文件可能需要更长时间
            timeout_config = httpx.Timeout(
                connect=10.0,    # 连接超时
                read=60.0,       # 读取超时（下载文件）
                write=30.0,      # 写入超时
                pool=5.0         # 连接池超时
            )

            async with httpx.AsyncClient(timeout=timeout_config, follow_redirects=True) as client:
                response = await client.get(url, headers=headers)
                if response.status_code == 200:
                    with open(file_path, "wb") as f:
                        f.write(response.content)
                    return True
            return False

        except Exception as e:
            logger.error(f"下载图像异常: {e}")
            return False
