import xml.etree.ElementTree as ET
from typing import List, Dict
import json
from Config.logger import logger
import os

from Plugins._Tools import Tools
from Core.PluginBase import PluginBase

class WelcomePluginPlugin(PluginBase):
    """欢迎新成员插件"""
    
    def __init__(self):
        super().__init__()
        # 基本信息（重要：name必须与目录名一致）
        self.name = "WelcomePlugin"
        self.description = "自动欢迎新加入群聊的成员，支持群聊独立配置和管理"
        self.version = "1.2.0"
        self.author = "移植自XYBot"
        
        # 工具类实例
        self.tools = Tools()
        
        # 加载配置文件
        self.configData = self.tools.returnConfigData(os.path.dirname(__file__))
        
        # 初始化配置
        self.enabled = self.configData.get('enabled', True)
        self.default_welcome_message = self.configData.get('default_welcome_message', "@{nickname} 欢迎加入我们！")
        
        # 群组配置
        self.groups = {}
        
        # 添加用户昵称缓存，格式为 {group_id: {wxid: nickname}}
        self.user_nicknames = {}
        
        # 读取全局管理员列表
        try:
            import Config.ConfigServer as ConfigServer
            config_data = ConfigServer.returnConfigData()
            dpbot_config = config_data.get('DPBotConfig', {})
            self.global_admins = dpbot_config.get('Administrators', ['wxid_ubbh6q832tcs21'])
        except Exception:
            self.global_admins = ['wxid_ubbh6q832tcs21']
        
        # 加载配置
        self._load_config()

    def _load_config(self) -> bool:
        """从文件加载配置"""
        config_path = os.path.join(os.path.dirname(__file__), "welcome_messages.json")

        if not os.path.exists(config_path):
            self._save_config()
            return True

        try:
            with open(config_path, "r", encoding="utf-8") as f:
                config = json.load(f)

            # 加载插件配置
            plugin_config = config.get("plugin_config", {})
            self.enabled = plugin_config.get("enable", True)
            self.default_welcome_message = plugin_config.get("default_welcome_message", "@{nickname} 欢迎加入我们！")

            # 加载群组配置
            self.groups = config.get("groups", {})
            return True

        except Exception as e:
            logger.error(f"[{self.name}] 加载配置失败: {e}")
            return False
            
    def _save_config(self) -> bool:
        """保存配置到文件"""
        try:
            config = {
                "plugin_config": {
                    "enable": self.enabled,
                    "default_welcome_message": self.default_welcome_message
                },
                "groups": self.groups
            }

            config_path = os.path.join(os.path.dirname(__file__), "welcome_messages.json")
            with open(config_path, "w", encoding="utf-8") as f:
                json.dump(config, f, ensure_ascii=False, indent=4)
            return True

        except Exception as e:
            logger.error(f"[{self.name}] 保存配置失败: {e}")
            return False

    async def reload_config(self) -> bool:
        """重新加载配置"""
        try:
            return self._load_config()
        except Exception as e:
            logger.error(f"[{self.name}] 重载配置失败: {e}")
            return False

    def _is_group_admin(self, group_id: str, wxid: str) -> bool:
        """检查用户是否是指定群的管理员或全局管理员"""
        # 检查是否是全局管理员
        if wxid in self.global_admins:
            return True
        # 检查是否是群管理员
        if group_id in self.groups:
            if wxid in self.groups[group_id]["admins"]:
                return True

        return False
        
    def _get_group_config(self, group_id: str) -> dict:
        """获取群聊配置，如果没有则返回默认配置"""
        if group_id in self.groups:
            return self.groups[group_id]
        return {
            "admins": [],
            "welcome_message": self.default_welcome_message,
            "enable": self.enabled
        }

    def _parse_member_info(self, root: ET.Element, link_name: str = "names") -> List[Dict[str, str]]:
        """解析新成员信息"""
        new_members = []
        try:
            names_link = root.find(f".//link[@name='{link_name}']")
            if names_link is not None:
                memberlist = names_link.find("memberlist")
                if memberlist is not None:
                    for member in memberlist.findall("member"):
                        username = member.find("username").text
                        nickname = member.find("nickname").text
                        new_members.append({
                            "username": username,
                            "nickname": nickname
                        })
        except Exception as e:
            logger.error(f"解析新成员信息失败: {e}")
        return new_members
        
    def _clean_content(self, content: str) -> str:
        """清理消息内容，去除群ID前缀"""
        if ":\n<sysmsg" in content:
            return content.split(":\n", 1)[1].strip()
        return content.strip()



    async def _send_welcome_extras(self, group_id: str, nickname: str = ""):
        """发送欢迎附加内容（音乐卡片和表情包）"""
        try:
            # 发送音乐卡片
            try:
                title = "欢迎加入！"
                singer = nickname if nickname else "😃"
                url = "https://weixin.qq.com"
                music_url = "https://sf3-cdn-tos.douyinstatic.com/obj/ies-music/7477140345122294565.mp3"
                cover_url = "http://shp.qpic.cn/collector/3211055935/d3e833ba-bddb-459b-97f9-edc7b3551af8/0"

                await self.dp.sendMusic(
                    title=title,
                    singer=singer,
                    url=url,
                    music_url=music_url,
                    cover_url=cover_url,
                    lyric="",
                    toWxid=group_id,
                    selfWxid=self.dp.self_wxid if hasattr(self.dp, 'self_wxid') else ""
                )
            except Exception as e:
                logger.error(f"[{self.name}] 发送音乐卡片失败: {e}")

            # 发送3次表情包
            try:
                emoji_md5 = "28a4ea054d4192d888067ebd51b442d0"
                emoji_size = 40461
                for i in range(3):
                    await self.dp.sendEmoji(
                        md5=emoji_md5,
                        toWxid=group_id,
                        selfWxid=self.dp.self_wxid if hasattr(self.dp, 'self_wxid') else "",
                        totalLen=emoji_size
                    )
                    if i < 2:  # 最后一次不需要延迟
                        import asyncio
                        await asyncio.sleep(1)
            except Exception as e:
                logger.error(f"[{self.name}] 发送表情包失败: {e}")

        except Exception as e:
            logger.error(f"[{self.name}] 发送欢迎附加内容失败: {e}")

    def _update_user_nickname(self, group_id: str, wxid: str, nickname: str) -> None:
        """更新用户昵称缓存"""
        if not group_id or not wxid or not nickname:
            return

        # 初始化群组的昵称字典（如果不存在）
        if group_id not in self.user_nicknames:
            self.user_nicknames[group_id] = {}

        # 保存昵称
        self.user_nicknames[group_id][wxid] = nickname

    def _get_user_display_name(self, wxid: str, group_id: str = "") -> str:
        """获取用户的显示名称，优先使用群昵称，没有群昵称则使用wxid"""
        try:
            # 如果wxid为空，直接返回"未知用户"
            if not wxid:
                return "未知用户"

            # 特殊情况：群ID为空
            if not group_id:
                # 如果是全局管理员，添加特殊标记
                if wxid in self.global_admins:
                    return f"{wxid[:8]}...(全局管理)"
                return f"{wxid[:8]}..." if len(wxid) > 10 else wxid

            # 从缓存中查找用户昵称
            if group_id in self.user_nicknames and wxid in self.user_nicknames[group_id]:
                nickname = self.user_nicknames[group_id][wxid]
                # 为全局管理员添加标记
                if wxid in self.global_admins:
                    return f"{nickname}(全局管理)"
                return nickname

            # 可以根据微信ID特征进行一些优化显示
            if wxid == "wxid_ubbh6q832tcs21":
                return "郭(全局管理)" if wxid in self.global_admins else "郭"

            if wxid == "wxid_4usgcju5ey9q29":
                return "瑶瑶(机器人)"

            if wxid == "wxid_rwfb9vuy93jn22":
                return "退游了各位"

            # 对于全局管理员，添加标记
            if wxid in self.global_admins:
                # 获取wxid的缩略显示
                short_id = wxid[:8] + "..." if len(wxid) > 10 else wxid
                return f"{short_id}(全局管理)"

            # 简化wxid显示
            if len(wxid) > 10:
                return f"{wxid[:8]}..."
            return wxid
        except Exception as e:
            logger.error(f"[{self.name}] 获取用户显示名称失败: {e}")
            return wxid

    def _format_admin_list(self, admin_list, group_id):
        """将管理员列表格式化为美观的竖排显示"""
        if not admin_list:
            return "⚠️ 当前群无管理员"

        # 将wxid转换为显示名称
        admin_display_names = [self._get_user_display_name(admin, group_id) for admin in admin_list]

        # 格式化为带序号和表情的列表
        formatted_list = []
        for i, name in enumerate(admin_display_names):
            # 根据是否为全局管理员使用不同的图标
            if any(admin in self.global_admins for admin in admin_list if self._get_user_display_name(admin, group_id) == name):
                icon = "👑" # 全局管理员
            else:
                icon = "🔰" # 普通群管理员

            formatted_list.append(f"{i+1}. {icon} {name}")

        # 合并为多行文本
        return "\n".join(formatted_list)

    async def handle_message(self, msg):
        """处理群聊消息（包括系统消息）"""
        if not self.enabled:
            return False

        # 处理系统消息（新成员加入）
        if msg.type == 10002 and msg.from_group():
            return await self._handle_system_message(msg)

        # 只处理文本消息
        if msg.type != 1:
            return False

        content = msg.content.strip()
        sender = msg.sender
        group_id = msg.roomid if msg.from_group() else msg.sender

        # 如果消息包含群名称前缀（格式：群名称:\n内容），去除前缀
        if ":\n" in content:
            sender_nickname, content = content.split(":\n", 1)
            content = content.strip()
            # 保存发送者昵称
            self._update_user_nickname(group_id, sender, sender_nickname)

        # 检查是否是群管理员或全局管理员
        is_admin = self._is_group_admin(group_id, sender)

        # 获取群配置
        group_config = self._get_group_config(group_id)

        # 如果群不在配置中，自动添加
        if group_id not in self.groups:
            self.groups[group_id] = {
                "admins": [],
                "welcome_message": self.default_welcome_message,
                "enable": self.enabled
            }
            # 保存配置以持久化新群设置
            self._save_config()

        # 处理管理员命令
        if is_admin:
            # 开启欢迎
            if content == "开启欢迎":
                self.groups[group_id]["enable"] = True
                if self._save_config():
                    await self.dp.sendText("✅ 开启成功！\n\n🎉 本群欢迎功能已开启", group_id, msg.self_wxid)
                else:
                    await self.dp.sendText("❌ 开启失败，请检查日志", group_id, msg.self_wxid)
                return True

            # 关闭欢迎
            elif content == "关闭欢迎":
                self.groups[group_id]["enable"] = False
                if self._save_config():
                    await self.dp.sendText("✅ 关闭成功！\n\n🔇 本群欢迎功能已关闭", group_id, msg.self_wxid)
                else:
                    await self.dp.sendText("❌ 关闭失败，请检查日志", group_id, msg.self_wxid)
                return True

            # 设置欢迎词
            elif content.startswith("设置欢迎词 "):
                new_message = content[6:].strip()
                if not new_message:
                    await self.dp.sendText("❌ 欢迎词不能为空！\n\n💡 请输入有效的欢迎词", group_id, msg.self_wxid)
                    return True

                # 保存用户输入的原始消息用于显示
                original_message = new_message

                # 自动在欢迎词前面添加@标记
                if not new_message.startswith("@{nickname}"):
                    new_message = f"@{{nickname}} {new_message}"

                self.groups[group_id]["welcome_message"] = new_message
                if self._save_config():
                    await self.dp.sendText(f"✅ 设置成功！\n\n🔹 当前欢迎词：\n「{original_message}」", group_id, msg.self_wxid)
                else:
                    await self.dp.sendText("❌ 设置失败，请检查日志", group_id, msg.self_wxid)
                return True

            # 查看欢迎词
            elif content == "查看欢迎词":
                welcome_msg = group_config["welcome_message"]
                display_msg = welcome_msg.replace("@{nickname}", "").strip()
                await self.dp.sendText(f"📝 当前欢迎词：\n「{display_msg}」", group_id, msg.self_wxid)
                return True

            # 重置欢迎词
            elif content == "重置欢迎词":
                self.groups[group_id]["welcome_message"] = self.default_welcome_message
                if self._save_config():
                    display_msg = self.default_welcome_message.replace("@{nickname}", "").strip()
                    await self.dp.sendText(f"✅ 重置成功！\n\n🔹 当前欢迎词：\n「{display_msg}」", group_id, msg.self_wxid)
                else:
                    await self.dp.sendText("❌ 重置失败，请检查日志", group_id, msg.self_wxid)
                return True

            # 重载欢迎配置
            elif content == "重载欢迎配置":
                if await self.reload_config():
                    await self.dp.sendText("✅ 重载成功！\n\n🔄 欢迎配置已刷新", group_id, msg.self_wxid)
                else:
                    await self.dp.sendText("❌ 重载失败，请检查日志", group_id, msg.self_wxid)
                return True

            # 查看群管理
            elif content == "查看群管理":
                if group_id in self.groups and self.groups[group_id]["admins"]:
                    admin_list = self._format_admin_list(self.groups[group_id]["admins"], group_id)
                    await self.dp.sendText(f"👥 当前群管理员：\n{admin_list}", group_id, msg.self_wxid)
                else:
                    await self.dp.sendText("⚠️ 当前群无欢迎管理员\n💡 仅全局管理员可管理", group_id, msg.self_wxid)
                return True

        return False

    async def _handle_system_message(self, msg):
        """处理系统消息 - 新成员加入"""
        group_id = msg.roomid
        logger.info(f"[{self.name}] 收到系统消息，群ID: {group_id}")

        # 获取群配置
        group_config = self._get_group_config(group_id)
        if not group_config["enable"]:
            logger.info(f"[{self.name}] 群 {group_id} 欢迎功能已关闭")
            return False

        content = msg.content
        if isinstance(content, dict):
            content = content.get("string", "")

        try:
            content = self._clean_content(content)
            logger.debug(f"[{self.name}] 清理后的XML内容: {content[:200]}...")

            root = ET.fromstring(content)
            if root.tag != "sysmsg":
                logger.debug(f"[{self.name}] 不是sysmsg类型: {root.tag}")
                return False

            msg_type = root.attrib.get("type")
            logger.debug(f"[{self.name}] 系统消息类型: {msg_type}")

            if msg_type == "sysmsgtemplate":
                sysmsgtemplate = root.find("sysmsgtemplate")
                if sysmsgtemplate is not None:
                    template = sysmsgtemplate.find("content_template")
                    if template is not None:
                        template_type = template.attrib.get("type")
                        logger.debug(f"[{self.name}] 模板类型: {template_type}")

                        if template_type in ["tmpl_type_profile", "tmpl_type_profilewithrevoke"]:
                            template_text = template.find("template").text
                            logger.info(f"[{self.name}] 模板文本: {template_text}")

                            new_members = []
                            if '"$names$"加入了群聊' in template_text:
                                logger.info(f"[{self.name}] 匹配到：names加入群聊")
                                new_members = self._parse_member_info(root, "names")
                            elif '"$username$"邀请"$names$"加入了群聊' in template_text:
                                logger.info(f"[{self.name}] 匹配到：username邀请names")
                                new_members = self._parse_member_info(root, "names")
                            elif '你邀请"$names$"加入了群聊' in template_text:
                                logger.info(f"[{self.name}] 匹配到：你邀请names")
                                new_members = self._parse_member_info(root, "names")
                            elif '"$adder$"通过扫描"$from$"分享的二维码加入群聊' in template_text:
                                logger.info(f"[{self.name}] 匹配到：adder通过扫描二维码")
                                new_members = self._parse_member_info(root, "adder")
                            elif '"$adder$"通过"$from$"的邀请二维码加入群聊' in template_text:
                                logger.info(f"[{self.name}] 匹配到：adder通过邀请二维码")
                                new_members = self._parse_member_info(root, "adder")
                            else:
                                logger.warning(f"[{self.name}] 未匹配到已知模板: {template_text}")
                                return False

                            logger.info(f"[{self.name}] 解析到新成员数量: {len(new_members)}")
                            if not new_members:
                                logger.warning(f"[{self.name}] 未解析到新成员信息")
                                return False

                            for member in new_members:
                                logger.info(f"[{self.name}] 欢迎新成员: {member['nickname']} ({member['username']})")
                                welcome_msg = group_config["welcome_message"].format(
                                    nickname=member["nickname"]
                                )
                                # 发送欢迎文本消息
                                await self.dp.sendText(welcome_msg, group_id, msg.self_wxid)
                                # 发送欢迎音乐卡片和表情包
                                await self._send_welcome_extras(group_id, member["nickname"])

                            return True

        except ET.ParseError as e:
            logger.error(f"[{self.name}] 解析系统消息XML失败: {e}")
        except Exception as e:
            logger.error(f"[{self.name}] 处理欢迎消息时发生错误: {e}")

        return False

    async def handle_private_message(self, msg):
        """
        处理私聊消息
        复用群聊消息处理逻辑
        """
        return await self.handle_message(msg)
