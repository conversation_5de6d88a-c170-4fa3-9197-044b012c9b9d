from Plugins._Tools import Tools
from Config.logger import logger
from Core.PluginBase import PluginBase
import os
class DailyPointPlugin(PluginBase):
    def __init__(self):
        super().__init__()
        self.name = "DailyPoint"
        self.description = "签到积分"
        self.version = "1.0.0"
        self.author = "大鹏"
        self.tools = Tools()
        self.configData = self.tools.returnConfigData(os.path.dirname(__file__))
        self.checkinword = self.configData.get('checkinword')
        self.yiyanApi = self.configData.get('yiyanApi')

    async def handle_message(self, msg) -> bool:
        """处理消息
        格式1：签到
        示例1：签到
        """

        if msg.type != 1:  # 只处理文本消息
            return False
        if self.tools.judgeEqualWord(msg.content, '签到'):
            # 获取用户信息
            user_info = await self.dp.getIdName(msg.sender)
            wenan = await self.getDPWenan()
            reply = f"@{user_info} \n签到成功✅️\n{wenan}\n开源地址：https://github.com/dpyyds/DPbot"
            await self.dp.sendText(reply, msg.roomid, msg.self_wxid)
            return True




    async def getDPWenan(self):
        """获取一言文案内容"""
        try:
            # 新API直接返回纯文本，无需参数
            textData = await self.tools.async_get(self.yiyanApi, return_json=False)
            if not textData:
                logger.warning('获取一言文案失败: 请求失败')
                return None

            # 如果返回的是字节数据，需要解码为字符串
            if isinstance(textData, bytes):
                wenan = textData.decode('utf-8').strip()
            else:
                wenan = str(textData).strip()

            if not wenan:
                logger.warning('获取一言文案失败: 返回内容为空')
                return None

            logger.info(f'{self.name} 成功获取一言文案: {wenan[:50]}...')
            return wenan
        except Exception as e:
            logger.warning(f'{self.name} 获取一言文案出现错误, 错误信息: {e}')
            return None
