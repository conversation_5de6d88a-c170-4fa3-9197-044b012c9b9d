# ArticleForwarder 公众号文章转发插件

## 功能介绍

ArticleForwarder 是一个用于监控和转发公众号文章的插件。当监控的公众号发布新文章时，插件会自动将文章转发到指定的群聊中。

## 主要功能

- **添加监控**：通过引用公众号文章消息来添加对该公众号的监控
- **删除监控**：通过引用公众号文章消息来删除对该公众号的监控
- **查看配置**：查看当前群聊监控的公众号列表
- **自动转发**：自动将监控公众号的新文章转发到配置的群聊中

## 使用方法

### 1. 配置管理员

在 `config.toml` 文件中配置管理员微信ID：

```toml
admins = [
    "your_wxid_here"
]
```

### 2. 添加监控

1. 在群聊中找到要监控的公众号文章
2. 引用该文章消息
3. 发送 "添加监控"

### 3. 删除监控

1. 在群聊中找到要取消监控的公众号文章
2. 引用该文章消息
3. 发送 "删除监控"

### 4. 查看配置

在群聊中发送 "查看配置" 即可查看当前群聊监控的公众号列表。

## 权限说明

- 只有配置文件中指定的管理员才能执行添加监控、删除监控和查看配置操作
- 普通用户无法修改监控配置

## 注意事项

1. 插件只能监控已经在微信中关注的公众号
2. 需要确保引用的是公众号文章消息（消息类型为5）
3. 转发的文章会以富文本卡片的形式发送
4. 插件会自动保存配置，重启后配置不会丢失

## 技术说明

- 基于DPbot插件架构开发
- 使用XML解析来提取公众号和文章信息
- 支持多群转发同一公众号的文章
- 配置数据存储在 `data/config.json` 文件中
- 正确处理引用消息的XML格式和转义字符
- 支持从多种XML节点提取公众号信息（sourceusername、sourcedisplayname等）

## 消息格式支持

插件支持以下消息格式：

1. **直接公众号文章消息**：消息类型为49，包含公众号文章的完整XML结构
2. **引用公众号文章消息**：消息类型为49，包含refermsg节点，引用原始公众号文章
3. **转义字符处理**：自动处理XML中的转义字符（&amp;、&lt;、&gt;、&#x20;等）

## 更新日志

### v1.0.0
- 初始版本
- 支持添加/删除监控、查看配置
- 自动转发公众号文章
- 移除播客功能，专注核心转发功能
- 优化XML解析，支持复杂的引用消息格式
