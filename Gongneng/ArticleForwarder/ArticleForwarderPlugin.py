from Plugins._Tools import Tools
from Plugins._Tools.ReferenceMessageHandler import ReferenceMessageHandler
from Plugins._Tools.ReferenceMessageErrorHandler import ReferenceMessageErrorHandler
from Config.logger import logger
from Core.PluginBase import PluginBase
import os
import json
import xml.etree.ElementTree as ET
import html
import re
from pathlib import Path

class ArticleForwarderPlugin(PluginBase, ReferenceMessageHandler):
    def __init__(self):
        super().__init__()
        self.name = "ArticleForwarder"
        self.description = "公众号文章转发插件"
        self.version = "1.0.0"
        self.author = "Assistant"
        self.tools = Tools()

        # 加载配置
        self.configData = self.tools.returnConfigData(os.path.dirname(__file__))
        self.admins = self.configData.get('admins', [])

        # 数据目录和监控配置
        self.data_dir = Path(os.path.dirname(__file__)) / "data"
        self.data_dir.mkdir(exist_ok=True)
        self.accounts = {}
        self._load_config()

    def _load_config(self):
        """加载转发配置"""
        config_file = self.data_dir / "config.json"
        try:
            if config_file.exists():
                with open(config_file, "r", encoding="utf-8") as f:
                    data = json.load(f)
                    old_accounts = data.get("accounts", {})

                    # 转换配置格式
                    for account_id, value in old_accounts.items():
                        if isinstance(value, list):  # 旧格式
                            self.accounts[account_id] = {"name": account_id, "groups": value}
                        elif isinstance(value, dict):  # 新格式
                            self.accounts[account_id] = {
                                "name": value.get("name", account_id),
                                "groups": value.get("groups", [])
                            }
            else:
                self._save_config()
        except Exception as e:
            logger.error(f"加载转发配置出错: {e}")
            self.accounts = {}

    def _save_config(self):
        """保存配置"""
        config_file = self.data_dir / "config.json"
        with open(config_file, "w", encoding="utf-8") as f:
            json.dump({"accounts": self.accounts}, f, ensure_ascii=False, indent=2)

    def _check_is_admin(self, sender_wxid: str) -> bool:
        """检查用户是否是管理员"""
        # 优先使用插件配置的管理员列表
        if self.admins and sender_wxid in self.admins:
            return True

        # 使用系统管理员列表
        try:
            import Config.ConfigServer as ConfigServer
            config_data = ConfigServer.returnConfigData()
            system_admins = config_data.get('DPBotConfig', {}).get('Administrators', [])
            return sender_wxid in system_admins
        except Exception:
            return False

    def _parse_xml(self, xml_content: str):
        """解析XML内容，返回根节点"""
        # 处理转义字符和HTML实体
        xml_content = html.unescape(xml_content.replace('&amp;', '&').replace('&lt;', '<').replace('&gt;', '>'))

        try:
            return ET.fromstring(xml_content)
        except ET.ParseError:
            # 尝试提取XML部分
            if '<?xml' in xml_content:
                xml_start = xml_content.find('<?xml')
                if xml_start != -1:
                    try:
                        return ET.fromstring(xml_content[xml_start:])
                    except ET.ParseError:
                        pass
            return None

    def _extract_account_info(self, xml_content: str) -> tuple[str, str]:
        """从XML中提取公众号ID和名称"""
        try:
            root = self._parse_xml(xml_content)
            if root is None:
                return "", ""

            appmsg = root.find(".//appmsg")
            if appmsg is None:
                return "", ""

            # 提取ID和名称
            account_id = ""
            account_name = ""

            # 优先使用sourceusername作为ID
            sourceusername = appmsg.find("sourceusername")
            if sourceusername is not None and sourceusername.text:
                account_id = sourceusername.text

            # 使用sourcedisplayname作为名称
            sourcedisplayname = appmsg.find("sourcedisplayname")
            if sourcedisplayname is not None and sourcedisplayname.text:
                account_name = sourcedisplayname.text

            # 备选方案：从publisher节点获取
            if not account_id:
                publisher = root.find(".//publisher")
                if publisher is not None:
                    username = publisher.find("username")
                    if username is not None and username.text:
                        account_id = username.text

                    if not account_name:
                        nickname = publisher.find("nickname")
                        if nickname is not None and nickname.text:
                            account_name = nickname.text

            # 如果没有名称，使用ID
            if not account_name and account_id:
                account_name = account_id

            return account_id, account_name

        except Exception as e:
            logger.error(f"提取公众号信息失败: {e}")
            return "", ""

    def _extract_article_info(self, xml_content: str) -> dict:
        """从XML中提取文章信息"""
        try:
            root = self._parse_xml(xml_content)
            if root is None:
                return {}

            appmsg = root.find(".//appmsg")
            if appmsg is None:
                return {}

            # 提取标题
            title_node = appmsg.find(".//title")
            title = title_node.text if title_node is not None and title_node.text else "无标题"

            # 提取URL
            url = None
            webviewshared = appmsg.find(".//webviewshared")
            if webviewshared is not None:
                for url_tag in ["shareUrlOpen", "shareUrlOriginal"]:
                    url_node = webviewshared.find(url_tag)
                    if url_node is not None and url_node.text:
                        url = url_node.text
                        break

            if not url:
                url_node = appmsg.find(".//url")
                url = url_node.text if url_node is not None and url_node.text else None

            # 提取缩略图
            thumb_url = None
            for thumb_tag in ["thumburl", "cover"]:
                thumb_node = appmsg.find(f".//{thumb_tag}")
                if thumb_node is not None and thumb_node.text:
                    thumb_url = thumb_node.text
                    break

            # 提取摘要
            summary = ""
            des_node = appmsg.find(".//des")
            if des_node is not None and des_node.text:
                summary = des_node.text
            else:
                # 从mmreader获取摘要
                summary_node = appmsg.find(".//mmreader/category/item/summary")
                if summary_node is not None and summary_node.text:
                    summary = summary_node.text

            return {
                "title": title,
                "url": url,
                "thumb_url": thumb_url,
                "summary": summary
            }

        except Exception as e:
            logger.error(f"提取文章信息失败: {e}")
            return {}

    async def handle_message(self, msg) -> bool:
        """处理消息"""
        try:
            # 处理文本消息（管理命令）
            if msg.type == 1:
                return await self._handle_text_message(msg)

            # 处理引用消息和公众号文章消息
            elif msg.type == 49:
                # 先尝试处理引用消息
                if await self._handle_quote_message(msg):
                    return True
                # 如果不是引用消息，尝试处理公众号文章消息
                return await self._handle_article_message(msg)

        except Exception as e:
            logger.error(f"ArticleForwarder处理消息失败: {e}")

        return False

    async def _handle_text_message(self, msg) -> bool:
        """处理文本消息"""
        if not msg.from_group() or msg.content.strip() != "查看配置":
            return False

        # 检查权限
        if not self._check_is_admin(msg.sender):
            await self.dp.sendText("⚠️ 抱歉，只有管理员才能操作文章转发配置", msg.roomid, msg.self_wxid)
            return True

        # 显示配置
        await self._show_config(msg)
        return True

    async def _show_config(self, msg):
        """显示配置信息"""
        if not self.accounts:
            await self.dp.sendText("📊 当前没有监控任何公众号", msg.roomid, msg.self_wxid)
            return

        # 统计当前群监控的公众号
        monitoring = [info['name'] for info in self.accounts.values() if msg.roomid in info["groups"]]

        # 生成配置信息
        if monitoring:
            response = "📊 当前群监控的公众号\n\n" + "\n".join([f"✦ {name}" for name in monitoring])
            response += f"\n\n共监控 {len(monitoring)} 个公众号"
        else:
            response = "📊 当前群监控的公众号\n\n暂未监控任何公众号"

        await self.dp.sendText(response, msg.roomid, msg.self_wxid)

    async def _handle_quote_message(self, msg) -> bool:
        """处理引用消息（添加/删除监控）"""
        if not msg.from_group():
            return False

        content = msg.content.strip()

        # 使用标准方法检查是否是引用消息
        if not self._is_reference_message(content):
            return False

        # 检查是否是监控命令
        command = None
        if "添加监控" in content:
            command = "添加监控"
        elif "删除监控" in content:
            command = "删除监控"
        else:
            return False

        # 检查权限
        if not self._check_is_admin(msg.sender):
            await ReferenceMessageErrorHandler.handle_permission_error(self.dp, msg.roomid, msg.self_wxid)
            return True

        # 使用标准方法获取引用内容
        try:
            refer_content = self._get_reference_content(content)
            if not refer_content:
                await ReferenceMessageErrorHandler.handle_reference_content_error(
                    self.dp, msg.roomid, msg.self_wxid, "无法获取引用消息内容"
                )
                return True

            # 解析引用的消息内容
            try:
                refer_root = self._parse_xml(refer_content)
                if refer_root is None:
                    refer_root = ET.fromstring(f"<root>{refer_content}</root>")
            except ET.ParseError:
                # 使用正则表达式提取作为备用方案
                account_id_match = re.search(r'<sourceusername>(.*?)</sourceusername>', refer_content)
                account_name_match = re.search(r'<sourcedisplayname>(.*?)</sourcedisplayname>', refer_content)

                if account_id_match and account_name_match:
                    account_id = account_id_match.group(1)
                    account_name = account_name_match.group(1)
                    await self._execute_monitor_command(command, msg, account_id, account_name)
                    return True
                else:
                    await ReferenceMessageErrorHandler.handle_xml_parse_error(
                        self.dp, msg.roomid, msg.self_wxid
                    )
                    return True

            # 检查是否是公众号文章
            refer_appmsg = refer_root.find(".//appmsg")
            if refer_appmsg is None:
                await ReferenceMessageErrorHandler.handle_reference_content_error(
                    self.dp, msg.roomid, msg.self_wxid, "引用消息格式错误"
                )
                return True

            refer_msg_type = refer_appmsg.find("type")
            if refer_msg_type is None or refer_msg_type.text != "5":
                await ReferenceMessageErrorHandler.handle_invalid_reference_type(
                    self.dp, msg.roomid, msg.self_wxid, "公众号文章"
                )
                return True

            # 提取公众号信息并执行命令
            account_id, account_name = self._extract_account_info(refer_content)
            if account_id:
                await self._execute_monitor_command(command, msg, account_id, account_name)
            else:
                await ReferenceMessageErrorHandler.handle_reference_content_error(
                    self.dp, msg.roomid, msg.self_wxid, "无法获取公众号ID，请确保引用的是公众号文章"
                )
            return True

        except Exception as e:
            logger.error(f"处理引用消息失败: {e}")
            await ReferenceMessageErrorHandler.handle_processing_error(
                self.dp, msg.roomid, msg.self_wxid, "引用消息处理"
            )
            return True

    async def _execute_monitor_command(self, command: str, msg, account_id: str, account_name: str):
        """执行监控命令"""
        if command == "添加监控":
            await self._add_monitor(msg, account_id, account_name)
        else:  # 删除监控
            await self._remove_monitor(msg, account_id, account_name)

    async def _add_monitor(self, msg, account_id: str, account_name: str):
        """添加监控"""
        if account_id in self.accounts:
            if msg.roomid in self.accounts[account_id]["groups"]:
                await self.dp.sendText(f"此群已经在公众号「{account_name}」的转发列表中了哦 😊", msg.roomid, msg.self_wxid)
                return
            self.accounts[account_id]["groups"].append(msg.roomid)
        else:
            self.accounts[account_id] = {"name": account_name, "groups": [msg.roomid]}

        self._save_config()
        await self.dp.sendText(f"✅ 已添加公众号「{account_name}」的监控\n此群将接收该公众号的新文章", msg.roomid, msg.self_wxid)

    async def _remove_monitor(self, msg, account_id: str, account_name: str):
        """删除监控"""
        if account_id not in self.accounts:
            await self.dp.sendText(f"公众号「{account_name}」不在监控列表中哦 😊", msg.roomid, msg.self_wxid)
            return

        if msg.roomid not in self.accounts[account_id]["groups"]:
            await self.dp.sendText(f"此群没有监控公众号「{account_name}」哦 😊", msg.roomid, msg.self_wxid)
            return

        self.accounts[account_id]["groups"].remove(msg.roomid)

        # 如果转发列表为空，删除该公众号的配置
        if not self.accounts[account_id]["groups"]:
            del self.accounts[account_id]

        self._save_config()
        await self.dp.sendText(f"✅ 已取消公众号「{account_name}」的监控", msg.roomid, msg.self_wxid)

    async def _handle_article_message(self, msg) -> bool:
        """处理公众号文章消息（转发）"""
        try:
            # 只处理直接来自公众号的消息
            if not msg.from_user_name.startswith("gh_"):
                return False

            account_id = msg.from_user_name

            # 检查是否在监控列表中
            if account_id not in self.accounts:
                return False

            # 检查是否是XML格式的消息
            if not msg.content or not msg.content.startswith("<msg>"):
                return False

            # 检查是否是公众号文章
            try:
                root = ET.fromstring(msg.content)
                appmsg = root.find(".//appmsg")
                if appmsg is None:
                    return False

                msg_type = appmsg.find("type")
                if msg_type is None or msg_type.text != "5":  # 5是公众号文章类型
                    return False
            except ET.ParseError:
                # 简单检查是否包含公众号文章标识
                if not ("<msg>" in msg.content and "<appmsg>" in msg.content and "<type>5</type>" in msg.content):
                    return False

            # 提取文章信息
            article_info = self._extract_article_info(msg.content)
            if not article_info.get("url"):
                return False

            # 转发到目标群
            target_groups = self.accounts[account_id]["groups"]
            success_count = 0

            for group_id in target_groups:
                try:
                    await self.dp.sendRich(
                        title=article_info["title"],
                        description=article_info["summary"][:100] + "..." if article_info["summary"] else "",
                        url=article_info["url"],
                        thumb_url=article_info["thumb_url"] or "",
                        toWxid=group_id,
                        selfWxid=msg.self_wxid
                    )
                    success_count += 1
                except Exception as e:
                    logger.error(f"转发文章到群 {group_id} 失败: {e}")

            logger.info(f"文章转发完成，成功: {success_count}/{len(target_groups)} 个群")
            return True

        except Exception as e:
            logger.error(f"处理文章消息失败: {e}")
            return False
