from Plugins._Tools import Tools
from Config.logger import logger
import Config.ConfigServer as Cs
from Core.PluginBase import PluginBase
import os
import asyncio

class AdminPlugin(PluginBase):
    def __init__(self):
        super().__init__()
        self.name = "Admin"
        self.description = "管理员管理插件"
        self.version = "1.0.0"
        self.author = "大鹏"
        self.tools = Tools()  
        
              
        self.configData = self.tools.returnConfigData(os.path.dirname(__file__))
        self.adminConfig = self.configData.get('AdminConfig')
        self.DPBotConfig = Cs.returnConfigData().get('DPBotConfig')
        self.Administrators = self.DPBotConfig.get('Administrators')
        self.enablePlugin = self.configData.get('AdminPlugin').get('enablePlugin')
        self.unablePlugin = self.configData.get('AdminPlugin').get('unablePlugin')
        self.menuPlugin = self.configData.get('AdminPlugin').get('menuPlugin')
        self.restartPlugin = self.configData.get('AdminPlugin').get('restartPlugin')
        self.addPlugin = self.configData.get('AdminPlugin').get('addPlugin')
        self.delPlugin = self.configData.get('AdminPlugin').get('delPlugin')
        self.initPlugin = self.configData.get('AdminPlugin').get('initPlugin')
        self.reloadPlugin = self.configData.get('AdminPlugin').get('reloadPlugin')
        self.loadPlugin = self.configData.get('AdminPlugin').get('loadPlugin')
        self.unloadPlugin = self.configData.get('AdminPlugin').get('unloadPlugin')
        self.listPlugin = self.configData.get('AdminPlugin').get('listPlugin')
        self.pluginInfo = self.configData.get('AdminPlugin').get('pluginInfo')
        self.fileWatcher = self.configData.get('AdminPlugin').get('fileWatcher')
        self.smartLoadPlugin = self.configData.get('AdminPlugin').get('smartLoadPlugin')
    async def _handle_admin_change(self, msg, is_add=True):
        """处理管理员添加或删除"""
        if not msg.atusers:
            await self.dp.sendText(f"请@要{'添加' if is_add else '删除'}的管理员", msg.sender, msg.self_wxid)
            return True
            
        for user in msg.atusers:
            try:
                is_admin = await self.tools.query_admin(msg.roomid, user)
                if is_add and is_admin:
                    await self.dp.sendText("该用户已经是管理员", msg.sender, msg.self_wxid)
                    return True
                if not is_add and not is_admin:
                    await self.dp.sendText("该用户不是管理员", msg.sender, msg.self_wxid)
                    return True
                    
                if is_add:
                    await self.tools.add_admin(msg.roomid, user)
                else:
                    await self.tools.del_admin(msg.roomid, user)
            except Exception as e:
                logger.error(f"{'添加' if is_add else '删除'}管理员 {user} 失败: {e}")
                await self.dp.sendText(f"{'添加' if is_add else '删除'}管理员失败", msg.sender, msg.self_wxid)
                return True
                
        await self.dp.sendText(f"{'添加' if is_add else '删除'}管理员成功", msg.sender, msg.self_wxid)
        return True

    async def _handle_mode_change(self, msg, mode=None):
        """处理群组模式设置或移除"""
        current_mode = await self.tools.query_group_mode(msg.roomid)
        
        # 移除模式
        if mode is None:
            if not current_mode:
                await self.dp.sendText("群组当前没有设置模式", msg.sender, msg.self_wxid)
                return True
            try:
                await self.tools.delete_group_mode(msg.roomid)
                await self.dp.sendText(f"成功移除群组{current_mode[0]}模式", msg.sender, msg.self_wxid)
            except Exception as e:
                logger.error(f"移除群组{current_mode[0]}模式失败: {e}")
                await self.dp.sendText(f"移除群组{current_mode[0]}模式失败", msg.sender, msg.self_wxid)
            return True
            
        # 设置模式
        if current_mode and current_mode[0] in ["admin", "custom"]:
            logger.debug(f"群组已经是{current_mode[0]}模式")
            await self.dp.sendText(f"群组已经是{current_mode[0]}模式", msg.sender, msg.self_wxid)
            return True
            
        try:
            await self.tools.set_group_mode(msg.roomid, mode)
            await self.dp.sendText(f"设置群组为{mode}模式成功", msg.sender, msg.self_wxid)
        except Exception as e:
            logger.error(f"设置群组{mode}模式失败: {e}")
            await self.dp.sendText(f"设置群组{mode}模式失败", msg.sender, msg.self_wxid)
        return True

    async def _handle_plugin_change(self, msg, operation: str):
        """处理插件变更
        Args:
            msg: 消息对象
            operation: 操作类型，可选值：'add', 'delete', 'enable', 'disable', 'query'
        """
        # 检查群组模式
        current_mode = await self.tools.query_group_mode(msg.roomid)
        logger.debug(f"当前群组模式:{current_mode[0]}")
        if current_mode[0] not in ["custom", "admin"]:
            await self.dp.sendText("当前群组模式不支持插件管理", msg.sender, msg.self_wxid)
            return False
        
        # 解析插件名称（去掉命令前缀）
        plugin_name = msg.content.split(' ', 1)[1] if ' ' in msg.content else None
        if not plugin_name and operation != 'query':
            await self.dp.sendText("请指定插件名称", msg.sender, msg.self_wxid)
            return True

        try:
            if operation == 'add':
                # 添加插件（默认禁用状态）
                success = await self.tools.set_plugin_config(current_mode[0], plugin_name, False)
                if success:
                    await self.dp.sendText(f"添加插件 {plugin_name} 成功", msg.sender, msg.self_wxid)
                else:
                    await self.dp.sendText(f"添加插件 {plugin_name} 失败", msg.sender, msg.self_wxid)

            elif operation == 'delete':
                # 删除插件配置
                success = await self.tools.delete_plugin_config(current_mode[0], plugin_name)
                if success:
                    await self.dp.sendText(f"删除插件 {plugin_name} 成功", msg.sender, msg.self_wxid)
                else:
                    await self.dp.sendText(f"删除插件 {plugin_name} 失败", msg.sender, msg.self_wxid)

            elif operation == 'enable':
                # 启用插件
                success = await self.tools.set_plugin_config(current_mode[0], plugin_name, True)
                if success:
                    await self.dp.sendText(f"启用插件 {plugin_name} 成功", msg.sender, msg.self_wxid)
                else:
                    await self.dp.sendText(f"启用插件 {plugin_name} 失败", msg.sender, msg.self_wxid)

            elif operation == 'disable':
                # 禁用插件
                success = await self.tools.set_plugin_config(current_mode[0], plugin_name, False)
                if success:
                    await self.dp.sendText(f"禁用插件 {plugin_name} 成功", msg.sender, msg.self_wxid)
                else:
                    await self.dp.sendText(f"禁用插件 {plugin_name} 失败", msg.sender, msg.self_wxid)

            elif operation == 'query':
                # 查询所有插件状态
                enabled_plugins = await self.tools.get_enabled_plugins(current_mode[0])
                all_plugins = await self.tools.list_plugin_configs()
                
                if not all_plugins:
                    await self.dp.sendText("当前没有配置任何插件", msg.sender, msg.self_wxid)
                    return True
                
                # 构建插件状态消息
                status_msg = "插件状态列表：\n"
                for plugin in all_plugins:
                    status = "✅ 已启用" if plugin in enabled_plugins else "❌ 已禁用"
                    status_msg += f"{plugin}: {status}\n"
                
                await self.dp.sendText(status_msg, msg.sender, msg.self_wxid)

        except Exception as e:
            logger.error(f"处理插件{operation}操作出错: {e}")
            await self.dp.sendText(f"处理插件操作失败", msg.sender, msg.self_wxid)
        
        return True

    async def _handle_plugin_hotswap(self, msg, operation: str):
        """处理插件热插拔操作"""
        try:
            # 获取插件管理器
            plugin_manager = self._message_handler.plugin_manager if self._message_handler else None
            if not plugin_manager:
                await self.dp.sendText("插件管理器不可用", msg.sender, msg.self_wxid)
                return True

            if operation == 'list':
                # 列出所有已加载的插件
                plugins = plugin_manager.list_loaded_plugins()
                if not plugins:
                    await self.dp.sendText("当前没有加载任何插件", msg.sender, msg.self_wxid)
                    return True

                msg_text = "📋 已加载插件列表：\n"
                for name, version in plugins.items():
                    msg_text += f"• {name} v{version}\n"

                # 添加文件监控状态
                watcher_status = "🟢 已启用" if plugin_manager.is_file_watcher_enabled() else "🔴 已禁用"
                msg_text += f"\n📁 文件监控: {watcher_status}"

                await self.dp.sendText(msg_text, msg.sender, msg.self_wxid)
                return True

            elif operation == 'watcher':
                # 文件监控控制
                parts = msg.content.split(' ', 2)
                if len(parts) < 2:
                    status = "🟢 已启用" if plugin_manager.is_file_watcher_enabled() else "🔴 已禁用"
                    await self.dp.sendText(f"📁 文件监控状态: {status}\n\n使用方法：\n文件监控 启动 - 启动文件监控\n文件监控 停止 - 停止文件监控", msg.sender, msg.self_wxid)
                    return True

                action = parts[1].strip()
                if action in ['启动', 'start', '开启']:
                    if plugin_manager.start_file_watcher(auto_reload=True):
                        await self.dp.sendText("✅ 文件监控已启动，将自动重载修改的插件", msg.sender, msg.self_wxid)
                    else:
                        await self.dp.sendText("❌ 文件监控启动失败，请检查是否安装了watchdog库", msg.sender, msg.self_wxid)
                elif action in ['停止', 'stop', '关闭']:
                    if plugin_manager.stop_file_watcher():
                        await self.dp.sendText("✅ 文件监控已停止", msg.sender, msg.self_wxid)
                    else:
                        await self.dp.sendText("❌ 文件监控停止失败", msg.sender, msg.self_wxid)
                else:
                    await self.dp.sendText("❌ 无效的操作，请使用：启动 或 停止", msg.sender, msg.self_wxid)
                return True

            elif operation == 'smart_load':
                # 智能加载插件
                await self.dp.sendText("🔍 正在检测插件变化，请稍候...", msg.sender, msg.self_wxid)

                results = await plugin_manager.smart_load_plugins()

                if "error" in results:
                    await self.dp.sendText(f"❌ 智能加载失败: {results['error']}", msg.sender, msg.self_wxid)
                    return True

                # 生成详细报告
                report_lines = ["🤖 智能插件加载完成！\n"]

                if results["new_loaded"]:
                    report_lines.append(f"✅ 新加载插件 ({len(results['new_loaded'])}):")
                    for plugin in results["new_loaded"]:
                        report_lines.append(f"  • {plugin}")
                    report_lines.append("")

                if results["reloaded"]:
                    report_lines.append(f"🔄 重新加载插件 ({len(results['reloaded'])}):")
                    for plugin in results["reloaded"]:
                        report_lines.append(f"  • {plugin}")
                    report_lines.append("")

                if results["failed"]:
                    report_lines.append(f"❌ 加载失败插件 ({len(results['failed'])}):")
                    for plugin in results["failed"]:
                        report_lines.append(f"  • {plugin}")
                    report_lines.append("")

                if results["unchanged"]:
                    report_lines.append(f"⚪ 无变化插件 ({len(results['unchanged'])}):")
                    for plugin in results["unchanged"]:
                        report_lines.append(f"  • {plugin}")

                # 如果没有任何操作
                if not any([results["new_loaded"], results["reloaded"], results["failed"]]):
                    report_lines = ["ℹ️ 未发现需要加载或重载的插件"]

                report_text = "\n".join(report_lines)

                # 如果报告太长，分段发送
                if len(report_text) > 800:
                    # 分段发送
                    parts = []
                    current_part = ""

                    for line in report_lines:
                        if len(current_part + line + "\n") > 800:
                            if current_part:
                                parts.append(current_part.strip())
                            current_part = line + "\n"
                        else:
                            current_part += line + "\n"

                    if current_part:
                        parts.append(current_part.strip())

                    for i, part in enumerate(parts):
                        if i == 0:
                            await self.dp.sendText(part, msg.sender, msg.self_wxid)
                        else:
                            await self.dp.sendText(f"📋 续：\n{part}", msg.sender, msg.self_wxid)
                        if i < len(parts) - 1:
                            await asyncio.sleep(1)  # 避免发送过快
                else:
                    await self.dp.sendText(report_text, msg.sender, msg.self_wxid)

                return True

            # 需要插件名称的操作
            parts = msg.content.split(' ', 2)
            if len(parts) < 2:
                await self.dp.sendText(f"❌ 请指定插件名称，例如：{operation} PluginName", msg.sender, msg.self_wxid)
                return True

            plugin_name = parts[1].strip()

            if operation == 'reload':
                # 重载插件
                if await plugin_manager.reload_plugin(plugin_name):
                    await self.dp.sendText(f"✅ 插件 {plugin_name} 重载成功", msg.sender, msg.self_wxid)
                else:
                    await self.dp.sendText(f"❌ 插件 {plugin_name} 重载失败", msg.sender, msg.self_wxid)

            elif operation == 'load':
                # 加载新插件
                if await plugin_manager.load_new_plugin(plugin_name):
                    await self.dp.sendText(f"✅ 插件 {plugin_name} 加载成功", msg.sender, msg.self_wxid)
                else:
                    await self.dp.sendText(f"❌ 插件 {plugin_name} 加载失败", msg.sender, msg.self_wxid)

            elif operation == 'unload':
                # 卸载插件
                if await plugin_manager.unload_plugin(plugin_name):
                    await self.dp.sendText(f"✅ 插件 {plugin_name} 卸载成功", msg.sender, msg.self_wxid)
                else:
                    await self.dp.sendText(f"❌ 插件 {plugin_name} 卸载失败", msg.sender, msg.self_wxid)

            elif operation == 'info':
                # 插件信息
                info = plugin_manager.get_plugin_info(plugin_name)
                if not info:
                    await self.dp.sendText(f"❌ 插件 {plugin_name} 不存在", msg.sender, msg.self_wxid)
                    return True

                msg_text = f"📋 插件信息：\n"
                msg_text += f"名称: {info['name']}\n"
                msg_text += f"版本: {info['version']}\n"
                msg_text += f"作者: {info['author']}\n"
                msg_text += f"描述: {info['description']}\n"
                msg_text += f"模块: {info['module']}\n"
                msg_text += f"类名: {info['class']}"

                await self.dp.sendText(msg_text, msg.sender, msg.self_wxid)

            return True

        except Exception as e:
            logger.error(f"处理插件热插拔操作失败: {e}", exc_info=True)
            await self.dp.sendText(f"❌ 操作失败: {str(e)}", msg.sender, msg.self_wxid)
            return True

    async def plugin_init(self):
        """插件初始化，从 Excel 读取插件配置并写入数据库"""
        try:
            import pandas as pd
            from DbServer.DbAdminServer import DbAdminServer

            # 初始化数据库服务
            db_server = DbAdminServer()
            
            # 读取 Excel 文件
            excel_path = "Config/plugininit.xlsx"
            if not os.path.exists(excel_path):
                logger.error(f"插件配置文件不存在: {excel_path}")
                return
                
            df = pd.read_excel(excel_path)
            
            # 遍历每一行数据
            for _, row in df.iterrows():
                plugin_name = row['name']
                # 分别设置三种模式的配置
                # admin 模式 (群组模式, mode=1)
                await db_server.set_plugin_config('admin', plugin_name, bool(int(row['admin'])))
                # custom 模式 (自定义模式, mode=2)
                await db_server.set_plugin_config('custom', plugin_name, bool(int(row['custom'])))
                # private 模式 (私聊模式, mode=3)
                await db_server.set_plugin_config('private', plugin_name, bool(int(row['private'])))
                
            logger.success("插件配置初始化完成")
            
        except Exception as e:
            logger.error(f"插件初始化失败: {e}")

    async def should_handle_message(self, msg) -> bool:
        """判断是否应该处理消息
        Args:
            msg: 消息对象
        Returns:
            bool: 是否为管理员
        """
        # 检查是否为配置文件中的管理员
        if msg.sender in self.Administrators:
            return True
            
        # 检查是否为数据库中的管理员
        is_admin = await self.tools.query_admin(msg.roomid, msg.sender)
        return is_admin

    async def handle_admin_message(self, msg) -> bool:
        """处理管理员消息，这个方法会被优先调用，不受插件配置影响
        Args:
            msg: 消息对象
        Returns:
            bool: 是否处理成功
        """
        try:
            # 处理插件管理命令
            if msg.content == "测试":
                return await self.dp.sendImage("https://p26-sign.douyinpic.com/tos-cn-i-0813c001/oMAxt78xDQBsSfybAiFADo9PFCfUoAAIEUglAP~tplv-dy-lqen-new:1920:1440:q80.webp?lk3s=138a59ce&x-expires=**********&x-signature=ebvrnCI%2Fy6Bp32BpZDLzP2%2Bw2Xo%3D&from=327834062&s=PackSourceEnum_DOUYIN_REFLOW&se=false&sc=image&biz_tag=aweme_images&l=20250702173333E9A34C2182C106384D8A",msg.roomid,msg.self_wxid)
            if self.tools.judgeSplitAllEqualWord(msg.content, self.addPlugin):
                return await self._handle_plugin_change(msg, 'add')
            
            elif self.tools.judgeSplitAllEqualWord(msg.content, self.delPlugin):
                return await self._handle_plugin_change(msg, 'delete')
            
            elif self.tools.judgeSplitAllEqualWord(msg.content, self.enablePlugin):
                return await self._handle_plugin_change(msg, 'enable')
            
            elif self.tools.judgeSplitAllEqualWord(msg.content, self.unablePlugin):
                return await self._handle_plugin_change(msg, 'disable')
            
            elif self.tools.judgeEqualListWord(msg.content, self.menuPlugin):
                return await self._handle_plugin_change(msg, 'query')

            # 添加管理员
            if self.tools.judgeEqualListWord(msg.noAtMsg, self.adminConfig.get('addAdminSymbol')):
                return await self._handle_admin_change(msg, is_add=True)
            
            # 删除管理员
            elif self.tools.judgeEqualListWord(msg.noAtMsg, self.adminConfig.get('delAdminSymbol')):
                return await self._handle_admin_change(msg, is_add=False)
            
            # 设置群组模式
            elif self.tools.judgeEqualListWord(msg.content, self.adminConfig.get('setGroupAdminModeSymbol')):
                return await self._handle_mode_change(msg, "admin")
            
            elif self.tools.judgeEqualListWord(msg.content, self.adminConfig.get('setGroupCustomModeSymbol')):
                return await self._handle_mode_change(msg, "custom")
            
            # 移除群组模式
            elif self.tools.judgeEqualListWord(msg.content, self.adminConfig.get('delGroupmModeSymbol')):
                return await self._handle_mode_change(msg)
            
            # 重启插件
            elif self.tools.judgeEqualListWord(msg.content, self.restartPlugin):
                return await self._handle_plugin_restart(msg)

            # 重载插件
            elif self.tools.judgeSplitAllEqualWord(msg.content, self.reloadPlugin):
                return await self._handle_plugin_hotswap(msg, 'reload')

            # 加载新插件
            elif self.tools.judgeSplitAllEqualWord(msg.content, self.loadPlugin):
                return await self._handle_plugin_hotswap(msg, 'load')

            # 智能加载插件
            elif self.tools.judgeEqualListWord(msg.content, self.smartLoadPlugin):
                return await self._handle_plugin_hotswap(msg, 'smart_load')

            # 卸载插件
            elif self.tools.judgeSplitAllEqualWord(msg.content, self.unloadPlugin):
                return await self._handle_plugin_hotswap(msg, 'unload')

            # 插件列表
            elif self.tools.judgeEqualListWord(msg.content, self.listPlugin):
                return await self._handle_plugin_hotswap(msg, 'list')

            # 插件信息
            elif self.tools.judgeSplitAllEqualWord(msg.content, self.pluginInfo):
                return await self._handle_plugin_hotswap(msg, 'info')

            # 文件监控
            elif self.tools.judgeSplitAllEqualWord(msg.content, self.fileWatcher):
                return await self._handle_plugin_hotswap(msg, 'watcher')

            # 插件初始化
            elif self.tools.judgeEqualListWord(msg.content, self.initPlugin):
                return await self.plugin_init()

            return False
            
        except Exception as e:
            logger.error(f"处理管理员消息出错: {e}")
            return False

    async def handle_message(self, msg) -> bool:
        """处理普通消息，这个方法会受到插件配置的影响
        Args:
            msg: 消息对象
        Returns:
            bool: 是否处理成功
        """
        # 管理员插件的普通消息处理逻辑
        # 由于核心功能都在 handle_admin_message 中，这里可以直接返回 False
        return False


    async def handle_private_message(self, msg) -> bool:
        """处理私聊消息
        Args:
            msg: 消息对象
        Returns:
            bool: 是否处理成功
        """
        