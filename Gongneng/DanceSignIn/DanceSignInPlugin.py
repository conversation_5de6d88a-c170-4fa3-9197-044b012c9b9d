from Plugins._Tools import Tools
from Core.PluginBase import PluginBase
import os
import time

class DanceSignInPlugin(PluginBase):
    def __init__(self):
        super().__init__()
        # 基本信息（重要：name必须与目录名一致）
        self.name = "DanceSignIn"
        self.description = "唱舞全明星签到插件"
        self.version = "1.0.0"
        self.author = "移植自XYBot"

        # 工具类实例
        self.tools = Tools()

        # 加载配置文件
        self.configData = self.tools.returnConfigData(os.path.dirname(__file__))
        self._load_config()

        # 用户冷却时间记录
        self.user_last_request = {}

    def _load_config(self):
        """加载配置"""
        self.enabled = self.configData.get('enabled', True)
        self.commands = self.configData.get('commands', ["唱舞签到"])
        self.cooldown = self.configData.get('cooldown', 5)
        self.signin_link = self.configData.get('signin_link',
            "https://open.weixin.qq.com/connect/oauth2/authorize?appid=wx6de432d4ad7e151c&redirect_uri=http%3A%2F%2Freserve.fhsj.xipu.com%2Fapi%2Fsign%2Findex&response_type=code&scope=snsapi_base&state=STATE#wechat_redirect")

        # 卡片信息
        card_info = self.configData.get('card_info', {})
        self.card_title = card_info.get('title', "唱舞全明星")
        self.card_description = card_info.get('description', "点击进入签到页面，领取专属福利")
        self.card_thumb_url = card_info.get('thumb_url',
            "https://mmbiz.qpic.cn/mmbiz_jpg/RicuJvxibRtUBlv9G75UTulanktDF1OxFO7Wyhzs1WS609tq1j9icfNhLkM6zB3lwM5ZlbgQia1ibIcxuj35WAm465w/640?wxtype=jpeg&wxfrom=0")

    def _check_user_limit(self, roomid: str, sender: str) -> bool:
        """检查用户冷却时间"""
        user_key = f"{roomid}_{sender}"
        current_time = time.time()
        last_request = self.user_last_request.get(user_key, 0)

        if current_time - last_request < self.cooldown:
            return True

        self.user_last_request[user_key] = current_time
        return False

    async def _send_signin_card(self, target_id: str, self_wxid: str):
        """发送签到卡片"""
        await self.dp.sendRich(
            title=self.card_title,
            description=self.card_description,
            url=self.signin_link,
            thumb_url=self.card_thumb_url,
            toWxid=target_id,
            selfWxid=self_wxid
        )

    async def handle_message(self, msg):
        """处理消息"""
        if not self.enabled:
            return False

        content = msg.content.strip()
        target_id = msg.roomid if msg.from_group() else msg.sender

        # 检查是否是签到命令
        if not any(content.startswith(cmd) for cmd in self.commands):
            return False

        # 限流检查
        if self._check_user_limit(msg.roomid, msg.sender):
            await self.dp.sendText("请稍后再试", target_id, msg.self_wxid)
            return True

        # 发送签到卡片
        await self._send_signin_card(target_id, msg.self_wxid)
        return True

    async def handle_private_message(self, msg):
        """处理私聊消息"""
        return await self.handle_message(msg)
