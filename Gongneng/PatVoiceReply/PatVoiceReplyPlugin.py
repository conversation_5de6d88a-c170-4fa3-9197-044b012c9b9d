from Plugins._Tools import Tools
from Config.logger import logger
from Core.PluginBase import PluginBase
import os
import time
from pathlib import Path
from typing import Optional

class PatVoiceReplyPlugin(PluginBase):
    def __init__(self):
        super().__init__()
        # 基本信息（重要：name必须与目录名一致）
        self.name = "PatVoiceReply"
        self.description = "拍一拍语音回复插件"
        self.version = "1.0.0"
        self.author = "移植自XYBot"
        
        # 工具类实例
        self.tools = Tools()
        
        # 创建临时目录
        self.temp_dir = Path("App/Plugins/PatVoiceReply/temp")
        self.temp_dir.mkdir(parents=True, exist_ok=True)
        
        # 加载配置文件
        self.configData = self.tools.returnConfigData(os.path.dirname(__file__))
        self._load_config()
        
        # 用户冷却时间记录
        self.user_last_request = {}

    def _load_config(self):
        """加载配置"""
        self.enabled = self.configData.get('enabled', True)

        # 处理语音文件路径 - 转换为绝对路径
        voice_file_path = self.configData.get('voice_file_path', 'App/Plugins/PatVoiceReply/voice/pat_reply.mp3')

        # 如果是相对路径，转换为绝对路径
        if not os.path.isabs(voice_file_path):
            # 获取项目根目录（DPbot-main）
            current_dir = os.path.dirname(os.path.abspath(__file__))  # 当前插件目录
            project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))  # 向上3级到项目根目录
            self.voice_file_path = os.path.join(project_root, voice_file_path)
        else:
            self.voice_file_path = voice_file_path

        # 标准化路径
        self.voice_file_path = os.path.normpath(self.voice_file_path)

        self.cooldown = self.configData.get('cooldown', 3)
        # 只有被拍的是这个wxid时才触发
        self.target_wxid = self.configData.get('target_wxid', 'wxid_4usgcju5ey9q29')











    async def handle_message(self, msg) -> bool:
        """
        处理群聊消息 - 主要处理拍一拍消息
        Args:
            msg: 消息对象（WxMsg类）
        Returns:
            bool: True表示已处理消息，False表示未处理
        """
        # 检查插件是否启用
        if not self.enabled:
            return False



        # 检查是否是拍一拍消息
        if not self._is_pat_message(msg):
            return False

        logger.info(f"插件 {self.name} 处理拍一拍消息: {msg.content}")

        try:
            # 解析被拍者信息
            patted_wxid = self._extract_patted_wxid(msg)
            if not patted_wxid:
                return False

            # 检查被拍者是否是目标wxid
            if patted_wxid != self.target_wxid:
                return False

            # 限流检查
            wait_time = self._check_user_limit(msg.roomid or msg.sender, msg.sender)
            if wait_time > 0:
                return True

            # 发送语音回复
            await self._send_voice_reply(msg)
            return True

        except Exception as e:
            logger.error(f"[{self.name}] 处理拍一拍消息异常: {e}")
            return False

    def _is_pat_message(self, msg) -> bool:
        """判断是否是拍一拍消息"""
        # 拍一拍消息的类型是 10002
        if msg.type != 10002:
            return False

        # 检查消息内容是否包含拍一拍的XML结构
        content = msg._raw_content if hasattr(msg, '_raw_content') else str(msg.content)

        # 拍一拍消息包含特定的XML结构
        return '<sysmsg type="pat">' in content and '<pattedusername>' in content

    def _extract_patted_wxid(self, msg) -> Optional[str]:
        """从拍一拍消息中提取被拍者的wxid"""
        try:
            # 获取原始消息内容
            content = msg._raw_content if hasattr(msg, '_raw_content') else str(msg.content)

            # 解析XML格式的拍一拍消息
            import re

            # 提取 pattedusername 标签中的内容
            pattern = r'<pattedusername>(.*?)</pattedusername>'
            match = re.search(pattern, content)

            if match:
                patted_wxid = match.group(1).strip()
                logger.debug(f"[{self.name}] 解析到被拍者: {patted_wxid}")
                return patted_wxid

            # 备用方法：如果XML解析失败，尝试其他方式
            if self.target_wxid in content:
                logger.debug(f"[{self.name}] 在消息内容中找到目标用户: {self.target_wxid}")
                return self.target_wxid

            logger.debug(f"[{self.name}] 无法从消息中解析被拍者")
            return None

        except Exception as e:
            logger.error(f"[{self.name}] 解析被拍者信息异常: {e}")
            return None



    async def _send_voice_reply(self, msg):
        """发送语音回复"""
        target_id = msg.roomid if msg.from_group() else msg.sender

        # 检查文件是否存在
        if not os.path.exists(self.voice_file_path):
            logger.error(f"[{self.name}] 语音文件不存在: {self.voice_file_path}")
            await self.dp.sendText("🎵 收到拍一拍~", target_id, msg.self_wxid)
            return False

        try:
            # 发送语音消息
            result = await self.dp.sendVoice(str(self.voice_file_path), target_id, msg.self_wxid)

            # 检查发送结果
            if result:
                if isinstance(result, dict) and result.get("Success"):
                    return True
                elif isinstance(result, list) and len(result) > 0 and result[0].get("Success"):
                    return True
                else:
                    await self.dp.sendText("🎵 收到拍一拍~", target_id, msg.self_wxid)
                    return False
            else:
                await self.dp.sendText("🎵 收到拍一拍~", target_id, msg.self_wxid)
                return False

        except Exception as e:
            logger.error(f"[{self.name}] 语音发送异常: {e}")
            await self.dp.sendText("🎵 收到拍一拍~", target_id, msg.self_wxid)
            return False

    def _check_user_limit(self, roomid: str, sender: str) -> float:
        """检查用户冷却时间"""
        user_key = f"{roomid}_{sender}"
        last_request = self.user_last_request.get(user_key, 0)
        current_time = time.time()
        elapsed = current_time - last_request
        self.user_last_request[user_key] = current_time

        if elapsed < self.cooldown:
            return self.cooldown - elapsed

        return 0
