from Plugins._Tools import Tools
from Config.logger import logger
from Core.PluginBase import PluginBase
import os
import time
import random
import re

class FortunePlugin(PluginBase):
    def __init__(self):
        super().__init__()
        # 基本信息（重要：name必须与目录名一致）
        self.name = "Fortune"
        self.description = "抽签占卜插件"
        self.version = "1.0.0"
        self.author = "移植自XYBot"
        
        # 工具类实例
        self.tools = Tools()
        
        # 加载配置文件
        self.configData = self.tools.returnConfigData(os.path.dirname(__file__))
        self.enabled = self.configData.get('enabled', True)
        self.command = self.configData.get('command', ["抽签", "求签", "占卜"])
        self.api_url = self.configData.get('api_url', 'https://api.dragonlongzhu.cn/api/yl_qiuqian.php')
        self.api_timeout = self.configData.get('api_timeout', 10)
        self.cooldown = self.configData.get('cooldown', 5)
        
        # 用户冷却时间记录
        self.user_last_request = {}
        
        # 注意：self.dp 会由 PluginManager 自动设置，无需手动实例化

    async def handle_message(self, msg) -> bool:
        """处理消息"""
        if (msg.type != 1 or not self.enabled or
            not self.tools.judgeEqualListWord(msg.content, self.command)):
            return False

        target_id = msg.roomid if msg.from_group() else msg.sender

        # 限流检查
        wait_time = self._check_user_limit(msg.roomid, msg.sender)
        if wait_time > 0:
            await self.dp.sendText(f"请等待 {wait_time:.1f} 秒后再次抽签", target_id, msg.self_wxid)
            return True

        # 处理抽签
        result = await self._process_fortune_telling()
        response = result if result else "抽签失败，请稍后重试"
        await self.dp.sendText(response, target_id, msg.self_wxid)
        return True

    async def handle_private_message(self, msg) -> bool:
        """处理私聊消息"""
        return await self.handle_message(msg)

    async def _process_fortune_telling(self) -> str | None:
        """处理抽签请求"""
        try:
            # 生成随机签号
            random_number = random.randint(1, 300)

            data = await self.tools.async_get(
                url=self.api_url,
                timeout=self.api_timeout,
                return_json=False
            )

            if not data:
                return None

            if isinstance(data, bytes):
                data = data.decode('utf-8')

            # 解析签诗和解签内容
            fortune_match = re.search(r'「签诗」(.*?)「解签」', data)
            explanation_match = re.search(r'「解签」(.*?)$', data)

            if not fortune_match or not explanation_match:
                return None

            fortune_telling = fortune_match.group(1).strip()
            fortune_explanation = explanation_match.group(1).strip()

            return (
                f'您抽到了第{random_number}签！\n'
                f'🎐「签诗」{fortune_telling}\n'
                f'🎐「解签」{fortune_explanation}'
            )

        except Exception as e:
            logger.error(f"插件 {self.name} 抽签异常: {e}")
            return None

    def _check_user_limit(self, roomid: str, sender: str) -> float:
        """检查用户冷却时间"""
        user_key = f"{roomid}_{sender}"
        last_request = self.user_last_request.get(user_key, 0)
        current_time = time.time()
        elapsed = current_time - last_request
        self.user_last_request[user_key] = current_time

        if elapsed < self.cooldown:
            return self.cooldown - elapsed

        return 0
