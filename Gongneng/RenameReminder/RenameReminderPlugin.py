import os
import json
import time
import asyncio
from pathlib import Path
from typing import Dict, List

from Plugins._Tools import Tools
from Config.logger import logger
from Core.PluginBase import PluginBase
import Config.ConfigServer as Cs


class RenameReminderPlugin(PluginBase):
    def __init__(self):
        super().__init__()
        self.name = "RenameReminder"
        self.tools = Tools()
        self.configData = self.tools.returnConfigData(os.path.dirname(__file__))

        # 数据目录
        self.data_dir = Path("App/Data/RenameReminder")
        self.data_dir.mkdir(parents=True, exist_ok=True)

        # 配置
        self.enabled = self.configData.get('enabled', True)
        self.check_interval = self.configData.get('check_interval', 300)
        self.keywords = self.configData.get('keywords', [])

        # 监控群组
        self.rename_monitored_groups = set()
        self.leave_monitored_groups = set()
        self.monitored_groups = set()

        # 检查状态
        self._is_checking = False
        self._task_setup = False

        # 用户限制
        self.user_last_request = {}
        self.cooldown = self.configData.get('user_limit', {}).get('cooldown', 10)

        # 获取机器人微信ID
        self.self_wxid = Cs.returnLoginData().get('DPBotConfig', {}).get('selfWxid', '')

        # 加载监控群组
        if self.enabled:
            self._load_monitored_groups()

    def _setup_scheduled_task(self):
        """设置定时任务"""
        if not self.enabled or not hasattr(self, '_message_handler'):
            return

        if hasattr(self, '_check_task') and not self._check_task.done():
            self._check_task.cancel()

        self._check_task = asyncio.create_task(self._start_periodic_check())
        logger.info(f"[{self.name}] 启动定时检查任务，间隔: {self.check_interval}秒")

    async def close(self):
        """插件关闭时的清理"""
        if hasattr(self, '_check_task') and not self._check_task.done():
            self._check_task.cancel()

    def _load_monitored_groups(self):
        """加载监控群组配置"""
        config_file = self.data_dir / "monitored_groups.json"
        try:
            if config_file.exists():
                with open(config_file, "r", encoding="utf-8") as f:
                    data = json.load(f)
                    self.rename_monitored_groups = set(data.get("rename_groups", []))
                    self.leave_monitored_groups = set(data.get("leave_groups", []))
            self._update_monitored_groups()
        except Exception as e:
            logger.error(f"[{self.name}] 加载监控群配置失败: {e}")
            self._update_monitored_groups()

    def _save_monitored_groups(self):
        """保存监控群组配置"""
        config_file = self.data_dir / "monitored_groups.json"
        data = {
            "rename_groups": list(self.rename_monitored_groups),
            "leave_groups": list(self.leave_monitored_groups)
        }
        with open(config_file, "w", encoding="utf-8") as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        self._update_monitored_groups()

    def _update_monitored_groups(self):
        """更新监控群组集合"""
        self.monitored_groups = self.rename_monitored_groups.union(self.leave_monitored_groups)


    async def _save_member_list(self, group_id: str, members: List[Dict]):
        """保存群成员列表"""
        member_file = self.data_dir / f"{group_id}_members.json"
        member_data = {
            member["UserName"]: member.get("DisplayName") or member.get("NickName", "未知用户")
            for member in members
        }
        with open(member_file, "w", encoding="utf-8") as f:
            json.dump(member_data, f, ensure_ascii=False, indent=2)

    def _load_member_list(self, group_id: str) -> Dict[str, str]:
        """加载群成员列表"""
        member_file = self.data_dir / f"{group_id}_members.json"
        if member_file.exists():
            try:
                with open(member_file, "r", encoding="utf-8") as f:
                    return json.load(f)
            except Exception:
                pass
        return {}

    def _check_user_limit(self, target_id: str, user_id: str) -> float:
        """检查用户限制"""
        current_time = time.time()
        key = f"{target_id}_{user_id}"
        if key in self.user_last_request:
            time_diff = current_time - self.user_last_request[key]
            if time_diff < self.cooldown:
                return self.cooldown - time_diff
        self.user_last_request[key] = current_time
        return 0

    def _cleanup_member_file(self, group_id: str):
        """清理成员数据文件"""
        if group_id not in self.monitored_groups:
            member_file = self.data_dir / f"{group_id}_members.json"
            if member_file.exists():
                member_file.unlink()

    async def _init_group_members(self, group_id: str):
        """初始化群成员列表"""
        if group_id not in self.monitored_groups:
            try:
                result = await self.dp.getGroupMemberInfos(group_id, self.self_wxid)
                if result and result.get("Success"):
                    members = result.get("Data", {}).get("NewChatroomData", {}).get("ChatRoomMember", [])
                    await self._save_member_list(group_id, members)
            except Exception as e:
                logger.error(f"[{self.name}] 初始化群成员列表失败: {e}")

    async def handle_message(self, msg) -> bool:
        """处理消息"""
        if not self.enabled or not msg.from_group():
            return False

        # 设置定时任务
        if not self._task_setup:
            self._setup_scheduled_task()
            self._task_setup = True

        # 检查关键词和用户限制
        if not self.tools.judgeOneEqualListWord(msg.content, self.keywords):
            return False

        if self._check_user_limit(msg.roomid, msg.sender) > 0:
            return True

        # 处理命令
        await self._handle_command(msg)
        return True

    async def _handle_command(self, msg):
        """处理具体命令"""
        content = msg.content.strip()

        if content == "查看提醒状态":
            await self._show_status(msg)
        elif content == "开启改名提醒":
            await self._toggle_reminder(msg, "rename", True)
        elif content == "关闭改名提醒":
            await self._toggle_reminder(msg, "rename", False)
        elif content == "开启退群提醒":
            await self._toggle_reminder(msg, "leave", True)
        elif content == "关闭退群提醒":
            await self._toggle_reminder(msg, "leave", False)

    async def _show_status(self, msg):
        """显示提醒状态"""
        group_id = msg.roomid
        rename_status = "已开启" if group_id in self.rename_monitored_groups else "未开启"
        leave_status = "已开启" if group_id in self.leave_monitored_groups else "未开启"

        status_message = f"当前群提醒功能状态：\n改名提醒：{rename_status}\n退群提醒：{leave_status}"
        await self.dp.sendText(status_message, msg.roomid, msg.self_wxid)

    async def _toggle_reminder(self, msg, reminder_type: str, enable: bool):
        """切换提醒功能"""
        group_id = msg.roomid

        if reminder_type == "rename":
            groups = self.rename_monitored_groups
            name = "改名提醒"
        else:
            groups = self.leave_monitored_groups
            name = "退群提醒"

        # 检查当前状态
        is_enabled = group_id in groups
        if is_enabled == enable:
            status = "已开启" if enable else "未开启"
            await self.dp.sendText(f"{name}{status}", msg.roomid, msg.self_wxid)
            return

        # 切换状态
        if enable:
            await self._init_group_members(group_id)
            groups.add(group_id)
            await self.dp.sendText(f"已开启{name}功能", msg.roomid, msg.self_wxid)
        else:
            groups.remove(group_id)
            self._cleanup_member_file(group_id)
            await self.dp.sendText(f"已关闭{name}功能", msg.roomid, msg.self_wxid)

        self._save_monitored_groups()
        logger.info(f"[{self.name}] 群 {group_id} {'开启' if enable else '关闭'}了{name}")

    async def _start_periodic_check(self):
        """启动定时检查任务"""
        while True:
            try:
                await asyncio.sleep(self.check_interval)
                await self._check_member_changes()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"[{self.name}] 定时检查异常: {e}")

    async def _check_member_changes(self):
        """检查群成员变化"""
        if self._is_checking or not self.monitored_groups:
            return

        self._is_checking = True
        try:
            for group_id in list(self.monitored_groups):
                await self._check_group_changes(group_id)
        finally:
            self._is_checking = False

    async def _check_group_changes(self, group_id: str):
        """检查单个群的变化"""
        try:
            # 获取当前群成员列表
            result = await self.dp.getGroupMemberInfos(group_id, self.self_wxid)
            if not result or not result.get("Success"):
                return

            current_members = result.get("Data", {}).get("NewChatroomData", {}).get("ChatRoomMember", [])
            if not current_members:
                return

            # 构建当前成员名称映射
            current_names = {
                member["UserName"]: member.get("DisplayName") or member.get("NickName", "未知用户")
                for member in current_members
            }

            # 加载之前的成员列表
            previous_names = self._load_member_list(group_id)

            # 检查改名和退群
            if group_id in self.rename_monitored_groups:
                await self._check_name_changes(group_id, previous_names, current_names, current_members)

            if group_id in self.leave_monitored_groups:
                await self._check_member_leaves(group_id, previous_names, current_names)

            # 保存新的成员列表
            await self._save_member_list(group_id, current_members)

        except Exception as e:
            logger.error(f"[{self.name}] 检查群 {group_id} 变化失败: {e}")

    async def _send_rename_card(self, group_id: str, old_name: str, new_name: str, member_info: Dict):
        """发送改名提醒卡片"""
        try:
            avatar_url = member_info.get("SmallHeadImgUrl") or member_info.get("BigHeadImgUrl", "")
            await self.dp.sendRich(
                title="👤 群成员改名提醒",
                description=f"💫 群友换新装扮啦~\n👉 原昵称：{old_name}\n👉 新昵称：{new_name}",
                url="weixin://",
                thumb_url=avatar_url,
                toWxid=group_id,
                selfWxid=self.self_wxid
            )
        except Exception as e:
            logger.error(f"[{self.name}] 发送改名提醒失败: {e}")

    async def _check_name_changes(self, group_id: str, previous_names: Dict[str, str], current_names: Dict[str, str], current_members: List[Dict]):
        """检查改名变化"""
        # 构建成员信息映射
        member_info_map = {member["UserName"]: member for member in current_members}

        # 检查改名
        for user_id, old_name in previous_names.items():
            if user_id in current_names and current_names[user_id] != old_name:
                new_name = current_names[user_id]
                member_info = member_info_map.get(user_id, {})
                await self._send_rename_card(group_id, old_name, new_name, member_info)
                logger.info(f"[{self.name}] 群 {group_id} 改名: {old_name} -> {new_name}")

    async def _check_member_leaves(self, group_id: str, previous_names: Dict[str, str], current_names: Dict[str, str]):
        """检查退群成员"""
        for user_id, name in previous_names.items():
            if user_id not in current_names:
                await self._send_leave_card(group_id, name)
                logger.info(f"[{self.name}] 群 {group_id} 退群: {name}")

    async def _send_leave_card(self, group_id: str, name: str):
        """发送退群提醒卡片"""
        try:
            default_avatar = "https://res.wx.qq.com/a/wx_fed/webwx/res/static/img/2KriyDK.png"
            await self.dp.sendRich(
                title="群成员退群提醒",
                description=f"成员昵称：{name}",
                url="weixin://",
                thumb_url=default_avatar,
                toWxid=group_id,
                selfWxid=self.self_wxid
            )
        except Exception as e:
            logger.error(f"[{self.name}] 发送退群提醒失败: {e}")
