# RenameReminder 插件

## 功能说明
群成员改名和退群提醒插件，可以监控群成员的昵称变化和退群行为。

## 主要功能
1. **改名提醒**：监控群成员昵称变化，及时通知群内成员
2. **退群提醒**：监控群成员退群行为，及时通知群内成员
3. **状态查看**：查看当前群的提醒功能开启状态

## 使用方法

### 基本命令
- `开启改名提醒` - 开启群成员改名提醒功能
- `关闭改名提醒` - 关闭群成员改名提醒功能
- `开启退群提醒` - 开启群成员退群提醒功能
- `关闭退群提醒` - 关闭群成员退群提醒功能
- `查看提醒状态` - 查看当前群的提醒功能开启状态

### 工作原理
1. 插件会定时（默认5分钟）检查监控群组的成员变化
2. 对比当前成员列表与之前保存的成员列表
3. 发现变化时发送相应的提醒消息

## 配置说明

### config.toml
```toml
# 插件启用状态
enabled = true

# 触发关键词
keywords = ["开启改名提醒", "关闭改名提醒", "开启退群提醒", "关闭退群提醒", "查看提醒状态"]

# 检查间隔（秒）
check_interval = 300

# 用户限制
[user_limit]
cooldown = 10  # 冷却时间（秒）
```

## 数据存储
- 监控群组配置：`App/Data/RenameReminder/monitored_groups.json`
- 群成员列表：`App/Data/RenameReminder/{群ID}_members.json`

## 注意事项
1. 插件需要定时获取群成员列表，可能会有一定的API调用频率
2. 首次开启提醒功能时会保存当前群成员列表作为基准
3. 插件会自动处理用户限制，防止频繁操作
4. 当群组同时关闭改名和退群提醒时，会自动清理该群的成员数据文件
