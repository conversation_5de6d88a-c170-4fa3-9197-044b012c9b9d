# DoubaoImageGeneration 插件

## 功能说明
豆包图生图插件，支持基于参考图片生成新的图片。

## 主要功能
1. **引用图片生图**：引用图片消息，发送生图指令
2. **文本生图**：先发送图片，再发送包含生图关键词的文本
3. **智能提示词**：自动提取用户的生图要求

## 使用方法

### 引用图片生图
1. 长按图片消息选择"引用"
2. 发送生图指令，如："生图 画成卡通风格"
3. 等待图片生成完成

### 文本生图
1. 先发送参考图片
2. 5分钟内发送生图指令，如："生图 改成油画风格"
3. 等待图片生成完成

### 支持的关键词
- `生图` - 基础生图指令
- `画图` - 绘画风格生图
- `生成图片` - 完整生图指令
- `图生图` - 图片到图片转换
- `画一张` - 自然语言生图
- `生成` - 简化生图指令

## 配置说明

### config.toml
```toml
# 插件启用状态
enabled = true

# 用户限制
cooldown = 30  # 冷却时间（秒）

# API配置
[API]
# 豆包网站的cookies，需要从浏览器中获取
cookies = ""

# 生图触发关键词
keywords = ["生图", "画图", "生成图片", "图生图", "画一张", "生成"]
```

### Cookies获取方法
1. 打开浏览器访问 https://www.doubao.com/
2. 登录豆包账号
3. 按F12打开开发者工具
4. 切换到Network标签
5. 刷新页面，找到请求
6. 复制Cookie值到配置文件

## 工作原理
1. **图片缓存**：自动缓存用户发送的图片消息
2. **引用解析**：解析微信引用消息中的图片信息
3. **API调用**：调用豆包API进行图片生成
4. **结果返回**：将生成的图片发送给用户

## 注意事项
1. 需要配置有效的豆包账号cookies
2. 图片缓存时间为5分钟
3. 用户操作有30秒冷却时间
4. 支持群聊和私聊使用
5. 需要网络连接访问豆包API

## 错误处理
- 未找到参考图片时会提示用户
- API调用失败时会显示错误信息
- 用户操作过于频繁时会提示等待

## 技术特点
- 借鉴识图插件的引用图片处理逻辑
- 支持多种触发方式（引用消息、文本消息）
- 智能提示词提取
- 完善的错误处理和用户限制
