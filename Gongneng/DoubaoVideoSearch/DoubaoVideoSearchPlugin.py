from Plugins._Tools import Tools
from Config.logger import logger
from Core.PluginBase import PluginBase
import os
import json
import time
import random
import uuid
import httpx
import asyncio
from typing import Optional, Dict, Any
from urllib.parse import quote

# 定义HTTP响应类，用于兼容旧插件代码
class HttpResponse:
    def __init__(self, status_code, body, headers=None):
        self.code = status_code
        self.status_code = status_code
        self.body = body
        self.headers = headers or {}

class DoubaoVideoSearchPlugin(PluginBase):
    def __init__(self):
        super().__init__()
        # 基本信息（重要：name必须与目录名一致）
        self.name = "DoubaoVideoSearch"
        self.description = "豆包视频搜索插件"
        self.version = "1.0.0"
        self.author = "XYBot"

        # 工具类实例
        self.tools = Tools()

        # 加载配置文件
        self.configData = self.tools.returnConfigData(os.path.dirname(__file__))
        # 豆包找视频：豆包+找开头
        self.keywords = ['豆包']
        self.api_config = self.configData.get('API', {})
        
        # 豆包API配置
        self.api_base_url = "https://www.doubao.com/samantha/chat/completion"
        self.cookies = self.api_config.get('cookies', '')
        
        # 豆包API常量
        self.device_id = "7468716989062841895"
        self.tea_uuid = "7468716986638386703"
        self.web_id = "7468716986638386703"
        
        # httpx客户端管理
        self._client = None
        self._client_lock = asyncio.Lock()
        
        # 令牌桶配置
        rate_limit_config = self.configData.get('rate_limit', {})
        self.rate_limit = {
            "tokens_per_second": rate_limit_config.get("tokens_per_second", 0.4),
            "bucket_size": rate_limit_config.get("bucket_size", 4)
        }
        self._token_bucket = self.rate_limit["bucket_size"]
        self._last_token_time = time.time()
        self._token_lock = asyncio.Lock()
        
        # 用户级别的请求限制
        self._user_limits = {}
        self.min_request_interval = 3.0  # 视频搜索间隔
        
        # 视频搜索去重缓存 - 存储每个查询关键词已发送过的视频URL
        self._video_cache = {}  # 格式: {query_key: {wxid: [sent_urls]}}
        self._video_cache_lock = asyncio.Lock()
        
        # 不使用自然化响应
        self.natural_response = False



    async def get_httpx_client(self):
        """创建httpx客户端"""
        return httpx.AsyncClient(
            timeout=httpx.Timeout(connect=10.0, read=20.0, write=20.0, pool=5.0),
            verify=False,  # 禁用SSL验证
            follow_redirects=True,
            limits=httpx.Limits(max_connections=20, max_keepalive_connections=10),
        )

    async def get_session(self):
        """获取或创建httpx异步客户端"""
        async with self._client_lock:
            if self._client is None or self._client.is_closed:
                self._client = await self.get_httpx_client()
            return self._client

    async def close_session(self):
        """关闭httpx客户端"""
        async with self._client_lock:
            if self._client and not self._client.is_closed:
                await self._client.aclose()
                self._client = None

    def _generate_headers(self):
        """生成请求头"""
        # 生成随机的x-flow-trace
        flow_trace = f"04-{uuid.uuid4().hex[:16]}-{uuid.uuid4().hex[:16]}-01"

        return {
            "Accept": "*/*",
            "Accept-Encoding": "gzip, deflate",
            "Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
            "Connection": "keep-alive",
            "Content-Type": "application/json; charset=utf-8",
            "Cookie": self.cookies,
            "Host": "www.doubao.com",
            "Origin": "https://www.doubao.com",
            "Referer": "https://www.doubao.com/chat/",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "x-flow-trace": flow_trace,
            "Agw-Js-Conv": "str",
            "X-Requested-With": "mark.via",
            "last-event-id": "undefined",
            "Sec-Fetch-Site": "same-origin",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Dest": "empty"
        }

    async def handle_message(self, msg) -> bool:
        """处理消息"""
        try:
            # 只处理文本消息
            if msg.type != 1:
                return False

            content = msg.content.strip()
            
            # 检查是否是视频搜索请求
            if not self._is_video_search_request(content):
                return False
                
            logger.info(f"插件 {self.name} 处理视频搜索消息: {content}")
            
            # 提取搜索关键词
            search_keyword = self._extract_video_keyword(content)
            if not search_keyword:
                return False
            
            # 处理视频搜索请求
            await self._handle_video_search_request(msg, search_keyword)
            return True
            
        except Exception as e:
            logger.error(f"插件 {self.name} 处理消息时出错: {e}", exc_info=True)
            return True

    def _is_video_search_request(self, content: str) -> bool:
        """判断是否是视频搜索请求：豆包+找开头"""
        if not content.startswith("豆包"):
            return False

        # 提取豆包后的内容
        query = content[2:].strip()  # 移除"豆包"
        return query.startswith("找")

    def _extract_query(self, content: str) -> str:
        """提取查询内容"""
        for keyword in self.keywords:
            if content.startswith(keyword):
                return content[len(keyword):].strip()
        return content.strip()

    def _extract_video_keyword(self, content: str) -> str:
        """提取视频搜索关键词：移除豆包+找前缀"""
        # 移除"豆包"
        query = content[2:].strip()
        # 移除"找"
        if query.startswith("找"):
            keyword = query[1:].strip()
            return keyword if keyword else "搞笑视频"
        return "搞笑视频"

    async def _get_unique_video(self, videos: list, query_key: str, wxid: str) -> Optional[dict]:
        """从视频列表中获取一个未发送过的视频"""
        async with self._video_cache_lock:
            # 初始化缓存结构
            if query_key not in self._video_cache:
                self._video_cache[query_key] = {}
            if wxid not in self._video_cache[query_key]:
                self._video_cache[query_key][wxid] = []
            
            sent_urls = self._video_cache[query_key][wxid]
            
            # 查找未发送过的视频
            for video in videos:
                video_url = video.get("url", "")
                if video_url and video_url not in sent_urls:
                    # 记录已发送的视频URL
                    sent_urls.append(video_url)
                    # 限制缓存大小，只保留最近的20个URL
                    if len(sent_urls) > 20:
                        sent_urls.pop(0)
                    return video
            
            # 如果所有视频都发送过，清空缓存并返回第一个视频
            if videos:
                self._video_cache[query_key][wxid] = [videos[0].get("url", "")]
                return videos[0]
            
            return None

    async def _handle_video_search_request(self, msg, keyword: str):
        """处理视频搜索请求"""
        try:
            target_id = msg.roomid if msg.from_group() else msg.sender
            
            # 检查用户限制
            wait_time = self._check_user_limit(target_id, msg.sender)
            if wait_time > 0:
                return

            # 检查令牌桶
            if not await self._acquire_token():
                return
            
            # 调用豆包API搜索视频
            search_query = f"搜索抖音视频:{keyword}"
            result = await self.call_doubao_api(search_query)
            
            if result and result.get("type") == "videos":
                videos = result.get("videos", [])
                if videos:
                    # 获取未发送过的视频
                    unique_video = await self._get_unique_video(videos, keyword, target_id)
                    if unique_video:
                        await self._send_video_info(msg, unique_video)
                    
        except Exception as e:
            logger.error(f"处理视频搜索请求失败: {e}", exc_info=True)

    async def _send_video_info(self, msg, video: dict):
        """发送视频信息"""
        target_id = msg.roomid if msg.from_group() else msg.sender

        try:
            # 获取视频URL并解析
            video_url = video.get("url", "")
            if not video_url:
                logger.warning("视频URL为空，无法解析")
                return

            # 解析视频URL获取详细信息
            parsed_info = await self._parse_video_url(video_url)

            if parsed_info:
                # 使用解析后的信息发送卡片
                await self._send_video_card(msg, parsed_info)
            else:
                # 解析失败，发送原始信息
                title = video.get("title", "未知标题")
                source = video.get("source", "抖音")
                description = video.get("description", f"来源: {source}")

                video_text = f"🎬 {title}\n\n{description}\n\n🔗 {video_url}"
                await self.dp.sendText(video_text, target_id, msg.self_wxid)
            
        except Exception as e:
            logger.error(f"发送视频信息失败: {e}")

    async def _parse_video_url(self, video_url: str) -> Optional[dict]:
        """解析视频URL获取详细信息"""
        try:
            # 构建解析API URL
            parse_api = f"https://api.xn--ei1aa.cn/API/douyin.php?url={quote(video_url)}"

            client = await self.get_session()
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }

            response = await client.get(
                parse_api,
                headers=headers,
                timeout=httpx.Timeout(connect=10.0, read=30.0, write=20.0, pool=5.0)
            )

            if response.status_code != 200:
                logger.error(f"视频解析API请求失败: {response.status_code}")
                return None

            result = response.json()
            if result.get("code") == 200 and "data" in result:
                return result["data"]
            else:
                logger.error(f"视频解析失败: {result.get('msg', '未知错误')}")
                return None

        except Exception as e:
            logger.error(f"解析视频URL失败: {e}")
            return None

    async def _send_video_card(self, msg, video_info: dict):
        """发送视频卡片"""
        target_id = msg.roomid if msg.from_group() else msg.sender

        try:
            # 提取视频信息
            title = video_info.get("title", "未知标题")
            author = video_info.get("author", "未知作者")
            like_count = video_info.get("like", 0)
            cover_url = video_info.get("cover", "")
            video_url = video_info.get("url", "")

            # 如果没有封面图或封面图无效，使用默认封面
            if not cover_url or not cover_url.startswith(('http://', 'https://')):
                cover_url = "https://mmbiz.qpic.cn/mmbiz_jpg/BUO9n59znKqhia3Nk2icSCz4jYXyVTTWURzC9TH9U0amDgBKAxtnVAD5O3enFzW3oDX3xtCEcibLx335lRlJxScXg/640?wx_fmt=jpeg&tp=webp&wxfrom=5&wx_lazy=1"

            # 检查视频URL
            if not video_url:
                logger.error("解析后的视频URL为空")
                return

            # 构建卡片内容
            card_title = f"🎬 {title}"
            card_desc = f"👤 作者: {author}\n❤️ 点赞: {like_count}"

            # 调试信息
            logger.info(f"[DoubaoVideoSearch] 发送视频卡片:")
            logger.info(f"  标题: {card_title}")
            logger.info(f"  描述: {card_desc}")
            logger.info(f"  视频URL: {video_url}")
            logger.info(f"  封面URL: {cover_url}")

            # 发送应用消息 - 简化的视频应用消息格式
            xml_content = f"""<appmsg appid="wx76fdd06dde311af3" sdkver="0">
<title>{title}</title>
<des>{author}</des>
<action>view</action>
<type>68</type>
<showtype>0</showtype>
<content />
<url>{video_url}</url>
<thumburl>{cover_url}</thumburl>
<appattach>
    <totallen>0</totallen>
    <attachid />
    <fileext />
</appattach>
<weappinfo>
    <username />
    <appid />
    <appservicetype>0</appservicetype>
    <videopageinfo>
        <thumbwidth>540</thumbwidth>
        <thumbheight>960</thumbheight>
        <fromopensdk>0</fromopensdk>
    </videopageinfo>
</weappinfo>
</appmsg>
<fromusername>{msg.self_wxid}</fromusername>
<scene>0</scene>
<appinfo>
    <version>2</version>
    <appname>视频分享</appname>
</appinfo>"""

            # 使用SendApp接口发送应用消息
            from WeChatApi.Base import sendPostReq
            data = {
                "ToWxid": target_id,
                "Type": 49,  # 应用消息类型 (从日志中看到MsgType是49)
                "Wxid": msg.self_wxid,
                "Xml": xml_content
            }

            result = await sendPostReq("Msg/SendApp", data)
            logger.info(f"[DoubaoVideoSearch] 应用消息发送结果: {result}")

        except Exception as e:
            logger.error(f"发送视频卡片失败: {e}")
            # 卡片发送失败，回退到文本发送
            title = video_info.get("title", "未知标题")
            author = video_info.get("author", "未知作者")
            video_text = f"🎬 {title}\n👤 作者: {author}"
            await self.dp.sendText(video_text, target_id, msg.self_wxid)

    async def _update_tokens(self):
        """更新令牌桶中的令牌数"""
        current_time = time.time()
        elapsed = current_time - self._last_token_time
        new_tokens = elapsed * self.rate_limit["tokens_per_second"]
        self._token_bucket = min(
            self.rate_limit["bucket_size"],
            self._token_bucket + new_tokens
        )
        self._last_token_time = current_time

    async def _acquire_token(self) -> bool:
        """尝试获取一个令牌"""
        async with self._token_lock:
            await self._update_tokens()
            if self._token_bucket >= 1:
                self._token_bucket -= 1
                return True
            return False

    def _check_user_limit(self, wxid: str, user_wxid: str) -> float:
        """检查用户是否可以发送请求"""
        current_time = time.time()

        if wxid not in self._user_limits:
            self._user_limits[wxid] = {}

        last_request = self._user_limits[wxid].get(user_wxid, 0)
        wait_time = max(0, self.min_request_interval - (current_time - last_request))

        if wait_time == 0:
            self._user_limits[wxid][user_wxid] = current_time

        return wait_time

    async def call_doubao_api(self, query: str) -> Optional[Dict[str, Any]]:
        """调用豆包API搜索视频"""
        max_retries = 3
        base_delay = 3
        max_delay = 10

        for retry in range(max_retries):
            try:
                # 构造请求URL参数
                params = {
                    "aid": "497858",
                    "device_id": self.device_id,
                    "device_platform": "web",
                    "language": "zh",
                    "pc_version": "2.16.4",
                    "pkg_type": "release_version",
                    "real_aid": "497858",
                    "region": "CN",
                    "samantha_web": "1",
                    "sys_region": "CN",
                    "tea_uuid": self.tea_uuid,
                    "use-olympus-account": "1",
                    "version_code": "20800",
                    "web_id": self.web_id
                }

                # 创建随机的会话ID和消息ID
                conversation_id = f"38{int(time.time() * 10000)}8"
                section_id = f"{conversation_id[:10]}4{conversation_id[10:]}"
                message_id = f"ba0d6520-{uuid.uuid4().hex[:4]}-11f0-a188-0d4d{uuid.uuid4().hex[:8]}"

                # 转换查询格式为视频搜索格式
                search_query = query
                if "找点" in query and "视频" in query:
                    # 提取搜索关键词
                    video_keyword = query.replace("豆包", "").replace("找点", "").replace("视频", "").strip()
                    search_query = f"搜索抖音视频:{video_keyword}"

                # 构造请求数据
                request_data = {
                    "messages": [{
                        "content": json.dumps({"text": search_query}),
                        "content_type": 2001,
                        "attachments": [],
                        "references": []
                    }],
                    "completion_option": {
                        "is_regen": False,
                        "with_suggest": True,
                        "need_create_conversation": True,
                        "launch_stage": 1,
                        "is_replace": False,
                        "is_delete": False,
                        "message_from": 0,
                        "use_deep_think": False,
                        "use_auto_cot": False,
                        "event_id": "0"
                    },
                    "section_id": section_id,
                    "conversation_id": conversation_id,
                    "local_message_id": message_id
                }

                # 获取httpx客户端
                client = await self.get_session()

                # 设置请求头
                headers = self._generate_headers()

                # 构造完整URL
                url = f"{self.api_base_url}?" + "&".join([f"{k}={quote(str(v))}" for k,v in params.items()])

                # 发送POST请求
                response = await client.post(
                    url,
                    json=request_data,
                    headers=headers,
                    timeout=httpx.Timeout(connect=10.0, read=300.0, write=60.0, pool=10.0)
                )

                if response.status_code != 200:
                    error_text = response.content
                    logger.error(f"[DoubaoVideoSearch] API请求失败: 状态码={response.status_code}, 响应内容={error_text}")
                    raise ValueError(f"API请求失败: {response.status_code}")

                # 创建自定义响应对象
                http_response = HttpResponse(
                    status_code=response.status_code,
                    body=response.content,
                    headers=dict(response.headers)
                )

                # 处理响应
                result = await self._process_stream(http_response)

                if result:
                    # 请求完成后删除会话
                    asyncio.create_task(self._delete_conversation(conversation_id))
                    return result

                # 如果没有结果但还有重试机会，继续重试
                if retry < max_retries - 1:
                    wait_time = min(base_delay * (2 ** retry) + random.uniform(0, 2), max_delay)
                    await asyncio.sleep(wait_time)
                    continue

                return {"type": "videos", "videos": []}

            except Exception as e:
                logger.error(f"[DoubaoVideoSearch] API调用失败: {e}")
                if retry < max_retries - 1:
                    wait_time = min(base_delay * (2 ** retry) + random.uniform(0, 2), max_delay)
                    await asyncio.sleep(wait_time)
                    continue
                else:
                    return {"type": "videos", "videos": []}

        return {"type": "videos", "videos": []}

    async def _process_stream(self, response) -> Optional[Dict[str, Any]]:
        """处理SSE响应流，专门处理视频搜索"""
        result_data = {"type": "videos", "videos": []}
        videos_data = []

        try:
            # 解码响应数据
            if isinstance(response.body, str):
                chunk = response.body.encode('utf-8')
            else:
                chunk = response.body

            try:
                decoded_chunk = chunk.decode('utf-8', errors='ignore')
                buffer = decoded_chunk
            except UnicodeDecodeError:
                return None

            # 处理完整的SSE事件
            while "\n\n" in buffer:
                parts = buffer.split("\n\n", 1)
                event = parts[0]
                buffer = parts[1]

                if not event.strip():
                    continue

                # 提取"data:"行
                data_line = None
                for line in event.split("\n"):
                    if line.startswith("data:"):
                        data_line = line[5:].strip()
                        break

                if not data_line:
                    continue

                # 解析数据
                try:
                    event_data = json.loads(data_line)
                    if not isinstance(event_data, dict):
                        continue

                    if "event_type" not in event_data:
                        continue

                    event_type = event_data["event_type"]

                    # 处理结束事件
                    if event_type == 2003:
                        if videos_data:
                            result_data = {
                                "type": "videos",
                                "videos": videos_data
                            }
                            return result_data
                        return result_data

                    # 处理正常消息事件
                    if event_type == 2001 and "event_data" in event_data:
                        try:
                            inner_data = json.loads(event_data["event_data"])

                            if "message" not in inner_data:
                                continue

                            message = inner_data["message"]
                            is_finish = inner_data.get("is_finish", False)

                            if "content_type" not in message or "content" not in message:
                                continue

                            content_type = message["content_type"]
                            try:
                                content = json.loads(message["content"])
                            except json.JSONDecodeError:
                                continue

                            # 处理视频搜索内容
                            if content_type == 2007:
                                # 处理视频卡片数据
                                if "search_result" in content and "video_card" in content["search_result"]:
                                    video_card = content["search_result"]["video_card"]
                                    if "card_list" in video_card:
                                        for video_item in video_card["card_list"]:
                                            # 提取视频信息
                                            video_info = {
                                                "item_id": video_item.get("item_id", ""),
                                                "title": video_item.get("video_captions", "未知标题"),
                                                "url": video_item.get("main_site_url", ""),
                                                "thumb_url": video_item.get("video_first_frame_image", video_item.get("album_image", "")),
                                                "source": video_item.get("source_app_name", "抖音"),
                                                "description": f"来源: {video_item.get('source_app_name', '抖音')}"
                                            }

                                            # 避免重复添加
                                            if video_info not in videos_data:
                                                videos_data.append(video_info)

                                        # 标记为视频类型响应
                                        if videos_data:
                                            result_data = {
                                                "type": "videos",
                                                "videos": videos_data
                                            }

                                        # 如果是完成状态，立即返回结果
                                        if is_finish and videos_data:
                                            return result_data

                        except Exception:
                            continue

                except json.JSONDecodeError:
                    continue
                except Exception:
                    continue

            # 如果有视频数据，返回视频数据
            if videos_data:
                result_data = {
                    "type": "videos",
                    "videos": videos_data
                }
                return result_data

            # 返回空结果
            return result_data

        except Exception as e:
            logger.error(f"[DoubaoVideoSearch] 处理响应流失败: {e}")
            if videos_data:
                result_data = {
                    "type": "videos",
                    "videos": videos_data
                }
                return result_data
            return None

    async def _delete_conversation(self, conversation_id: str) -> bool:
        """删除豆包会话"""
        try:
            params = {
                "version_code": "20800",
                "language": "zh",
                "device_platform": "web",
                "aid": "497858",
                "real_aid": "497858",
                "pc_version": "2.16.1",
                "pkg_type": "release_version",
                "device_id": self.device_id,
                "web_id": self.web_id,
                "tea_uuid": self.tea_uuid,
                "use-olympus-account": "1",
                "region": "CN",
                "sys_region": "CN",
                "samantha_web": "1"
            }

            url = f"https://www.doubao.com/samantha/thread/delete?" + "&".join([f"{k}={quote(str(v))}" for k,v in params.items()])

            headers = {
                "Accept": "application/json, text/plain, */*",
                "Content-Type": "application/json",
                "Origin": "https://www.doubao.com",
                "Referer": "https://www.doubao.com/chat/",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                "X-Requested-With": "mark.via",
                "Connection": "keep-alive",
                "Cookie": self.cookies
            }

            data = {"conversation_id": conversation_id}

            client = await self.get_session()

            response = await client.post(
                url,
                json=data,
                headers=headers,
                timeout=httpx.Timeout(connect=5.0, read=10.0, write=10.0, pool=5.0)
            )

            return response.status_code == 200

        except Exception as e:
            logger.error(f"[DoubaoVideoSearch] 删除会话失败: {e}")
            return False
