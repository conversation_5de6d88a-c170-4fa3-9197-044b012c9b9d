from Plugins._Tools import Tools
from Plugins._Tools.ReferenceMessageHandler import ReferenceMessageHandler
from Plugins._Tools.ReferenceMessageErrorHandler import ReferenceMessageErrorHandler
from Config.logger import logger
from Core.PluginBase import PluginBase
import os
import json
import time
import uuid
import httpx
import asyncio
import base64
import hashlib
import hmac
import zlib
import datetime as dt
from urllib.parse import urlencode, urlparse
from typing import Optional, Dict, Any

class DoubaoVideoGenerationPlugin(PluginBase, ReferenceMessageHandler):
    """
    豆包图生视频插件 - 重构优化版本

    主要优化内容：
    1. 提取公共API参数构建方法，减少代码重复
    2. 统一XML解析逻辑，提高代码复用性
    3. 简化HTTP请求处理和错误处理
    4. 重构视频生成流程，分离复杂逻辑
    5. 优化轮询机制，提高可读性
    6. 重构图片上传流程，分离AWS签名和上传步骤
    """

    def __init__(self):
        super().__init__()
        self.name = "DoubaoVideoGeneration"
        self.description = "豆包图生视频插件 - 引用图片+生成视频+提示词"
        self.version = "1.0.0"
        self.author = "XYBot"

        # 工具类实例
        self.tools = Tools()

        # 加载配置文件
        self.configData = self.tools.returnConfigData(os.path.dirname(__file__))
        self.api_config = self.configData.get('API', {})

        # 豆包API配置
        self.api_base_url = "https://www.doubao.com/samantha/chat/completion"
        self.cookies = self.api_config.get('cookies', '')

        # 豆包API常量
        self.device_id = "7468716989062841895"
        self.tea_uuid = "7468716986638386703"
        self.web_id = "7468716986638386703"
        self.service_id = "a9rns2rl98"
        self.bot_id = "7338286299411103781"
        self.skill_id = "3"

        # 图片缓存
        self.image_cache = {}

        # httpx客户端管理
        self._client = None
        self._client_lock = asyncio.Lock()

    async def get_session(self):
        """获取httpx客户端"""
        async with self._client_lock:
            if self._client is None or self._client.is_closed:
                headers = {
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                    "Accept": "text/event-stream",
                    "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
                    "Accept-Encoding": "gzip, deflate, br",
                    "Connection": "keep-alive",
                    "Cookie": self.cookies,
                    "Referer": "https://www.doubao.com/chat/",
                    "Origin": "https://www.doubao.com",
                    "Sec-Ch-Ua": '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
                    "Sec-Ch-Ua-Mobile": "?0",
                    "Sec-Ch-Ua-Platform": '"Windows"',
                    "Sec-Fetch-Site": "same-origin",
                    "Sec-Fetch-Mode": "cors",
                    "Sec-Fetch-Dest": "empty"
                }
                self._client = httpx.AsyncClient(
                    headers=headers,
                    timeout=httpx.Timeout(30.0, connect=10.0),
                    follow_redirects=True
                )
            return self._client

    async def handle_message(self, msg) -> bool:
        """处理消息"""
        try:
            # 如果是图片消息，缓存图片信息
            if msg.type == 3:  # 图片消息
                await self._cache_image_info(msg)
                return False

            # 检查是否是图生视频请求
            if not self._is_video_generation_request(msg):
                return False

            # 处理图生视频请求
            await self._handle_video_generation_request(msg)
            return True

        except Exception as e:
            logger.error(f"插件 {self.name} 处理消息时出错: {e}")
            return False

    def _parse_xml_safely(self, xml_content: str) -> Optional[Any]:
        """
        安全解析XML内容

        重构说明：合并重复的XML解析代码，创建通用的XML解析辅助方法
        原来在多个方法中都有相似的XML解析逻辑，现在统一到这个方法中
        """
        try:
            import xml.etree.ElementTree as ET
            if 'wxid_' in xml_content and '<?xml' in xml_content:
                xml_start = xml_content.find('<?xml')
                if xml_start != -1:
                    xml_part = xml_content[xml_start:].strip()
                    if xml_part:
                        return ET.fromstring(xml_part)
        except Exception as e:
            logger.debug(f"XML解析失败: {e}")
        return None

    def _is_video_generation_request(self, msg) -> bool:
        """判断是否是图生视频请求：引用图片+生成视频+任一提示词"""
        # 只处理引用消息
        if msg.type != 49:
            return False

        # 使用标准方法检查是否是引用消息
        if not self._is_reference_message(msg.content):
            return False

        # 检查引用的是否是图片
        if self._get_reference_type(msg.content) != "3":
            return False

        # 提取用户指令
        prompt = self._extract_prompt_from_reference(msg.content)
        if not prompt:
            return False

        # 检查是否包含生成视频关键词
        return "生成视频" in prompt

    async def _cache_image_info(self, msg):
        """缓存图片消息的下载信息"""
        try:
            msg_id = str(msg.id)
            self.image_cache[msg_id] = {
                "msg_id": msg_id,
                "to_wxid": msg.roomid if msg.from_group() else msg.sender,
                "wxid": msg.self_wxid,
                "timestamp": time.time(),
                "content": msg.content
            }

            # 清理过期缓存（保留最近1小时的图片）
            current_time = time.time()
            expired_keys = [
                key for key, info in self.image_cache.items()
                if current_time - info["timestamp"] > 3600
            ]
            for key in expired_keys:
                del self.image_cache[key]

        except Exception as e:
            logger.error(f"缓存图片信息失败: {e}")

    async def _handle_video_generation_request(self, msg):
        """处理图生视频请求"""
        try:
            target_id = msg.roomid if msg.from_group() else msg.sender

            # 使用标准方法获取图片数据
            image_data = await self._get_reference_image(msg.content, msg.self_wxid)
            prompt = self._extract_prompt_from_message(msg)

            if not image_data:
                await ReferenceMessageErrorHandler.handle_image_not_found(
                    self.dp, target_id, msg.self_wxid
                )
                return

            # 发送开始生成的提示
            await self.dp.sendText("🎬 开始生成视频，请稍候...", target_id, msg.self_wxid)

            # 调用豆包API生成视频
            result = await self.generate_video(image_data, prompt)

            if result and result.get("success"):
                # 发送生成的视频
                video_url = result.get("video_url")
                if video_url:
                    await self.dp.sendVideo(video_url, target_id, msg.self_wxid)
                else:
                    await self.dp.sendText("视频生成完成，但获取视频失败", target_id, msg.self_wxid)
            else:
                error_msg = result.get("error", "视频生成失败") if result else "视频生成失败"
                await self.dp.sendText(f"❌ {error_msg}", target_id, msg.self_wxid)

        except Exception as e:
            logger.error(f"处理图生视频请求失败: {e}")
            await ReferenceMessageErrorHandler.handle_processing_error(
                self.dp, target_id, msg.self_wxid, "视频生成"
            )

    def _extract_prompt_from_message(self, msg) -> str:
        """从引用消息中提取提示词：生成视频+任一提示词"""
        # 使用标准方法提取提示词
        prompt = self._extract_prompt_from_reference(msg.content)
        if prompt and "生成视频" in prompt:
            # 移除"生成视频"关键词，保留后面的提示词
            cleaned_prompt = prompt.replace("生成视频", "").strip()
            return cleaned_prompt if cleaned_prompt else "生成视频"

        return "生成视频"

    # 使用基类的 _get_reference_image 方法

    def _decode_base64_image(self, data: Any) -> Optional[bytes]:
        """解码base64图片数据"""
        try:
            # CDN接口返回的数据格式是 {"Image": "base64_data"}
            if isinstance(data, dict) and "Image" in data:
                img_data = data["Image"]
            elif isinstance(data, str):
                img_data = data
            else:
                return None

            image_bytes = base64.b64decode(img_data)
            return image_bytes if len(image_bytes) > 1000 else None
        except Exception:
            return None

    async def _download_image_with_cdn_api(self, cdn_url: str, aes_key: str, wxid: str) -> Optional[bytes]:
        """使用DPbot的CDN下载接口下载图片"""
        try:
            from WeChatApi.Base import sendPostReq

            data = {
                "FileAesKey": aes_key,
                "FileNo": cdn_url,
                "Wxid": wxid
            }

            result = await sendPostReq("Tools/CdnDownloadImage", data)

            if result and result.get("Success"):
                return self._decode_base64_image(result.get("Data"))

            return None

        except Exception as e:
            logger.error(f"CDN下载图片失败: {e}")
            return None

    def _build_api_params(self, pc_version: str = "2.24.5") -> Dict[str, str]:
        """
        构建通用API请求参数

        重构说明：提取重复的API参数构建逻辑，减少代码重复
        原来在多个方法中都有相同的参数构建代码，现在统一到这个方法中
        """
        return {
            'aid': '497858',
            'device_id': self.device_id,
            'device_platform': 'web',
            'language': 'zh',
            'pc_version': pc_version,
            'pkg_type': 'release_version',
            'real_aid': '497858',
            'region': 'CN',
            'samantha_web': '1',
            'sys_region': 'CN',
            'tea_uuid': self.tea_uuid,
            'use-olympus-account': '1',
            'version_code': '20800',
            'web_id': self.web_id
        }

    def _build_video_request_data(self, image_uri: str, prompt: str) -> Dict[str, Any]:
        """构建视频生成请求数据"""
        local_conversation_id = f"local_{int(time.time() * 1000000)}"
        local_message_id = str(uuid.uuid4())

        return {
            "messages": [{
                "content": json.dumps({"text": prompt}),
                "content_type": 2020,  # 图生视频内容类型
                "attachments": [{
                    "type": "image",
                    "key": image_uri,
                    "identifier": str(uuid.uuid4()),
                    "extra": {
                        "refer_types": "overall"
                    }
                }]
            }],
            "completion_option": {
                "is_regen": False,
                "with_suggest": False,
                "need_create_conversation": True,
                "launch_stage": 1,
                "is_replace": False,
                "is_delete": False,
                "message_from": 0,
                "use_auto_cot": False,
                "resend_for_regen": False,
                "event_id": "0"
            },
            "conversation_id": "0",
            "local_conversation_id": local_conversation_id,
            "local_message_id": local_message_id
        }

    async def generate_video(self, image_data: bytes, prompt: str = "生成视频") -> Dict[str, Any]:
        """调用豆包API生成视频"""
        try:
            # 上传图片获取image_uri
            image_uri = await self._upload_image(image_data)
            if not image_uri:
                return {"success": False, "error": "图片上传失败"}

            # 构建请求参数
            base_params = self._build_api_params()

            # 构建请求数据
            request_data = self._build_video_request_data(image_uri, prompt)

            # 发送请求并处理响应
            return await self._send_video_request_and_process(base_params, request_data)

        except Exception as e:
            logger.error(f"生成视频时发生错误: {e}")
            return {"success": False, "error": str(e)}

    async def _send_video_request_and_process(self, base_params: Dict, request_data: Dict) -> Dict[str, Any]:
        """发送视频生成请求并处理流式响应"""
        # 构建请求头
        headers = {
            'Content-Type': 'application/json',
            'x-flow-trace': f"04-{uuid.uuid4().hex[:16]}-{uuid.uuid4().hex[:16]}-01",
            'last-event-id': 'undefined',
            'Agw-Js-Conv': 'str, str',
            'Accept': '*/*',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Dest': 'empty'
        }

        # 发送请求
        client = await self.get_session()
        api_url = f"{self.api_base_url}?{urlencode(base_params)}"
        response = await client.post(api_url, json=request_data, headers=headers)

        if response.status_code != 200:
            return {"success": False, "error": f"请求失败: {response.status_code}"}

        # 处理流式响应
        conversation_id = await self._process_stream_response(response)

        if conversation_id:
            video_url = await self._poll_message_status(conversation_id, None)
            if video_url:
                return {"success": True, "video_url": video_url}

        return {"success": False, "error": "未获取到视频URL"}

    async def _process_stream_response(self, response) -> Optional[str]:
        """处理流式响应，返回conversation_id"""
        conversation_id = None

        async for line in response.aiter_lines():
            if not line.startswith('data: '):
                continue

            try:
                data_str = line[6:]  # 去掉 'data: ' 前缀
                if not data_str.strip():
                    continue

                event_data = json.loads(data_str)
                if 'event_data' not in event_data or not event_data['event_data']:
                    continue

                inner_data = json.loads(event_data['event_data'])

                # 保存conversation_id
                if 'conversation_id' in inner_data:
                    conversation_id = inner_data['conversation_id']

                # 检查是否是视频生成消息
                if 'message' in inner_data:
                    message = inner_data['message']
                    if message.get('content_type') == 2021:
                        # 获取conversation_id用于轮询
                        conv_id = inner_data.get('conversation_id') or conversation_id
                        message_id = inner_data.get('message_id')

                        if conv_id:
                            # 开始轮询消息状态
                            video_url = await self._poll_message_status(conv_id, message_id)
                            if video_url:
                                return conv_id  # 直接返回成功的conversation_id

            except (json.JSONDecodeError, Exception) as e:
                logger.debug(f"处理响应行时出错: {e}")
                continue

        return conversation_id

    async def _poll_message_status(self, conversation_id: str, message_id: str = None) -> Optional[str]:
        """
        轮询消息状态，检查视频生成进度

        重构说明：简化轮询逻辑，将复杂的响应处理分离到独立方法
        原来的轮询方法过于复杂，现在分离为多个小方法，提高可读性
        """
        logger.debug(f"开始轮询消息状态，会话ID: {conversation_id}, 消息ID: {message_id or 'None'}")

        # 先等待4分钟
        logger.debug("等待4分钟后开始轮询...")
        await asyncio.sleep(240)  # 4分钟 = 240秒

        # 构建轮询请求参数
        api_url = "https://www.doubao.com/alice/message/list"
        base_params = self._build_api_params()
        request_data = {
            "conversation_id": conversation_id,
            "cursor": 2,  # 从第2条消息开始（基于抓包数据）
            "batch_size": 50
        }
        headers = {
            'Accept': 'application/json, text/plain, */*',
            'Content-Type': 'application/json',
            'Agw-Js-Conv': 'str'
        }

        client = await self.get_session()
        attempt = 1

        # 每15秒轮询一次，直到有结果
        while True:
            try:
                logger.debug(f"第 {attempt} 次消息轮询...")
                response = await client.post(api_url, params=base_params, json=request_data, headers=headers)

                if response.status_code == 200:
                    video_url = await self._process_poll_response(response)
                    if video_url:
                        return video_url

            except Exception as e:
                logger.debug(f"消息轮询过程中发生错误，忽略错误继续轮询: {e}")

            # 等待15秒后继续下次轮询
            attempt += 1
            logger.debug(f"等待15秒后继续第 {attempt} 次轮询...")
            await asyncio.sleep(15)

    async def _process_poll_response(self, response) -> Optional[str]:
        """处理轮询响应，查找视频URL"""
        try:
            result = response.json()
            if result.get('code') != 0 or 'data' not in result:
                return None

            message_list = result['data'].get('message_list', [])

            # 查找视频生成消息
            for message in message_list:
                if message.get('content_type') == 2021:  # 视频生成响应
                    video_url = await self._extract_video_from_message(message)
                    if video_url:
                        return video_url

        except Exception as e:
            logger.debug(f"处理轮询响应失败: {e}")

        return None

    async def _extract_video_from_message(self, message: Dict) -> Optional[str]:
        """从消息中提取视频URL"""
        try:
            content_str = message.get('content', '{}')
            content = json.loads(content_str)
            video_status = content.get('video_status')

            logger.debug(f"找到视频消息，状态: {video_status}")

            # 检查是否有视频ID
            video_id = content.get('vid')
            if video_id:
                logger.debug(f"获取到视频ID: {video_id}")
                video_url = await self._get_video_play_info(video_id)
                if video_url:
                    return video_url
                else:
                    logger.warning("视频ID存在但获取播放信息失败")

            # 根据状态处理
            if video_status == 3:  # 生成失败
                logger.error("视频生成失败")
                return None
            elif video_status == 1:
                logger.debug("视频仍在生成中...")
            elif video_status == 4:
                logger.info("视频生成完成但未找到视频ID")
            else:
                logger.debug(f"未知视频状态: {video_status}")

        except json.JSONDecodeError as e:
            logger.debug(f"消息内容解析失败: {e}")

        return None

    async def _get_video_play_info(self, video_id: str) -> Optional[str]:
        """获取视频播放信息"""
        try:
            # 构建请求参数
            base_params = self._build_api_params()

            # 构建请求数据
            request_data = {
                "vid": video_id
            }

            # 构建请求头
            headers = {
                'Accept': 'application/json, text/plain, */*',
                'Content-Type': 'application/json',
                'Agw-Js-Conv': 'str'
            }

            api_url = "https://www.doubao.com/samantha/video/get_play_info"
            client = await self.get_session()

            response = await client.post(api_url, params=base_params, json=request_data, headers=headers)

            if response.status_code == 200:
                try:
                    result = response.json()
                except Exception as e:
                    logger.debug(f"获取播放信息JSON解析失败: {e}")
                    return None

                if result.get('code') == 0 and 'data' in result:
                    data = result['data']
                    play_infos = data.get('play_infos', [])

                    if play_infos and len(play_infos) > 0:
                        # 选择分辨率最高的视频（如果有多个分辨率选项）
                        best_play_info = None
                        max_resolution = 0

                        for play_info in play_infos:
                            width = play_info.get('width', 0)
                            height = play_info.get('height', 0)
                            resolution = width * height

                            if resolution > max_resolution:
                                max_resolution = resolution
                                best_play_info = play_info

                        # 如果没找到最佳选项，使用第一个
                        if not best_play_info:
                            best_play_info = play_infos[0]

                        # 优先使用main链接，不可用时使用backup链接
                        video_url = best_play_info.get('main') or best_play_info.get('backup')

                        if video_url:
                            return video_url

            return None

        except Exception as e:
            logger.error(f"获取播放信息时发生错误: {e}")
            return None

    def _create_upload_client(self) -> httpx.AsyncClient:
        """创建上传客户端"""
        cookies = self._parse_cookies(self.cookies)

        # 为上传操作设置较长的超时时间
        timeout_config = httpx.Timeout(
            connect=10.0,    # 连接超时
            read=120.0,      # 读取超时（上传可能需要较长时间）
            write=120.0,     # 写入超时（上传文件）
            pool=10.0        # 连接池超时
        )

        return httpx.AsyncClient(
            cookies=cookies,
            timeout=timeout_config,
            follow_redirects=True,
            verify=False
        )

    def _get_upload_headers(self) -> Dict[str, str]:
        """获取上传请求头"""
        return {
            'User-Agent': 'Mozilla/5.0 (Linux; Android 10; V2002A Build/QP1A.190711.020) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Mobile Safari/537.36',
            'Accept': '*/*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
            'Accept-Encoding': 'gzip, deflate',
            'Origin': 'https://www.doubao.com',
            'Referer': 'https://www.doubao.com/chat/create-image',
            'X-Requested-With': 'mark.via',
        }

    async def _get_upload_auth(self, client: httpx.AsyncClient, headers: Dict[str, str]) -> Optional[Dict]:
        """获取上传认证信息"""
        url = "https://www.doubao.com/alice/resource/prepare_upload"
        params = self._build_api_params("2.24.2")
        data = {
            "tenant_id": "5",
            "scene_id": "5",
            "resource_type": 2
        }

        response = await client.post(url, params=params, json=data, headers=headers)
        if response.status_code != 200:
            return None

        result = response.json()
        return result['data']['upload_auth_token'] if result.get('code') == 0 else None

    async def _apply_upload_permission(self, client: httpx.AsyncClient, headers: Dict[str, str], upload_auth: Dict,
                                     file_size: int, file_ext: str) -> Optional[Dict]:
        """申请上传权限"""
        upload_params = {
            'Action': 'ApplyImageUpload',
            'Version': '2018-08-01',
            'ServiceId': self.service_id,
            'NeedFallback': 'true',
            'FileSize': str(file_size),
            'FileExtension': file_ext,
            's': 'yy49d6n7o6p'
        }

        apply_url = f"https://imagex.bytedanceapi.com/?{urlencode(upload_params)}"
        aws_headers = self._generate_aws_signature('GET', apply_url, upload_auth)

        request_headers = headers.copy()
        request_headers.update(aws_headers)

        response = await client.get(apply_url, headers=request_headers)
        if response.status_code != 200:
            return None

        upload_info = response.json()
        return upload_info.get('Result')

    async def _upload_file_data(self, client: httpx.AsyncClient, upload_info: Dict, image_data: bytes,
                              crc32_value: int) -> bool:
        """上传文件数据"""
        store_info = upload_info['UploadAddress']['StoreInfos'][0]
        upload_host = upload_info['UploadAddress']['UploadHosts'][0]

        upload_url = f"https://{upload_host}/upload/v1/{store_info['StoreUri']}"
        upload_headers = {
            'Authorization': store_info['Auth'],
            'Content-CRC32': f"{crc32_value:08x}",
            'Content-Type': 'application/octet-stream',
            'X-Storage-U': store_info['UploadID'],
            'Origin': 'https://www.doubao.com',
            'Referer': 'https://www.doubao.com/chat/',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        }

        response = await client.post(upload_url, content=image_data, headers=upload_headers)
        return response.status_code == 200 and response.json().get('code') == 2000

    async def _commit_upload(self, client: httpx.AsyncClient, upload_info: Dict, upload_auth: Dict) -> Optional[str]:
        """提交上传完成"""
        commit_params = {
            'Action': 'CommitImageUpload',
            'Version': '2018-08-01',
            'ServiceId': self.service_id
        }

        commit_url = f"https://imagex.bytedanceapi.com/?{urlencode(commit_params)}"
        session_key = upload_info['UploadAddress']['SessionKey']
        commit_payload = json.dumps({"SessionKey": session_key})

        commit_aws_headers = self._generate_aws_signature('POST', commit_url, upload_auth, commit_payload)
        commit_headers = {
            'Content-Type': 'application/json',
            'Accept': '*/*',
            'Origin': 'https://www.doubao.com',
            'Referer': 'https://www.doubao.com/chat/',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        }
        commit_headers.update(commit_aws_headers)

        response = await client.post(commit_url, content=commit_payload, headers=commit_headers)
        if response.status_code != 200:
            return None

        commit_result = response.json()
        if 'Result' not in commit_result:
            return None

        return commit_result['Result']['Results'][0]['Uri']

    async def _upload_image(self, image_data: bytes) -> Optional[str]:
        """上传图片到豆包服务器"""
        try:
            async with self._create_upload_client() as client:
                headers = self._get_upload_headers()

                file_size = len(image_data)
                file_ext = '.jpg'
                crc32_value = self._calculate_crc32(image_data)

                # 1. 获取上传认证信息
                upload_auth = await self._get_upload_auth(client, headers)
                if not upload_auth:
                    return None

                # 2. 申请上传权限
                upload_info = await self._apply_upload_permission(client, headers, upload_auth, file_size, file_ext)
                if not upload_info:
                    return None

                # 3. 上传图片文件
                if not await self._upload_file_data(client, upload_info, image_data, crc32_value):
                    return None

                # 4. 提交上传完成
                return await self._commit_upload(client, upload_info, upload_auth)

        except Exception as e:
            logger.error(f"上传图片时发生错误: {e}")
            return None

    def _parse_cookies(self, cookie_string: str) -> Dict[str, str]:
        """解析Cookie字符串"""
        cookies = {}
        for item in cookie_string.split(';'):
            if '=' in item:
                key, value = item.strip().split('=', 1)
                cookies[key] = value
        return cookies

    def _calculate_crc32(self, data: bytes) -> int:
        """计算文件CRC32校验值"""
        return zlib.crc32(data) & 0xffffffff

    def _generate_aws_signature(self, method: str, url: str, upload_auth: Dict, payload: str = "") -> Dict[str, str]:
        """生成AWS4-HMAC-SHA256签名"""
        access_key = upload_auth['access_key']
        secret_key = upload_auth['secret_key']
        session_token = upload_auth['session_token']

        # 解析URL
        parsed = urlparse(url)
        host = parsed.netloc
        path = parsed.path if parsed.path else '/'
        query = parsed.query

        # 时间戳
        t = dt.datetime.now(dt.timezone.utc)
        amz_date = t.strftime('%Y%m%dT%H%M%SZ')
        date_stamp = t.strftime('%Y%m%d')

        # 规范化查询字符串
        if query:
            query_params = []
            for param in query.split('&'):
                if '=' in param:
                    key, value = param.split('=', 1)
                    query_params.append((key, value))
                else:
                    query_params.append((param, ''))
            query_params.sort()
            canonical_querystring = '&'.join([f'{k}={v}' for k, v in query_params])
        else:
            canonical_querystring = ''

        # 规范化头部
        canonical_headers = f'host:{host}\nx-amz-date:{amz_date}\nx-amz-security-token:{session_token}\n'
        signed_headers = 'host;x-amz-date;x-amz-security-token'

        # 载荷哈希
        payload_hash = hashlib.sha256(payload.encode('utf-8')).hexdigest()

        # 规范化请求
        canonical_request = f'{method}\n{path}\n{canonical_querystring}\n{canonical_headers}\n{signed_headers}\n{payload_hash}'

        # 创建签名字符串
        algorithm = 'AWS4-HMAC-SHA256'
        credential_scope = f'{date_stamp}/cn-north-1/imagex/aws4_request'
        string_to_sign = f'{algorithm}\n{amz_date}\n{credential_scope}\n{hashlib.sha256(canonical_request.encode()).hexdigest()}'

        # 计算签名
        def sign(key, msg):
            if isinstance(key, str):
                key = key.encode('utf-8')
            return hmac.new(key, msg.encode('utf-8'), hashlib.sha256).digest()

        def get_signature_key(key, date_stamp, region_name, service_name):
            k_date = sign('AWS4' + key, date_stamp)
            k_region = sign(k_date, region_name)
            k_service = sign(k_region, service_name)
            k_signing = sign(k_service, 'aws4_request')
            return k_signing

        signing_key = get_signature_key(secret_key, date_stamp, 'cn-north-1', 'imagex')
        signature = hmac.new(signing_key, string_to_sign.encode('utf-8'), hashlib.sha256).hexdigest()

        # 构建认证头部
        authorization_header = f'{algorithm} Credential={access_key}/{credential_scope}, SignedHeaders={signed_headers}, Signature={signature}'

        return {
            'Authorization': authorization_header,
            'X-Amz-Date': amz_date,
            'x-amz-security-token': session_token
        }

    async def close(self):
        """插件关闭时的清理"""
        if self._client and not self._client.is_closed:
            await self._client.aclose()
