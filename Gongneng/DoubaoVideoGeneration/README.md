# 豆包图生视频插件

## 📖 插件简介

豆包图生视频插件是基于豆包AI的图片生成视频功能，能够将静态图片转换为动态视频。插件完全移植自旧插件系统，保持原有功能逻辑不变，并适配到DPbot项目架构中。

## 🚀 功能特性

- **图生视频**：将静态图片转换为动态视频
- **智能识别**：精确识别引用图片+豆包视频+提示词的触发条件
- **异步处理**：支持异步视频生成，不阻塞其他功能
- **错误处理**：完善的错误处理和用户反馈机制
- **Cookie复用**：直接复用旧插件的认证配置

## 🎯 触发条件

插件需要同时满足以下三个条件才会触发：

1. **引用图片**：用户需要引用/回复一张图片消息
2. **关键词**：消息中包含"豆包视频"关键词
3. **提示词**：消息中包含视频生成的提示词内容

### 使用示例

```
用户A: [发送一张图片]
用户B: [引用上面的图片] 豆包视频 让图中的人物动起来
机器人: 🎬 开始生成视频，请稍候...
机器人: [发送生成的视频]
```

## ⚙️ 配置说明

### config.toml 配置文件

```toml
# 插件启用状态
enabled = true

# 用户限制
cooldown = 60  # 冷却时间（秒），视频生成比较耗时

# API配置
[API]
# 豆包网站的cookies（从旧插件复制）
cookies = "your_cookies_here"

# 视频生成触发关键词
keywords = ["豆包视频"]
```

### Cookie配置

Cookie配置直接复用旧插件中的配置，无需重新获取：

1. 打开 `config.toml` 文件
2. 在 `[API]` 部分找到 `cookies` 配置项
3. 确认Cookie字符串完整且有效

## 🔧 技术实现

### 核心流程

1. **消息识别**：检测引用图片+豆包视频+提示词的组合
2. **图片获取**：从引用消息中提取图片数据
3. **图片上传**：将图片上传到豆包服务器
4. **视频生成**：调用豆包API生成视频
5. **状态轮询**：轮询检查视频生成状态
6. **结果返回**：获取视频URL并发送给用户

### 关键特性

- **AWS签名认证**：完整实现AWS4-HMAC-SHA256签名算法
- **流式响应处理**：支持豆包API的流式响应格式
- **消息轮询机制**：自动轮询视频生成状态
- **CDN图片下载**：使用DPbot的CDN下载接口获取图片

## 📋 使用注意事项

### 前置条件

1. **Cookie有效性**：确保豆包网站的Cookie未过期
2. **网络连接**：需要稳定的网络连接访问豆包服务
3. **图片格式**：支持常见的图片格式（JPG、PNG等）
4. **服务可用性**：豆包图生视频服务正常运行

### 使用限制

1. **冷却时间**：默认60秒冷却时间，避免频繁调用
2. **图片大小**：建议图片大小适中，过大可能影响处理速度
3. **提示词长度**：提示词建议简洁明确
4. **并发限制**：避免同时发起多个视频生成请求

### 错误处理

- **图片获取失败**：检查引用的图片是否有效
- **上传失败**：检查网络连接和Cookie有效性
- **生成失败**：可能是服务繁忙或提示词不合适
- **超时处理**：视频生成有超时机制，避免无限等待

## 🧪 测试验证

插件包含完整的测试脚本 `test_plugin.py`，可以验证基本功能：

```bash
python App\Plugins\DoubaoVideoGeneration\test_plugin.py
```

测试内容包括：
- 插件初始化
- 消息识别逻辑
- 提示词提取
- 工具方法验证

## 🔍 故障排除

### 常见问题

1. **插件不响应**
   - 检查插件是否已启用
   - 确认触发条件是否完整满足
   - 查看日志确认错误信息

2. **Cookie过期**
   - 重新获取豆包网站的Cookie
   - 更新config.toml中的cookies配置
   - 重启机器人使配置生效

3. **视频生成失败**
   - 检查网络连接
   - 确认豆包服务可用性
   - 尝试简化提示词内容

### 日志查看

插件会记录详细的执行日志，包括：
- 消息处理过程
- API调用状态
- 错误信息详情

## 📞 技术支持

如遇到问题，可以：
1. 查看插件日志获取详细错误信息
2. 参考现有豆包插件的实现方式
3. 检查网络连接和服务可用性

---

**版本信息**：v1.0.0  
**作者**：XYBot  
**更新时间**：2025-07-16
