# 插件启用状态
enabled = true

# 触发关键词列表
command = ["定时", "提醒我", "闹钟"]

# 使用说明
command_format = """
定时任务使用说明：

📝 文本任务：
- 定时 10分钟后 开会提醒
- 提醒我 明天8点 起床
- 定时 每天18点 下班了

⏰ 支持的时间格式：
- 相对时间：10分钟后、2小时后、30秒后
- 绝对时间：明天8点、18点、下午6点
- 重复任务：每天18点、每天18:30、每小时、每30分钟
- 每周任务：每周一18点、每周五19:30、每周日10:00

📋 管理命令：
- 群任务 - 查看本群所有任务
- 删除群任务1 - 删除第1个任务
- 修改群任务1每周五18:00 - 修改任务时间
- 清空群任务 - 删除本群所有任务
"""

# 限流配置
[rate_limit]
cooldown = 2
