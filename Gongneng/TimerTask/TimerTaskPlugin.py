import os
import json
import asyncio
import time
import re
import random
import base64
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from pathlib import Path

from Plugins._Tools import Tools
from Config.logger import logger
from Core.PluginBase import PluginBase


class TimerTaskPlugin(PluginBase):
    def __init__(self):
        super().__init__()
        # 基本信息（重要：name必须与目录名一致）
        self.name = "TimerTask"
        self.description = "定时发送文本或图片"
        self.version = "3.0.0"
        self.author = "XYBot"

        # 工具类实例
        self.tools = Tools()

        # 加载配置文件
        self.configData = self.tools.returnConfigData(os.path.dirname(__file__))
        self.enabled = self.configData.get('enabled', True)
        self.command = self.configData.get('command', ["定时", "提醒我", "闹钟"])
        self.command_format = self.configData.get('command_format', "使用说明")
        self.cooldown = self.configData.get('rate_limit', {}).get('cooldown', 2)

        # 初始化临时目录
        self.temp_dir = Path("temp/timer_task")
        self.temp_dir.mkdir(parents=True, exist_ok=True)

        # 任务存储和管理
        self.tasks: Dict[str, dict] = {}
        self.task_counter = 0
        self.user_last_request = {}

        # 图片消息缓存 - 用于引用消息处理
        self.image_cache: Dict[str, dict] = {}

        # 设置定时检查任务
        if self.enabled:
            self._setup_task_checker()

    def _setup_task_checker(self):
        """设置定时任务检查器"""
        # 简化实现，使用异步循环检查任务
        if self.enabled:
            asyncio.create_task(self._task_checker_loop())
            logger.info(f"[{self.name}] 已启动定时任务检查器")

        return True

    async def _task_checker_loop(self):
        """定时任务检查循环"""
        while self.enabled:
            try:
                await self._check_and_execute_tasks()
                await asyncio.sleep(60)  # 每分钟检查一次
            except Exception as e:
                logger.error(f"[{self.name}] 任务检查器异常: {e}")
                await asyncio.sleep(60)

    async def handle_message(self, msg) -> bool:
        """处理群聊消息"""
        if not self.enabled:
            return False

        # 设置定时任务检查器（如果还未设置）
        if not hasattr(self, '_task_setup'):
            self._setup_task_checker()
            self._task_setup = True

        # 只处理文本消息
        if msg.type != 1:
            return False

        content = msg.content.strip()
        wxid = msg.roomid if msg.from_group() else msg.sender
        user_wxid = msg.sender

        # 简化查看和管理命令
        if content == "群任务":
            result = await self._list_tasks(wxid, user_wxid)
            await self.dp.sendText(result, wxid, msg.self_wxid)
            return True

        if content == "清空群任务":
            result = await self._clear_all_tasks(wxid, user_wxid)
            await self.dp.sendText(result, wxid, msg.self_wxid)
            return True

        # 简化删除任务命令
        if content.startswith("删除群任务"):
            try:
                cmd_text = content.replace("删除群任务", "").strip()
                index = int(cmd_text)
                result = await self._delete_task_by_index(wxid, user_wxid, index)
                await self.dp.sendText(result, wxid, msg.self_wxid)
            except ValueError:
                await self.dp.sendText("请输入正确的任务编号，如：删除群任务1", wxid, msg.self_wxid)
            return True

        # 简化修改任务时间命令
        if content.startswith("修改群任务"):
            try:
                cmd_text = content.replace("修改群任务", "").strip()
                match = re.match(r'(\d+)\s*(.*)', cmd_text)
                if not match:
                    await self.dp.sendText("格式：修改群任务[编号][新时间]\n例如：修改群任务1每周五18:00", wxid, msg.self_wxid)
                    return True

                index = int(match.group(1))
                new_time_text = match.group(2).strip()

                if not new_time_text:
                    await self.dp.sendText("请指定新时间，如：修改群任务1每周五18:00", wxid, msg.self_wxid)
                    return True

                result = await self._modify_task_time(wxid, user_wxid, index, new_time_text)
                await self.dp.sendText(result, wxid, msg.self_wxid)
            except ValueError:
                await self.dp.sendText("请输入正确的格式，如：修改群任务1每周五18:00", wxid, msg.self_wxid)
            return True

        # 删除复杂的任务提醒功能，简化代码

        # 处理创建文本任务命令
        for cmd in self.command:
            if content.startswith(cmd):
                # 限流检查
                wait_time = self._check_user_limit(wxid, user_wxid)
                if wait_time > 0:
                    await self.dp.sendText(f"请等待 {wait_time:.1f} 秒", wxid, msg.self_wxid)
                    return True

                try:
                    text = content[len(cmd):].strip()
                    if not text:
                        await self.dp.sendText(self.command_format, wxid, msg.self_wxid)
                        return True

                    result = await self._create_task(wxid, user_wxid, text, "text")
                    await self.dp.sendText(result, wxid, msg.self_wxid)

                except Exception as e:
                    logger.error(f"[{self.name}] 异常: {e}")
                    await self.dp.sendText("创建任务失败，请检查格式", wxid, msg.self_wxid)
                return True

        return False

    async def handle_private_message(self, msg) -> bool:
        """处理私聊消息"""
        return await self.handle_message(msg)

    def _generate_task_id(self) -> str:
        """生成任务ID"""
        self.task_counter += 1
        return f"task_{self.task_counter:03d}"

    def _parse_time_expression(self, text: str) -> Optional[dict]:
        """智能解析时间表达式，返回任务信息"""
        text = text.strip()
        now = datetime.now()

        # 1. 相对时间：X分钟后、X小时后、X秒后、X天后
        relative_patterns = [
            (r'(\d+)秒后', 'seconds'),
            (r'(\d+)分钟?后', 'minutes'),
            (r'(\d+)小时后', 'hours'),
            (r'(\d+)天后', 'days')
        ]

        for pattern, unit in relative_patterns:
            match = re.search(pattern, text)
            if match:
                num = int(match.group(1))
                kwargs = {unit: num}
                target_time = now + timedelta(**kwargs)
                return {
                    'type': 'one_time',
                    'target_time': target_time,
                    'description': f"{num}{unit[:-1]}后"
                }

        # 2. 重复任务：每天X点、每小时、每周X
        if text.startswith('每') or '每周' in text or '每天' in text:
            if '每天' in text:
                # 每天HH:MM 或 每天HH点
                time_match = re.search(r'(\d{1,2}):(\d{2})', text)
                if time_match:
                    hour = int(time_match.group(1))
                    minute = int(time_match.group(2))
                    return {
                        'type': 'repeat',
                        'repeat_type': 'daily',
                        'hour': hour,
                        'minute': minute,
                        'description': f"每天{hour}:{minute:02d}"
                    }

                time_match = re.search(r'(\d{1,2})点?', text)
                if time_match:
                    hour = int(time_match.group(1))
                    return {
                        'type': 'repeat',
                        'repeat_type': 'daily',
                        'hour': hour,
                        'minute': 0,
                        'description': f"每天{hour}点"
                    }

            elif '每周' in text:
                # 每周X HH:MM 或 每周X HH点
                weekday_map = {
                    '一': 0, '二': 1, '三': 2, '四': 3, '五': 4, '六': 5, '日': 6, '天': 6
                }

                for day_name, day_num in weekday_map.items():
                    if f'每周{day_name}' in text:
                        # 匹配时间 HH:MM
                        time_match = re.search(r'(\d{1,2}):(\d{2})', text)
                        if time_match:
                            hour = int(time_match.group(1))
                            minute = int(time_match.group(2))
                            return {
                                'type': 'repeat',
                                'repeat_type': 'weekly',
                                'weekday': day_num,
                                'hour': hour,
                                'minute': minute,
                                'description': f"每周{day_name} {hour}:{minute:02d}"
                            }

                        # 匹配时间 HH点
                        time_match = re.search(r'(\d{1,2})点?', text)
                        if time_match:
                            hour = int(time_match.group(1))
                            return {
                                'type': 'repeat',
                                'repeat_type': 'weekly',
                                'weekday': day_num,
                                'hour': hour,
                                'minute': 0,
                                'description': f"每周{day_name} {hour}点"
                            }

            elif '每小时' in text:
                return {
                    'type': 'repeat',
                    'repeat_type': 'hourly',
                    'description': "每小时"
                }

            # 每X分钟
            minute_match = re.search(r'每(\d+)分钟', text)
            if minute_match:
                minutes = int(minute_match.group(1))
                return {
                    'type': 'repeat',
                    'repeat_type': 'interval',
                    'minutes': minutes,
                    'description': f"每{minutes}分钟"
                }

        # 3. 今天时间：18点、下午6点、晚上8点
        time_patterns = [
            (r'(\d{1,2})点', lambda h: h),
            (r'上午(\d{1,2})点?', lambda h: h),
            (r'下午(\d{1,2})点?', lambda h: h + 12 if h < 12 else h),
            (r'晚上(\d{1,2})点?', lambda h: h + 12 if h < 12 else h),
            (r'(\d{1,2}):(\d{2})', lambda h, m=0: h)
        ]

        for pattern, hour_func in time_patterns:
            match = re.search(pattern, text)
            if match:
                if ':' in pattern:
                    hour = int(match.group(1))
                    minute = int(match.group(2))
                else:
                    hour = hour_func(int(match.group(1)))
                    minute = 0

                target_time = now.replace(hour=hour, minute=minute, second=0, microsecond=0)
                if target_time <= now:
                    target_time += timedelta(days=1)

                return {
                    'type': 'one_time',
                    'target_time': target_time,
                    'description': f"今天{hour}:{minute:02d}" if target_time.date() == now.date() else f"明天{hour}:{minute:02d}"
                }

        # 4. 明天时间：明天8点、明天上午9点
        if '明天' in text:
            time_part = re.sub(r'明天\s*', '', text)
            tomorrow = now.date() + timedelta(days=1)

            # 明天X点
            match = re.search(r'(\d{1,2})点?', time_part)
            if match:
                hour = int(match.group(1))
                target_time = datetime.combine(tomorrow, datetime.min.time().replace(hour=hour))
                return {
                    'type': 'one_time',
                    'target_time': target_time,
                    'description': f"明天{hour}点"
                }

        return None

    def _extract_message(self, text: str, time_info: dict) -> str:
        """从文本中提取消息内容 - 简化版本，只移除命令词和时间表达式"""
        message = text

        # 1. 移除命令词
        for cmd in self.command:
            if message.startswith(cmd):
                message = message[len(cmd):].strip()
                break

        # 2. 移除时间表达式 - 只移除开头的时间表达式
        # 相对时间
        message = re.sub(r'^\d+秒后\s*', '', message)
        message = re.sub(r'^\d+分钟?后\s*', '', message)
        message = re.sub(r'^\d+小时后\s*', '', message)
        message = re.sub(r'^\d+天后\s*', '', message)

        # 绝对时间
        message = re.sub(r'^明天\s*\d{1,2}点?\s*', '', message)
        message = re.sub(r'^\d{1,2}点\s*', '', message)
        message = re.sub(r'^上午\s*\d{1,2}点?\s*', '', message)
        message = re.sub(r'^下午\s*\d{1,2}点?\s*', '', message)
        message = re.sub(r'^晚上\s*\d{1,2}点?\s*', '', message)
        message = re.sub(r'^\d{1,2}:\d{2}\s*', '', message)

        # 重复时间
        message = re.sub(r'^每天\s*\d{1,2}:\d{2}\s*', '', message)
        message = re.sub(r'^每天\s*\d{1,2}点?\s*', '', message)
        message = re.sub(r'^每周[一二三四五六日天]\s*\d{1,2}:\d{2}\s*', '', message)
        message = re.sub(r'^每周[一二三四五六日天]\s*\d{1,2}点?\s*', '', message)
        message = re.sub(r'^每小时\s*', '', message)
        message = re.sub(r'^每\d+分钟\s*', '', message)

        # 3. 保持原始格式，只清理首尾空白
        message = message.strip()

        return message if message else "提醒"

    async def _create_task(self, wxid: str, user_wxid: str, text: str, content_type: str = "text", content_data: dict = None) -> str:
        """创建定时任务"""
        # 智能提取时间
        time_info = self._parse_time_expression(text)
        if not time_info:
            return "时间格式不正确\n支持格式：10分钟后、明天8点、每天18点等"

        task_id = self._generate_task_id()

        # 根据内容类型设置任务数据
        if content_type == "text":
            # 提取消息内容（去掉时间部分）
            message = self._extract_message(text, time_info)
            if not message:
                message = "提醒"

            content_info = {
                'type': 'text',
                'content': message
            }
        elif content_type == "image":
            if not content_data:
                return "图片数据缺失"

            content_info = {
                'type': 'image',
                'image_base64': content_data.get('image_base64'),
                'image_path': content_data.get('image_path'),
                'xml_content': content_data.get('xml_content')
            }
        else:
            return "不支持的内容类型"

        if time_info['type'] == 'one_time':
            target_time = time_info['target_time']
            if target_time <= datetime.now():
                return "设置的时间已经过去了"

            task_data = {
                'id': task_id,
                'type': 'one_time',
                'wxid': wxid,
                'user_wxid': user_wxid,
                'content': content_info,
                'target_time': target_time.isoformat(),
                'created_time': datetime.now().isoformat(),
                'status': 'active',
                'description': time_info['description']
            }

            result = "好的，已设置"

        else:  # repeat
            task_data = {
                'id': task_id,
                'type': 'repeat',
                'wxid': wxid,
                'user_wxid': user_wxid,
                'content': content_info,
                'repeat_config': time_info,
                'created_time': datetime.now().isoformat(),
                'status': 'active',
                'last_run': None,
                'run_count': 0,
                'description': time_info['description']
            }

            result = "好的，已设置"

        self.tasks[task_id] = task_data

        # 记录任务创建信息
        logger.info(f"[{self.name}] 创建任务 {task_id} - 群组: {wxid}, 用户: {user_wxid}, 类型: {content_type}")

        return result

    def _format_time_diff(self, time_diff: timedelta) -> str:
        """格式化时间差"""
        total_seconds = int(time_diff.total_seconds())

        if total_seconds < 60:
            return f"{total_seconds}秒后"
        elif total_seconds < 3600:
            minutes = total_seconds // 60
            return f"{minutes}分钟后"
        elif total_seconds < 86400:
            hours = total_seconds // 3600
            minutes = (total_seconds % 3600) // 60
            if minutes > 0:
                return f"{hours}小时{minutes}分钟后"
            return f"{hours}小时后"
        else:
            days = total_seconds // 86400
            hours = (total_seconds % 86400) // 3600
            if hours > 0:
                return f"{days}天{hours}小时后"
            return f"{days}天后"

    def _calculate_next_repeat_run(self, repeat_config: dict) -> Optional[datetime]:
        """计算重复任务的下次执行时间"""
        now = datetime.now()

        if repeat_config['repeat_type'] == 'daily':
            hour = repeat_config['hour']
            minute = repeat_config.get('minute', 0)
            next_run = now.replace(hour=hour, minute=minute, second=0, microsecond=0)
            if next_run <= now:
                next_run += timedelta(days=1)
            return next_run

        elif repeat_config['repeat_type'] == 'weekly':
            target_weekday = repeat_config['weekday']  # 0=周一, 6=周日
            hour = repeat_config['hour']
            minute = repeat_config.get('minute', 0)

            # 计算当前是周几 (0=周一, 6=周日)
            current_weekday = now.weekday()

            # 计算距离目标星期几还有几天
            days_ahead = target_weekday - current_weekday

            # 如果是今天但时间已过，或者是之前的日期，则计算下周
            if days_ahead < 0 or (days_ahead == 0 and now.time() >= datetime.min.time().replace(hour=hour, minute=minute)):
                days_ahead += 7

            next_run = now.replace(hour=hour, minute=minute, second=0, microsecond=0) + timedelta(days=days_ahead)
            return next_run

        elif repeat_config['repeat_type'] == 'hourly':
            next_run = now.replace(minute=0, second=0, microsecond=0) + timedelta(hours=1)
            return next_run

        elif repeat_config['repeat_type'] == 'interval':
            minutes = repeat_config['minutes']
            next_run = now + timedelta(minutes=minutes)
            return next_run

        return None

    async def _list_tasks(self, wxid: str, user_wxid: str) -> str:
        """列出用户在当前群组的所有任务"""
        # 只显示当前群组的任务，确保群组隔离
        user_tasks = [task for task in self.tasks.values()
                     if task['wxid'] == wxid and task['user_wxid'] == user_wxid and task['status'] == 'active']

        if not user_tasks:
            return "📝 本群还没有设置任何定时任务"

        result = f"📋 本群定时任务（{len(user_tasks)}个）\n\n"

        for i, task in enumerate(user_tasks, 1):
            content = task.get('content', {})
            content_desc = content.get('content', '[图片]') if content.get('type') == 'text' else '[图片]'

            # 简化显示，只显示关键信息
            if len(content_desc) > 20:
                content_desc = content_desc[:20] + "..."

            if task['type'] == 'one_time':
                target_time = datetime.fromisoformat(task['target_time'])
                time_diff = target_time - datetime.now()
                if time_diff.total_seconds() > 0:
                    time_desc = self._format_time_diff(time_diff)
                    result += f"{i}. ⏰ {task['description']} ({time_desc}) - {content_desc}\n"
                else:
                    result += f"{i}. ⏰ {task['description']} (即将执行) - {content_desc}\n"
            else:
                next_run = self._calculate_next_repeat_run(task['repeat_config'])
                next_desc = next_run.strftime('%m-%d %H:%M') if next_run else "计算中"
                run_count = task.get('run_count', 0)
                result += f"{i}. 🔄 {task['description']} (下次: {next_desc}) - {content_desc}\n"
                if run_count > 0:
                    result += f"    已执行{run_count}次\n"

        result += "💡 管理命令：\n"
        result += "• 删除群任务[编号] - 删除指定任务\n"
        result += "• 修改群任务[编号][新时间] - 修改任务时间\n"
        result += "• 清空群任务 - 删除所有任务"
        return result.strip()

    async def _delete_task_by_index(self, wxid: str, user_wxid: str, index: int) -> str:
        """按编号删除任务（仅限当前群组）"""
        user_tasks = [task for task in self.tasks.values()
                     if task['wxid'] == wxid and task['user_wxid'] == user_wxid and task['status'] == 'active']

        if not user_tasks:
            return "本群没有任何任务可以删除"

        if index < 1 or index > len(user_tasks):
            return f"编号错误，请输入1-{len(user_tasks)}之间的数字"

        task = user_tasks[index - 1]
        task['status'] = 'deleted'

        content = task.get('content', {})
        content_desc = content.get('content', '[图片]') if content.get('type') == 'text' else '[图片]'
        return f"✅ 已删除任务：{task['description']} - {content_desc}"

    async def _clear_all_tasks(self, wxid: str, user_wxid: str) -> str:
        """清空用户在当前群组的所有任务"""
        user_tasks = [task for task in self.tasks.values()
                     if task['wxid'] == wxid and task['user_wxid'] == user_wxid and task['status'] == 'active']

        if not user_tasks:
            return "本群没有任何任务需要清空"

        count = 0
        for task in user_tasks:
            task['status'] = 'deleted'
            count += 1

        return f"✅ 已清空本群的所有任务（{count}个）"

    async def _modify_task_time(self, wxid: str, user_wxid: str, index: int, new_time_text: str) -> str:
        """修改任务的时间（仅限当前群组）"""
        user_tasks = [task for task in self.tasks.values()
                     if task['wxid'] == wxid and task['user_wxid'] == user_wxid and task['status'] == 'active']

        if not user_tasks:
            return "本群没有任何任务可以修改"

        if index < 1 or index > len(user_tasks):
            return f"编号错误，请输入1-{len(user_tasks)}之间的数字"

        task = user_tasks[index - 1]

        # 解析新的时间信息
        new_time_info = self._parse_time_expression(new_time_text)
        if not new_time_info:
            return "新时间格式不正确\n支持格式：10分钟后、明天8点、每周五18:00等"

        # 保存原始信息用于回滚
        old_description = task['description']

        try:
            # 更新任务时间信息
            if new_time_info['type'] == 'one_time':
                target_time = new_time_info['target_time']
                if target_time <= datetime.now():
                    return "设置的时间已经过去了"

                task['type'] = 'one_time'
                task['target_time'] = target_time.isoformat()
                task['description'] = new_time_info['description']

                # 移除重复任务相关字段
                task.pop('repeat_config', None)
                task.pop('last_run', None)
                task.pop('run_count', None)

                time_diff = target_time - datetime.now()
                time_desc = self._format_time_diff(time_diff)
                result_msg = f"⏰ {new_time_info['description']} ({time_desc})"

            else:  # repeat
                task['type'] = 'repeat'
                task['repeat_config'] = new_time_info
                task['description'] = new_time_info['description']
                task['last_run'] = None
                task['run_count'] = 0

                # 移除一次性任务相关字段
                task.pop('target_time', None)

                next_run = self._calculate_next_repeat_run(new_time_info)
                next_desc = next_run.strftime('%m-%d %H:%M') if next_run else "计算中"
                result_msg = f"🔄 {new_time_info['description']}\n⏰ 下次: {next_desc}"

            content = task.get('content', {})
            content_desc = content.get('content', '[图片]') if content.get('type') == 'text' else '[图片]'

            return f"✅ 任务时间已修改\n📝 内容: {content_desc}\n{result_msg}"

        except Exception as e:
            # 回滚
            task['description'] = old_description
            logger.error(f"[{self.name}] 修改任务时间失败: {e}")
            return "修改失败，请重试"

    # 删除复杂的任务提醒功能，简化代码

    def _check_user_limit(self, wxid: str, user_wxid: str) -> float:
        user_key = f"{wxid}_{user_wxid}"
        last_request = self.user_last_request.get(user_key, 0)
        current_time = time.time()
        elapsed = current_time - last_request
        self.user_last_request[user_key] = current_time

        if elapsed < self.cooldown:
            return self.cooldown - elapsed

        return 0

    async def _check_and_execute_tasks(self):
        """定时检查任务执行 - 高精度时间检查"""
        if not self.enabled:
            return

        now = datetime.now()
        executed_tasks = []

        for task_id, task in list(self.tasks.items()):
            if task['status'] != 'active':
                continue

            try:
                if task['type'] == 'one_time':
                    target_time = datetime.fromisoformat(task['target_time'])
                    # 精确到秒的时间检查
                    if now >= target_time:
                        await self._execute_task(task)
                        task['status'] = 'completed'
                        task['completed_time'] = now.isoformat()
                        executed_tasks.append(task_id)

                elif task['type'] == 'repeat':
                    # 检查是否到了执行时间
                    should_execute = self._should_execute_repeat_task(task, now)
                    if should_execute:
                        await self._execute_task(task)
                        task['last_run'] = now.isoformat()
                        task['run_count'] = task.get('run_count', 0) + 1
                        executed_tasks.append(task_id)

            except Exception as e:
                logger.error(f"[{self.name}] 执行任务 {task_id} 失败: {e}")

        # 每分钟清理一次
        if now.second == 0:
            await self._cleanup_tasks()
            self._cleanup_image_cache()

    def _should_execute_repeat_task(self, task: dict, now: datetime) -> bool:
        """判断重复任务是否应该执行"""
        repeat_config = task['repeat_config']
        last_run_str = task.get('last_run')

        if repeat_config['repeat_type'] == 'daily':
            target_hour = repeat_config['hour']
            target_minute = repeat_config.get('minute', 0)

            # 检查是否到了指定时间（精确到分钟）
            if now.hour == target_hour and now.minute == target_minute:
                # 检查今天是否已经执行过
                if last_run_str:
                    last_run = datetime.fromisoformat(last_run_str)
                    if last_run.date() == now.date():
                        return False
                return True

        elif repeat_config['repeat_type'] == 'weekly':
            target_weekday = repeat_config['weekday']  # 0=周一, 6=周日
            target_hour = repeat_config['hour']
            target_minute = repeat_config.get('minute', 0)

            # 检查是否是目标星期几和时间
            current_weekday = now.weekday()
            if (current_weekday == target_weekday and
                now.hour == target_hour and
                now.minute == target_minute):

                # 检查本周是否已经执行过
                if last_run_str:
                    last_run = datetime.fromisoformat(last_run_str)
                    # 计算本周的开始时间（周一00:00）
                    week_start = now - timedelta(days=now.weekday())
                    week_start = week_start.replace(hour=0, minute=0, second=0, microsecond=0)

                    if last_run >= week_start:
                        return False
                return True

        elif repeat_config['repeat_type'] == 'hourly':
            # 每小时的整点执行
            if now.minute == 0:
                if last_run_str:
                    last_run = datetime.fromisoformat(last_run_str)
                    # 检查这个小时是否已经执行过
                    if last_run.hour == now.hour and last_run.date() == now.date():
                        return False
                return True

        elif repeat_config['repeat_type'] == 'interval':
            minutes = repeat_config['minutes']
            if last_run_str:
                last_run = datetime.fromisoformat(last_run_str)
                time_diff = now - last_run
                if time_diff.total_seconds() >= minutes * 60:
                    return True
            else:
                # 首次执行
                return True

        return False

    async def _execute_task(self, task: dict):
        """执行任务 - 严格按群组隔离"""
        try:
            wxid = task['wxid']  # 任务创建时的群组ID
            user_wxid = task['user_wxid']  # 任务创建者
            content = task.get('content', {})
            content_type = content.get('type', 'text')

            # 记录任务执行信息
            logger.info(f"[{self.name}] 执行任务 {task['id']} - 群组: {wxid}, 用户: {user_wxid}, 类型: {content_type}")

            if content_type == 'text':
                # 发送文本消息到指定群组
                message = content.get('content', '提醒')
                await self.dp.sendText(message, wxid, task.get('self_wxid', ''))
                logger.info(f"[{self.name}] 文本任务执行成功 - 群组: {wxid}")

            elif content_type == 'image':
                # 发送图片消息到指定群组
                image_base64 = content.get('image_base64')
                image_path = content.get('image_path')

                if image_base64:
                    # 优先使用base64
                    await self.dp.sendImage(f"data:image/jpeg;base64,{image_base64}", wxid, task.get('self_wxid', ''))
                    logger.info(f"[{self.name}] 图片任务执行成功(base64) - 群组: {wxid}")
                elif image_path and os.path.exists(image_path):
                    # 使用文件路径
                    await self.dp.sendImage(image_path, wxid, task.get('self_wxid', ''))
                    logger.info(f"[{self.name}] 图片任务执行成功(文件) - 群组: {wxid}")
                else:
                    # 图片数据丢失，发送错误提醒到原群组
                    await self.dp.sendText("⚠️ 定时图片已丢失，无法发送", wxid, task.get('self_wxid', ''))
                    logger.warning(f"[{self.name}] 图片数据丢失 - 群组: {wxid}")

        except Exception as e:
            logger.error(f"[{self.name}] 任务执行失败 - 群组: {task.get('wxid', 'unknown')}, 错误: {e}")
            try:
                # 错误提醒也发送到原群组
                await self.dp.sendText("⚠️ 定时任务执行失败", task['wxid'], task.get('self_wxid', ''))
            except:
                pass

    async def _cleanup_tasks(self):
        """清理已删除和过期的任务"""
        try:
            to_remove = []
            now = datetime.now()

            for task_id, task in self.tasks.items():
                # 删除已标记为删除的任务
                if task['status'] == 'deleted':
                    to_remove.append(task_id)

                # 删除已完成的一次性任务（保留1小时）
                elif task['status'] == 'completed' and task['type'] == 'one_time':
                    completed_time = datetime.fromisoformat(task.get('completed_time', task['target_time']))
                    if now - completed_time > timedelta(hours=1):
                        to_remove.append(task_id)

            # 从内存中移除
            for task_id in to_remove:
                if task_id in self.tasks:
                    del self.tasks[task_id]

        except Exception as e:
            logger.error(f"[{self.name}] 清理任务失败: {e}")

    def _cleanup_image_cache(self):
        """清理过期的图片缓存"""
        try:
            current_time = time.time()
            expired_keys = []

            # 清理超过1小时的图片缓存
            for msg_id, cache_info in self.image_cache.items():
                if current_time - cache_info['timestamp'] > 3600:  # 1小时
                    expired_keys.append(msg_id)

            for key in expired_keys:
                del self.image_cache[key]

            if expired_keys:
                logger.info(f"[{self.name}] 清理过期图片缓存: {len(expired_keys)}个")

        except Exception as e:
            logger.error(f"[{self.name}] 清理图片缓存失败: {e}")
