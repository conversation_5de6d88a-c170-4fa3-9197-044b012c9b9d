# DailyBrowserTask 插件

## 功能说明
定时浏览器任务插件，可以在指定时间自动启动Chrome浏览器访问配置的网址列表，利用浏览器缓存的登录状态和数据。

## 主要功能
1. **定时任务**：每日凌晨0点（可配置）自动执行浏览器任务
2. **浏览器自动化**：启动Chrome浏览器并访问指定网址
3. **自动关闭**：运行指定时间后自动关闭浏览器进程
4. **手动执行**：支持通过关键词手动触发浏览器任务
5. **状态查询**：查看定时任务配置和状态

## 使用方法

### 基本命令
- `状态重置` - 手动执行浏览器任务

### 工作原理
1. 插件在初始化时注册定时任务到系统调度器
2. 每日指定时间自动执行浏览器任务
3. 启动Chrome浏览器并同时打开所有配置的网址
4. 等待指定时间后自动关闭浏览器进程
5. 利用浏览器现有的cookie和登录状态

## 配置说明

### config.toml
```toml
# 插件启用状态
enabled = true

# Chrome浏览器路径
chrome_path = "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe"

# 要访问的网址列表
target_urls = [
    "https://www.doubao.com/chat/4498442627796994",
    "https://jimeng.jianying.com/ai-tool/generate/?type=image"
]

# 定时任务执行时间
task_hour = 0      # 小时 (0-23)
task_minute = 0    # 分钟 (0-59)

# 浏览器运行等待时间（秒）
wait_time = 120    # 2分钟

# 触发关键词列表
keywords = [
    "状态重置"
]
```

### 配置项说明
- `enabled`: 插件启用状态
- `chrome_path`: Chrome浏览器可执行文件的完整路径
- `target_urls`: 要访问的网址列表，支持多个网址
- `task_hour/task_minute`: 定时任务执行时间（24小时制）
- `wait_time`: 浏览器运行时间（秒），建议120秒（2分钟）
- `keywords`: 触发插件功能的关键词列表

## 技术特性

### 浏览器启动参数
- `--new-window`: 在新窗口中打开
- `--disable-web-security`: 禁用web安全检查（可选）
- `--disable-features=VizDisplayCompositor`: 提高兼容性

### 进程管理
- 自动记录浏览器进程PID
- 优雅关闭：先尝试terminate()
- 强制关闭：超时后使用kill()
- 插件关闭时自动清理进程

### 错误处理
- Chrome路径检查
- 进程启动失败处理
- 进程关闭异常处理
- 完整的日志记录

## 安装和部署

### 1. 文件结构
```
App/Plugins/DailyBrowserTask/
├── DailyBrowserTaskPlugin.py  # 插件主文件
├── config.toml               # 配置文件
├── test_plugin.py           # 测试脚本
└── README.md               # 说明文档
```

### 2. 依赖要求
- Python 3.7+
- Chrome浏览器已安装
- DPbot框架环境

### 3. 配置步骤
1. 确认Chrome浏览器路径正确
2. 配置要访问的网址列表
3. 设置定时任务执行时间
4. 重启DPbot使插件生效

### 4. 测试方法
```bash
# 在App目录下运行测试脚本
python Plugins\DailyBrowserTask\test_plugin.py
```

## 注意事项

1. **Chrome路径**：请确保Chrome浏览器路径正确，不同系统可能路径不同
2. **网络访问**：确保目标网址可以正常访问
3. **登录状态**：插件会使用Chrome的默认用户配置，包括已保存的登录状态
4. **资源占用**：浏览器运行期间会占用系统资源，建议合理设置运行时间
5. **防火墙**：确保Chrome可以正常访问网络

## 故障排除

### 常见问题
1. **Chrome启动失败**：检查Chrome路径是否正确
2. **网址无法访问**：检查网络连接和URL有效性
3. **进程无法关闭**：检查系统权限和进程状态
4. **定时任务不执行**：检查插件是否正确加载和启用

### 日志查看
插件运行时会输出详细日志，包括：
- 定时任务注册状态
- 浏览器启动和关闭过程
- 错误信息和异常处理

## 版本信息
- 版本：1.0.0
- 作者：DPbot
- 更新日期：2025-07-16
