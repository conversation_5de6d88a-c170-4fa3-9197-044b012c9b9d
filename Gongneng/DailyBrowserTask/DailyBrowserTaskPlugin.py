import os
import asyncio
import subprocess
import time
from pathlib import Path
from typing import Optional
from Plugins._Tools import Tools
from Config.logger import logger
from Core.PluginBase import PluginBase


class DailyBrowserTaskPlugin(PluginBase):
    def __init__(self):
        super().__init__()
        # 基本信息（重要：name必须与目录名一致）
        self.name = "DailyBrowserTask"
        self.description = "定时浏览器任务插件"
        self.version = "1.0.0"
        self.author = "DPbot"

        # 工具类实例
        self.tools = Tools()

        # 加载配置文件
        self.configData = self.tools.returnConfigData(os.path.dirname(__file__))
        
        # 配置项
        self.enabled = self.configData.get('enabled', True)
        self.chrome_path = self.configData.get('chrome_path', r'C:\Program Files\Google\Chrome\Application\chrome.exe')
        self.target_urls = self.configData.get('target_urls', [
            'https://www.doubao.com/chat/4498442627796994',
            'https://jimeng.jianying.com/ai-tool/generate/?type=image'
        ])
        self.task_hour = self.configData.get('task_hour', 0)
        self.task_minute = self.configData.get('task_minute', 0)
        self.wait_time = self.configData.get('wait_time', 120)  # 等待时间（秒）
        self.keywords = self.configData.get('keywords', ['状态重置'])
        
        # 浏览器进程管理
        self.browser_process: Optional[subprocess.Popen] = None
        
        # 设置定时任务
        if self.enabled:
            self._setup_scheduled_task()

    def _setup_scheduled_task(self):
        """设置定时任务"""
        if not self._message_handler:
            logger.warning(f"[{self.name}] MessageHandler未设置，稍后将重试设置定时任务")
            return False

        success = self.add_scheduled_task(
            "daily_browser_visit",
            self.task_hour,
            self.task_minute,
            self._execute_browser_task
        )
        
        if success:
            logger.info(f"[{self.name}] 已设置定时浏览器任务，时间: {self.task_hour:02d}:{self.task_minute:02d}")
        else:
            logger.error(f"[{self.name}] 设置定时浏览器任务失败")
        
        return success

    async def handle_message(self, msg) -> bool:
        """处理消息"""
        if not self.enabled:
            return False
            
        # 设置定时任务（如果还未设置）
        if self._message_handler and not hasattr(self, '_task_setup'):
            self._setup_scheduled_task()
            self._task_setup = True
            
        # 只处理文本消息
        if msg.type != 1:
            return False

        # 检查关键词匹配
        if self.tools.judgeEqualListWord(msg.content, self.keywords):
            logger.info(f"[{self.name}] 处理消息: {msg.content}")
            
            target_id = msg.roomid if msg.from_group() else msg.sender
            
            if msg.content == "状态重置":
                # 手动执行浏览器任务
                await self.dp.sendText("🔄 开始执行状态重置任务...", target_id, msg.self_wxid)
                result = await self._execute_browser_task()
                await self.dp.sendText(result, target_id, msg.self_wxid)
                return True

        return False

    async def handle_private_message(self, msg) -> bool:
        """处理私聊消息"""
        return await self.handle_message(msg)

    async def _execute_browser_task(self) -> str:
        """执行浏览器任务"""
        try:
            logger.info(f"[{self.name}] 开始执行定时浏览器任务")
            
            # 检查Chrome路径是否存在
            if not os.path.exists(self.chrome_path):
                error_msg = f"❌ Chrome浏览器路径不存在：{self.chrome_path}"
                logger.error(f"[{self.name}] {error_msg}")
                return error_msg
            
            # 启动Chrome浏览器
            result = await self._start_chrome_browser()
            
            return result
            
        except Exception as e:
            error_msg = f"❌ 执行浏览器任务失败：{str(e)}"
            logger.error(f"[{self.name}] {error_msg}", exc_info=True)
            return error_msg

    async def _start_chrome_browser(self) -> str:
        """启动Chrome浏览器并访问网址"""
        try:
            # 构建Chrome启动参数
            chrome_args = [
                self.chrome_path,
                '--new-window',  # 新窗口
                '--disable-web-security',  # 禁用web安全（可选）
                '--disable-features=VizDisplayCompositor',  # 提高兼容性
            ]
            
            # 添加要访问的网址
            chrome_args.extend(self.target_urls)
            
            logger.info(f"[{self.name}] 启动Chrome浏览器，参数：{' '.join(chrome_args)}")
            
            # 启动Chrome进程
            self.browser_process = subprocess.Popen(
                chrome_args,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
            )
            
            logger.info(f"[{self.name}] Chrome浏览器已启动，PID：{self.browser_process.pid}")
            
            # 等待指定时间
            logger.info(f"[{self.name}] 等待 {self.wait_time} 秒...")
            await asyncio.sleep(self.wait_time)
            
            # 关闭浏览器进程
            await self._close_browser()
            
            success_msg = f"✅ 浏览器任务执行完成\n"
            success_msg += f"🌐 已访问 {len(self.target_urls)} 个网址\n"
            success_msg += f"⏱️ 运行时间：{self.wait_time} 秒\n"
            success_msg += f"🕐 执行时间：{time.strftime('%Y-%m-%d %H:%M:%S')}"
            
            logger.info(f"[{self.name}] 浏览器任务执行完成")
            return success_msg
            
        except Exception as e:
            # 确保在出错时也关闭浏览器
            await self._close_browser()
            raise e

    async def _close_browser(self):
        """关闭浏览器进程"""
        try:
            if self.browser_process and self.browser_process.poll() is None:
                logger.info(f"[{self.name}] 正在关闭Chrome浏览器进程 PID：{self.browser_process.pid}")
                
                # 尝试优雅关闭
                self.browser_process.terminate()
                
                # 等待进程结束
                try:
                    self.browser_process.wait(timeout=10)
                    logger.info(f"[{self.name}] Chrome浏览器进程已正常关闭")
                except subprocess.TimeoutExpired:
                    # 强制关闭
                    logger.warning(f"[{self.name}] Chrome浏览器进程未响应，强制关闭")
                    self.browser_process.kill()
                    self.browser_process.wait()
                    logger.info(f"[{self.name}] Chrome浏览器进程已强制关闭")
                
                self.browser_process = None
                
        except Exception as e:
            logger.error(f"[{self.name}] 关闭浏览器进程时出错：{e}")

    async def close(self):
        """插件关闭时的清理"""
        try:
            # 关闭浏览器进程
            await self._close_browser()
            logger.info(f"[{self.name}] 插件清理完成")
        except Exception as e:
            logger.error(f"[{self.name}] 插件清理时出错：{e}")
