# 插件启用状态
enabled = true

# Chrome浏览器路径
chrome_path = "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe"

# 要访问的网址列表
target_urls = [
    "https://www.doubao.com/chat/4498442627796994",
    "https://jimeng.jianying.com/ai-tool/generate/?type=image"
]

# 定时任务执行时间
task_hour = 0      # 小时 (0-23)
task_minute = 1    # 分钟 (0-59)

# 浏览器运行等待时间（秒）
wait_time = 120    # 2分钟

# 触发关键词列表
keywords = [
    "状态重置"
]

# 插件描述
description = """
⏰ 定时浏览器任务插件

🎯 主要功能：
• 每日凌晨0点自动启动Chrome浏览器
• 依次访问配置的网址列表
• 利用浏览器缓存的登录状态和数据
• 运行2分钟后自动关闭浏览器

📝 使用方法：
• 发送 "状态重置" - 手动执行浏览器任务

⚙️ 配置说明：
• chrome_path: Chrome浏览器可执行文件路径
• target_urls: 要访问的网址列表
• task_hour/task_minute: 定时任务执行时间
• wait_time: 浏览器运行时间（秒）
"""
