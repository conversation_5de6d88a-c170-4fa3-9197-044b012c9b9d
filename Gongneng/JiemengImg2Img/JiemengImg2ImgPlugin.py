from Plugins._Tools import Tools
from Plugins._Tools.ReferenceMessageHandler import ReferenceMessageHandler
from Plugins._Tools.ReferenceMessageErrorHandler import ReferenceMessageErrorHandler
from Config.logger import logger
from Core.PluginBase import PluginBase
import os
import json
import asyncio
import time
import uuid
import urllib.parse
import xml.etree.ElementTree as ET
from pathlib import Path
from typing import Optional, Dict

class JiemengImg2ImgPlugin(PluginBase, ReferenceMessageHandler):
    def __init__(self):
        super().__init__()
        self.name = "JiemengImg2Img"
        self.description = "即梦图生图插件"
        self.version = "1.0.0"
        self.author = "移植自XYBot"

        self.tools = Tools()
        self.temp_dir = Path("App/Plugins/JiemengImg2Img/temp")
        self.temp_dir.mkdir(parents=True, exist_ok=True)

        self.configData = self.tools.returnConfigData(os.path.dirname(__file__))
        self._load_config()

        self.user_last_request = {}
        self._token_bucket = self.rate_limit["bucket_size"]
        self._last_token_time = time.time()

    def _load_config(self):
        """加载配置"""
        self.enabled = self.configData.get('enabled', True)
        self.command = self.configData.get('command', ["即梦"])
        self.command_format = self.configData.get('command_format', "")
        self.cooldown = self.configData.get('cooldown', 30)

        api_config = self.configData.get('API', {})
        self.device_id = api_config.get('device_id', "86229099759449")
        self.iid = api_config.get('iid', "86229099218793")

        self.rate_limit = self.configData.get('rate_limit', {
            "tokens_per_second": 0.1,
            "bucket_size": 3
        })

        self.dreamina_api_url = "https://dreamina-app-hl.jianying.com"
        self.base_headers = {
            "Connection": "keep-alive",
            "lan": "zh-hans",
            "loc": "cn",
            "pf": "0",
            "vr": "*********",
            "appvr": "1.3.8",
            "tdid": self.device_id,
            "sign-ver": "1",
            "appid": "581595",
            "ac": "wifi",
            "Cache-Control": "no-cache",
            "sysvr": "29",
            "ch": "oppo_64_581595",
            "uid": "329468416627917",
            "COMPRESSED": "1",
            "did": "00000000-50f9-90d5-ffff-ffffef05ac4a",
            "model": "UGl4ZWw=",
            "manu": "R29vZ2xl",
            "GPURender": "QWRyZW5vIChUTSkgNzUw",
            "HDR-TDID": self.device_id,
            "HDR-TIID": self.iid,
            "version_code": "*********",
            "HDR-Sign-Ver": "1",
            "x-vc-bdturing-sdk-version": "3.7.2.cn",
            "sdk-version": "2",
            "X-Tt-Token": "00a00b34aff3a919ca8990897d72a6685d04401ee9474e28c90221f554153c0bdd530fb8e99a032de30269120da2b4868ae53f9e54f0bd7e0dad469840d90199f9dcc3d5c00a9f1aa9a6f954669c4fcc557868d982f18a88c0b6303130a5f6bd2c9b5-1.0.1",
            "passport-sdk-version": "50561",
            "commerce-sign-version": "v1",
            "User-Agent": "com.bytedance.dreamina/1381600 (Linux; U; Android 10; zh_CN; Pixel; Build/NHG47O; Cronet/TTNetVersion:22f14a0c 2024-07-09 QuicVersion:46688bb4 2022-11-28)",
            "Accept-Encoding": "gzip, deflate",
        }

    def _update_headers_time(self, headers: dict) -> dict:
        """更新请求头中的时间戳"""
        timestamp = str(int(time.time()))
        headers["device-time"] = timestamp
        headers["HDR-Device-Time"] = timestamp
        return headers

    def _check_user_limit(self, wxid: str, user_wxid: str) -> float:
        """检查用户请求限制"""
        current_time = time.time()
        user_key = f"{wxid}_{user_wxid}"

        if user_key in self.user_last_request:
            time_diff = current_time - self.user_last_request[user_key]
            if time_diff < self.cooldown:
                return self.cooldown - time_diff

        self.user_last_request[user_key] = current_time
        return 0

    async def _acquire_token(self) -> bool:
        """获取令牌桶令牌"""
        current_time = time.time()
        time_passed = current_time - self._last_token_time

        tokens_to_add = time_passed * self.rate_limit["tokens_per_second"]
        self._token_bucket = min(self.rate_limit["bucket_size"],
                                self._token_bucket + tokens_to_add)
        self._last_token_time = current_time

        if self._token_bucket >= 1:
            self._token_bucket -= 1
            return True
        return False

    async def handle_message(self, msg) -> bool:
        """处理群聊消息"""
        if not self.enabled:
            return False

        # 处理引用消息（type=49）
        if msg.type != 49:
            return False

        content = str(msg.content).strip()
        if not self._is_reference_message(content):
            return False

        prompt = self._extract_prompt_from_reference(content)
        if not prompt:
            return False

        command_parts = prompt.split(" ", 1)
        if command_parts[0] not in self.command:
            return False

        target_id = msg.roomid if msg.from_group() else msg.sender

        # 如果没有提供描述文本
        if len(command_parts) == 1:
            await self.dp.sendText(self.command_format, target_id, msg.self_wxid)
            return True

        # 检查用户请求限制
        wait_time = self._check_user_limit(msg.roomid or msg.sender, msg.sender)
        if wait_time > 0:
            await self.dp.sendText(f"慢点慢点，等 {wait_time:.1f} 秒再来", target_id, msg.self_wxid)
            return True

        # 检查令牌桶
        if not await self._acquire_token():
            await self.dp.sendText("忙不过来了，歇会儿", target_id, msg.self_wxid)
            return True

        # 获取引用的图片
        reference_image = await self._get_reference_image(content, msg.self_wxid)
        if not reference_image:
            await ReferenceMessageErrorHandler.handle_image_not_found(
                self.dp, target_id, msg.self_wxid
            )
            return True

        # 生成并下载图片
        prompt_text = " ".join(command_parts[1:])
        image_paths = await self._generate_img2img(reference_image, prompt_text)

        if image_paths:
            for image_path in image_paths:
                await self.dp.sendImage(image_path, target_id, msg.self_wxid)
                try:
                    os.remove(image_path)
                except:
                    pass
        else:
            await ReferenceMessageErrorHandler.handle_processing_error(
                self.dp, target_id, msg.self_wxid, "图生图"
            )

        return True

    async def handle_private_message(self, msg) -> bool:
        """处理私聊消息"""
        return await self.handle_message(msg)

    def _is_reference_message(self, content: str) -> bool:
        """检查是否是引用消息"""
        return content.startswith('<?xml') and 'refermsg' in content

    def _extract_prompt_from_reference(self, content: str) -> Optional[str]:
        """从引用消息中提取提示词"""
        try:
            root = ET.fromstring(content)
            appmsg = root.find('appmsg')
            if appmsg is not None:
                title = appmsg.find('title')
                if title is not None and title.text:
                    return title.text.strip()
            return None
        except Exception as e:
            logger.error(f"[{self.name}] 提取提示词失败: {e}")
            return None

    # 使用基类的 _get_reference_image 和 _download_image_with_cdn_api 方法

    async def _generate_img2img(self, image_data: bytes, prompt: str) -> list:
        """执行图生图"""
        temp_image_path = None
        try:
            temp_image_path = await self._save_temp_image(image_data)
            if not temp_image_path:
                return []

            image_url = await self._upload_image(temp_image_path)
            if not image_url:
                return []

            task_id = await self._create_img2img_task(image_url, prompt)
            if not task_id:
                return []

            result = await self._poll_task_status(task_id)
            if not result:
                return []

            return await self._download_images(result)
        except Exception as e:
            logger.error(f"[{self.name}] 图生图失败: {e}")
            return []
        finally:
            if temp_image_path:
                try:
                    os.remove(temp_image_path)
                except:
                    pass

    async def _get_upload_token(self) -> Optional[dict]:
        """获取上传Token"""
        try:
            url = f"{self.dreamina_api_url}/mweb/v1/get_upload_token"
            params = self._build_common_params()

            headers = self.base_headers.copy()
            headers = self._update_headers_time(headers)
            headers["Content-Type"] = "application/json; charset=utf-8"
            headers["Host"] = "dreamina-app-hl.jianying.com"

            data = {"scene": 2}

            url_with_params = f"{url}?{urllib.parse.urlencode(params)}"
            result = await self.tools.async_post(url_with_params, json_data=data, headers=headers)

            if result and result.get("ret") == "0":
                return result.get("data")
            else:
                logger.error(f"[{self.name}] 获取上传Token失败: {result}")
                return None

        except Exception as e:
            logger.error(f"[{self.name}] 获取上传Token异常: {e}")
            return None

    async def _apply_image_upload(self, token_data: dict) -> Optional[dict]:
        """申请图片上传地址"""
        try:
            import httpx

            url = "https://imagex.bytedanceapi.com/top/v1"
            params = {
                "Action": "ApplyImageUpload",
                "FileType": "image",
                "ServiceId": token_data["space_name"],
                "UploadNum": "1",
                "Version": "2018-08-01",
                "device_platform": "android"
            }

            base_headers = {
                "User-Agent": "BDFileUpload(1748031167853)",
                "Content-Type": "application/json"
            }

            # 生成AWS4签名
            headers = self._generate_aws4_signature(
                access_key_id=token_data["access_key_id"],
                secret_access_key=token_data["secret_access_key"],
                session_token=token_data["session_token"],
                method="GET",
                url=url,
                params=params,
                headers=base_headers
            )

            timeout = httpx.Timeout(30.0)
            async with httpx.AsyncClient(timeout=timeout, verify=False) as client:
                response = await client.get(url, params=params, headers=headers)

                if response.status_code == 200:
                    result = response.json()

                    # 检查是否有错误
                    error = result.get("ResponseMetadata", {}).get("Error")
                    if error:
                        logger.error(f"[{self.name}] 申请上传地址失败: {error}")
                        return None

                    if result.get("ResponseMetadata", {}).get("Action") == "ApplyImageUpload":
                        upload_result = result.get("Result", {})
                        logger.info(f"[{self.name}] 申请上传地址成功")
                        return upload_result
                    else:
                        logger.error(f"[{self.name}] 申请上传地址失败: {result}")
                        return None
                else:
                    logger.error(f"[{self.name}] 申请上传地址HTTP失败: {response.status_code}")
                    return None

        except Exception as e:
            logger.error(f"[{self.name}] 申请上传地址异常: {e}")
            return None

    def _generate_aws4_signature(self, access_key_id: str, secret_access_key: str,
                                session_token: str, method: str, url: str,
                                params: dict, headers: dict, payload: str = "") -> dict:
        """生成AWS4签名"""
        try:
            import hashlib
            import hmac
            from datetime import datetime
            from urllib.parse import urlparse, quote

            # 解析URL
            parsed_url = urlparse(url)
            host = parsed_url.netloc
            path = parsed_url.path or "/"

            # 时间戳
            t = datetime.utcnow()
            amz_date = t.strftime('%Y%m%dT%H%M%SZ')
            datestamp = t.strftime('%Y%m%d')

            # 必需的headers
            canonical_headers_dict = {
                'host': host,
                'x-amz-date': amz_date,
            }

            if session_token:
                canonical_headers_dict['x-amz-security-token'] = session_token.strip()

            # 添加其他headers
            for k, v in headers.items():
                if k.lower() not in canonical_headers_dict:
                    canonical_headers_dict[k.lower()] = str(v).strip()

            # 规范化查询字符串
            canonical_querystring = '&'.join([
                f"{quote(str(k), safe='')}={quote(str(v), safe='')}"
                for k, v in sorted(params.items())
            ])

            # 规范化headers
            signed_headers = ';'.join(sorted(canonical_headers_dict.keys()))
            canonical_headers = '\n'.join([
                f"{k}:{canonical_headers_dict[k]}"
                for k in sorted(canonical_headers_dict.keys())
            ]) + '\n'

            # Payload hash
            payload_hash = hashlib.sha256(payload.encode('utf-8')).hexdigest()

            # 规范化请求
            canonical_request = '\n'.join([
                method.upper(),
                path,
                canonical_querystring,
                canonical_headers,
                signed_headers,
                payload_hash
            ])

            # 要签名的字符串
            algorithm = 'AWS4-HMAC-SHA256'
            credential_scope = f"{datestamp}/sdwdmwlll/imagex/aws4_request"
            string_to_sign = '\n'.join([
                algorithm,
                amz_date,
                credential_scope,
                hashlib.sha256(canonical_request.encode('utf-8')).hexdigest()
            ])

            # 计算签名
            def sign(key, msg):
                return hmac.new(key, msg.encode('utf-8'), hashlib.sha256).digest()

            def get_signature_key(key, date_stamp, region_name, service_name):
                k_date = sign(('AWS4' + key).encode('utf-8'), date_stamp)
                k_region = sign(k_date, region_name)
                k_service = sign(k_region, service_name)
                k_signing = sign(k_service, 'aws4_request')
                return k_signing

            signing_key = get_signature_key(secret_access_key, datestamp, 'sdwdmwlll', 'imagex')
            signature = hmac.new(signing_key, string_to_sign.encode('utf-8'), hashlib.sha256).hexdigest()

            # 构建Authorization头
            authorization_header = (
                f"{algorithm} "
                f"Credential={access_key_id}/{credential_scope}, "
                f"SignedHeaders={signed_headers}, "
                f"Signature={signature}"
            )

            # 构建最终headers
            final_headers = headers.copy()
            final_headers.update({
                'Host': host,
                'X-Amz-Date': amz_date,
                'Authorization': authorization_header
            })

            if session_token:
                final_headers['X-Amz-Security-Token'] = session_token.strip()

            return final_headers

        except Exception as e:
            logger.error(f"[{self.name}] AWS4签名生成失败: {e}")
            return headers

    async def _upload_image_to_aws(self, upload_info: dict, image_path: str) -> Optional[str]:
        """上传图片到AWS"""
        try:
            import httpx
            import zlib

            logger.info(f"[{self.name}] 上传信息结构: {upload_info.keys()}")

            # 检查上传信息结构
            if "UploadAddress" in upload_info and upload_info["UploadAddress"]:
                upload_address = upload_info["UploadAddress"]
                store_info = upload_address["StoreInfos"][0]
                upload_host = upload_address["UploadHosts"][0]

                url = f"https://{upload_host}/upload/v1/{store_info['StoreUri']}"
                logger.info(f"[{self.name}] 上传URL: {url}")

                # 读取图片文件
                with open(image_path, "rb") as f:
                    image_data = f.read()

                # 计算CRC32校验值
                crc32_value = zlib.crc32(image_data) & 0xffffffff
                crc32_hex = f"{crc32_value:08x}"

                headers = {
                    "Authorization": store_info["Auth"],
                    "Content-Type": "application/octet-stream",
                    "User-Agent": "BDFileUpload(1748031169029)",
                    "X-Upload-Content-CRC32": crc32_hex,
                    "Content-Length": str(len(image_data))
                }

                timeout = httpx.Timeout(60.0)
                async with httpx.AsyncClient(timeout=timeout, verify=False) as client:
                    response = await client.post(url, headers=headers, content=image_data)

                    if response.status_code == 200:
                        result = response.json()
                        if result.get("code") == 2000:
                            logger.info(f"[{self.name}] 图片上传成功: {store_info['StoreUri']}")
                            return store_info["StoreUri"]
                        else:
                            logger.error(f"[{self.name}] 图片上传失败: {result}")
                            return None
                    else:
                        logger.error(f"[{self.name}] 上传请求失败: {response.status_code}")
                        return None
            else:
                logger.error(f"[{self.name}] upload_info格式错误，缺少UploadAddress: {upload_info}")
                return None

        except Exception as e:
            logger.error(f"[{self.name}] 图片上传异常: {e}")
            return None

    def _build_common_params(self) -> dict:
        """构建通用请求参数"""
        return {
            "iid": self.iid,
            "device_id": self.device_id,
            "ac": "wifi",
            "channel": "oppo_64_581595",
            "aid": "581595",
            "app_name": "dreamina",
            "version_code": "*********",
            "version_name": "1.3.8",
            "device_platform": "android",
            "os": "android",
            "ssmix": "a",
            "device_type": "Pixel",
            "device_brand": "Google",
            "language": "zh",
            "os_api": "29",
            "os_version": "10",
            "manifest_version_code": "*********",
            "resolution": "1080*2232",
            "dpi": "480",
            "update_version_code": "*********",
            "_rticket": str(int(time.time() * 1000)),
            "cdid": str(uuid.uuid4()),
            "region": "cn"
        }

    async def _save_temp_image(self, image_data: bytes) -> Optional[str]:
        """保存图片到临时文件"""
        try:
            filename = f"temp_{uuid.uuid4().hex[:8]}.jpg"
            file_path = self.temp_dir / filename

            with open(file_path, 'wb') as f:
                f.write(image_data)

            return str(file_path)
        except Exception as e:
            logger.error(f"[{self.name}] 保存临时图片失败: {e}")
            return None

    async def _upload_image(self, image_path: str) -> Optional[str]:
        """上传图片到即梦服务器"""
        try:
            upload_token = await self._get_upload_token()
            if not upload_token:
                return None

            upload_info = await self._apply_image_upload(upload_token)
            if not upload_info:
                return None

            image_uri = await self._upload_image_to_aws(upload_info, image_path)
            return image_uri
        except Exception as e:
            logger.error(f"[{self.name}] 上传图片失败: {e}")
            return None

    async def _create_img2img_task(self, image_url: str, prompt: str) -> Optional[str]:
        """创建图生图任务"""
        try:
            url = f"{self.dreamina_api_url}/mweb/v1/aigc_draft/generate"

            # 构建请求参数（按照旧插件的完整参数）
            params = self._build_common_params()
            params.update({
                "babi_param": urllib.parse.quote(json.dumps({
                    "scenario": "image_video_generation",
                    "feature_key": "image_to_image",
                    "feature_entrance": "to_image",
                    "feature_entrance_detail": "to_image-image_to_image"
                })),
                "aigc_flow_version": "3.2.5",
                "aigc_flow_support_features": "AIGC_BlendAbility_twoFace,AIGC_GenerateType_AI_Effect,AIGC_GenerateType_byteEditPainting,AIGC_GenerateType_byteEditPainting_v2,AIGC_BlendAbility_multiStyleReference,AIGC_BlendAbility_styleCode,AIGC_LipSync_PostEdit,AIGC_BlendAbility_mixIpKeepByteEdit"
            })

            # 构建任务数据
            task_data = self._build_img2img_task_data(prompt, image_url)
            if not task_data:
                logger.error(f"[{self.name}] 构建任务数据失败")
                return None

            headers = self.base_headers.copy()
            headers = self._update_headers_time(headers)
            headers["Content-Type"] = "application/json; charset=utf-8"
            headers["Host"] = "dreamina-app-hl.jianying.com"

            url_with_params = f"{url}?{urllib.parse.urlencode(params)}"
            result = await self.tools.async_post(url_with_params, json_data=task_data, headers=headers)

            if result and result.get('ret') == "0":
                # 按照旧插件的逻辑获取任务ID
                data_obj = result.get("data", {})
                aigc_data = data_obj.get("aigc_data", {})
                task_id = aigc_data.get("history_record_id")

                if task_id:
                    logger.info(f"[{self.name}] 图生图任务创建成功: {task_id}")
                    return task_id
                else:
                    logger.error(f"[{self.name}] 无法从响应中获取任务ID")
                    logger.error(f"[{self.name}] 完整响应: {result}")
                    return None
            else:
                logger.error(f"[{self.name}] 图生图任务创建失败: {result}")
                return None

        except Exception as e:
            logger.error(f"[{self.name}] 创建图生图任务失败: {e}")
            return None

    def _build_img2img_task_data(self, prompt: str, image_uri: str) -> dict:
        """构建图生图任务数据"""
        try:
            # 生成各种ID
            submit_id = f"581595_{str(uuid.uuid4())}"
            draft_id = str(uuid.uuid4())
            component_id = str(uuid.uuid4())
            metadata_id = str(uuid.uuid4())
            abilities_id = str(uuid.uuid4())
            blend_id = str(uuid.uuid4())
            core_param_id = str(uuid.uuid4())
            large_image_info_id = str(uuid.uuid4())
            ability_item_id = str(uuid.uuid4())
            image_item_id = str(uuid.uuid4())
            prompt_placeholder_id = str(uuid.uuid4())
            postedit_param_id = str(uuid.uuid4())

            # 当前时间戳
            current_time_ms = str(int(time.time() * 1000))

            # 默认图片尺寸配置（3:4比例）
            width, height = 936, 1248

            # 构建draft content
            draft_content = {
                "type": "draft",
                "id": draft_id,
                "min_version": "3.2.5",
                "min_features": [],
                "is_from_tsn": True,
                "version": "3.2.5",
                "main_component_id": component_id,
                "component_list": [
                    {
                        "type": "image_base_component",
                        "id": component_id,
                        "min_version": "3.0.2",
                        "gen_type": 12,
                        "metadata": {
                            "type": "",
                            "id": metadata_id,
                            "created_platform": 2,
                            "created_platform_version": "*********",
                            "created_time_in_ms": current_time_ms,
                            "created_did": self.device_id
                        },
                        "generate_type": "blend",
                        "aigc_mode": "workbench",
                        "abilities": {
                            "type": "",
                            "id": abilities_id,
                            "blend": {
                                "type": "",
                                "id": blend_id,
                                "min_version": "3.2.5",
                                "min_features": [],
                                "core_param": {
                                    "type": "",
                                    "id": core_param_id,
                                    "model": "high_aes_general_v30l_art_fangzhou:general_v3.0_18b",
                                    "prompt": f"##{prompt}",
                                    "sample_strength": 0.3,
                                    "image_ratio": 5,
                                    "large_image_info": {
                                        "type": "",
                                        "id": large_image_info_id,
                                        "height": height,
                                        "width": width,
                                        "format": "jpg",
                                        "resolution_type": "1k"
                                    }
                                },
                                "ability_list": [
                                    {
                                        "type": "",
                                        "id": ability_item_id,
                                        "name": "byte_edit",
                                        "image_uri_list": [image_uri],
                                        "image_list": [
                                            {
                                                "type": "image",
                                                "id": image_item_id,
                                                "source_from": "upload",
                                                "platform_type": 1,
                                                "name": "",
                                                "image_uri": image_uri,
                                                "width": 0,
                                                "height": 0,
                                                "format": "",
                                                "uri": image_uri
                                            }
                                        ],
                                        "strength": 0.5
                                    }
                                ],
                                "prompt_placeholder_info_list": [
                                    {
                                        "type": "",
                                        "id": prompt_placeholder_id,
                                        "ability_index": 0
                                    }
                                ],
                                "postedit_param": {
                                    "type": "",
                                    "id": postedit_param_id,
                                    "generate_type": 12
                                }
                            }
                        }
                    }
                ]
            }

            return {
                "draft_content": json.dumps(draft_content),
                "submit_id": submit_id,
                "metrics_extra": json.dumps({"is_regenerate": False}),
                "extend": {
                    "root_model": "high_aes_general_v30l_art_fangzhou:general_v3.0_18b"
                }
            }

        except Exception as e:
            logger.error(f"[{self.name}] 构建任务数据异常: {e}")
            return {}

    async def _poll_task_status(self, task_id: str, max_attempts: int = 20) -> Optional[Dict]:
        """轮询任务状态"""
        try:
            STATUS_PENDING = 20
            STATUS_SUCCESS = 50
            STATUS_FAILED = 40

            url = f"{self.dreamina_api_url}/mweb/v1/get_history_by_ids"
            params = self._build_common_params()
            payload = {"history_ids": [task_id]}

            headers = self.base_headers.copy()
            headers = self._update_headers_time(headers)
            headers["Content-Type"] = "application/json; charset=utf-8"
            headers["Host"] = "dreamina-app-hl.jianying.com"

            url_with_params = f"{url}?{urllib.parse.urlencode(params)}"

            for attempt in range(max_attempts):
                try:
                    result = await self.tools.async_post(url_with_params, json_data=payload, headers=headers)

                    if result and result.get("ret") == "0" and task_id in result.get("data", {}):
                        data_result = result.get("data", {}).get(task_id, {})
                        status = data_result.get("status") or data_result.get("task", {}).get("status")

                        if status == STATUS_SUCCESS:
                            return data_result
                        elif status == STATUS_FAILED:
                            return None
                        elif status == STATUS_PENDING:
                            await asyncio.sleep(3)
                            continue

                    await asyncio.sleep(3)
                except Exception:
                    await asyncio.sleep(3)
                    continue

            return None
        except Exception as e:
            logger.error(f"[{self.name}] 轮询任务状态失败: {e}")
            return None

    async def _download_images(self, result: Dict) -> list:
        """下载生成的图片"""
        try:
            item_list = result.get("item_list", [])
            if not item_list:
                return []

            image_paths = []
            for idx, item in enumerate(item_list):
                try:
                    image_data = item.get("image", {})
                    large_images = image_data.get("large_images", [])

                    for large_image in large_images:
                        image_url = large_image.get("image_url")
                        if image_url:
                            image_path = await self._download_single_image(image_url, idx)
                            if image_path:
                                image_paths.append(image_path)
                except Exception:
                    continue

            return image_paths
        except Exception as e:
            logger.error(f"[{self.name}] 下载图片失败: {e}")
            return []

    async def _download_single_image(self, image_url: str, idx: int) -> Optional[str]:
        """下载单张图片"""
        try:
            import httpx

            filename = f"jiemeng_img2img_{idx}_{uuid.uuid4().hex[:8]}.jpg"
            file_path = self.temp_dir / filename

            await asyncio.sleep(1)  # 简化延时

            timeout = httpx.Timeout(30.0)
            async with httpx.AsyncClient(timeout=timeout, verify=False) as client:
                response = await client.get(image_url)

                if response.status_code == 200:
                    img_data = response.content
                    if img_data and len(img_data) > 100:
                        with open(file_path, 'wb') as f:
                            f.write(img_data)
                        return str(file_path)

            return None
        except Exception as e:
            logger.error(f"[{self.name}] 下载单张图片失败: {e}")
            return None
