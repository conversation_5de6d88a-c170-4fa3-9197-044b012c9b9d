from Plugins._Tools import Tools
from Config.logger import logger
from Core.PluginBase import PluginBase
import os
import json
import time
import uuid
import httpx
import asyncio
from typing import Optional, Dict, Any
from urllib.parse import quote
from pathlib import Path

# 定义HTTP响应类，用于兼容旧插件代码
class HttpResponse:
    def __init__(self, status_code, body, headers=None):
        self.code = status_code
        self.status_code = status_code
        self.body = body
        self.headers = headers or {}

class DoubaoImageGenPlugin(PluginBase):
    def __init__(self):
        super().__init__()
        # 基本信息（重要：name必须与目录名一致）
        self.name = "DoubaoImageGen"
        self.description = "豆包AI图片生成插件"
        self.version = "1.0.0"
        self.author = "XYBot"

        # 工具类实例
        self.tools = Tools()

        # 加载配置文件
        self.configData = self.tools.returnConfigData(os.path.dirname(__file__))
        # 豆包生图：豆包+画开头
        self.keywords = ['豆包']
        self.api_config = self.configData.get('API', {})
        
        # 豆包API配置
        self.api_base_url = "https://www.doubao.com/samantha/chat/completion"
        self.cookies = self.api_config.get('cookies', '')
        
        # 豆包API常量
        self.device_id = "7468716989062841895"
        self.tea_uuid = "7468716986638386703"
        self.web_id = "7468716986638386703"
        
        # 画图触发关键词
        self.drawing_keywords = [
            "画一", "画个", "画图", "绘制", "生成图片", "创建图像", "创建图片",
            "生成一张", "生成一个", "生成一幅", "作画", "画出", "生成一套",
            "分镜", "插画", "漫画", "海报", "封面", "图像", "图集",
            "照片", "效果图", "设计图", "AI绘画", "AI创作", "电影分镜",
            "概念图", "场景图", "角色图", "人物图", "背景图", "动作图",
            "机械设计", "3D建模", "渲染", "CG", "UI设计", "游戏原画",
            "剧照", "视觉效果", "美术风格", "视觉呈现", "图形设计"
        ]
        
        # 画图相关的图像风格和格式关键词
        self.drawing_style_keywords = [
            "风格", "比例", "像素", "精细", "质感", "氛围", "写实", "卡通",
            "赛博朋克", "科幻", "魔幻", "奇幻", "古风", "现代", "未来",
            "16:9", "4:3", "1:1", "3:4", "9:16", "方形", "竖版", "横版",
            "高清", "超清", "4K", "8K", "HDR", "高对比度", "饱和度", "色彩鲜艳",
            "暗色调", "亮色调", "黑白", "彩色", "水彩", "油画", "素描", "漫画风",
            "写意", "抽象", "具象", "照片级", "逼真", "动漫风", "像素风",
            "赛璐璐", "唯美", "黑暗", "明亮", "温暖", "冷色调", "复古", "现代感"
        ]
        
        # 图片选择配置
        image_selection_config = self.configData.get('image_selection', {})
        self.image_selection = {
            "default_mode": image_selection_config.get("default_mode", "thumbnail"),
            "thumbnail_keywords": image_selection_config.get("thumbnail_keywords", ["缩略图", "小图", "预览图", "thumb"]),
            "original_keywords": image_selection_config.get("original_keywords", ["原图", "高清", "大图", "清晰", "原始"])
        }
        
        # httpx客户端管理
        self._client = None
        self._client_lock = asyncio.Lock()
        

        
        # 用户级别的请求限制
        self._user_limits = {}
        self.min_request_interval = 5.0  # 图片生成间隔更长
        

        
        # 初始化临时目录
        self.temp_dir = Path("temp/doubao_image")
        self.temp_dir.mkdir(parents=True, exist_ok=True)



    async def get_httpx_client(self):
        """创建httpx客户端"""
        return httpx.AsyncClient(
            timeout=httpx.Timeout(connect=10.0, read=20.0, write=20.0, pool=5.0),
            verify=False,  # 禁用SSL验证
            follow_redirects=True,
            limits=httpx.Limits(max_connections=20, max_keepalive_connections=10),
        )

    async def get_session(self):
        """获取或创建httpx异步客户端"""
        async with self._client_lock:
            if self._client is None or self._client.is_closed:
                self._client = await self.get_httpx_client()
            return self._client

    async def close_session(self):
        """关闭httpx客户端"""
        async with self._client_lock:
            if self._client and not self._client.is_closed:
                await self._client.aclose()
                self._client = None

    def _generate_headers(self):
        """生成请求头"""
        # 生成随机的x-flow-trace
        flow_trace = f"04-{uuid.uuid4().hex[:16]}-{uuid.uuid4().hex[:16]}-01"

        return {
            "Accept": "*/*",
            "Accept-Encoding": "gzip, deflate",
            "Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
            "Connection": "keep-alive",
            "Content-Type": "application/json; charset=utf-8",
            "Cookie": self.cookies,
            "Host": "www.doubao.com",
            "Origin": "https://www.doubao.com",
            "Referer": "https://www.doubao.com/chat/",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "x-flow-trace": flow_trace,
            "Agw-Js-Conv": "str",
            "X-Requested-With": "mark.via",
            "last-event-id": "undefined",
            "Sec-Fetch-Site": "same-origin",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Dest": "empty"
        }

    async def handle_message(self, msg) -> bool:
        """处理消息"""
        try:
            # 只处理文本消息
            if msg.type != 1:
                return False

            content = msg.content.strip()
            
            # 检查是否是画图请求
            if not self._is_drawing_request(content):
                return False
                
            logger.info(f"插件 {self.name} 处理画图消息: {content}")
            
            # 提取画图描述
            drawing_prompt = self._extract_drawing_prompt(content)
            if not drawing_prompt:
                return False
            
            # 处理画图请求
            await self._handle_drawing_request(msg, drawing_prompt)
            return True
            
        except Exception as e:
            logger.error(f"插件 {self.name} 处理消息时出错: {e}", exc_info=True)
            return True

    def _is_drawing_request(self, content: str) -> bool:
        """判断是否是画图请求：豆包+画开头"""
        if not content.startswith("豆包"):
            return False

        # 提取豆包后的内容
        query = content[2:].strip()  # 移除"豆包"
        return query.startswith("画")

    def _extract_query(self, content: str) -> str:
        """提取查询内容"""
        for keyword in self.keywords:
            if content.startswith(keyword):
                return content[len(keyword):].strip()
        return content.strip()

    def _extract_drawing_prompt(self, content: str) -> str:
        """提取画图描述：移除豆包+画前缀"""
        # 移除"豆包"
        query = content[2:].strip()
        # 移除"画"
        if query.startswith("画"):
            prompt = query[1:].strip()
            return prompt if prompt else "一张美丽的图片"
        return "一张美丽的图片"

    def _determine_image_mode(self, query: str) -> str:
        """根据查询内容确定图片模式 - 总是返回高清原图模式"""
        # 总是返回原图模式，发送高清图片
        return "original"

    def _select_image_url(self, image_data: dict, mode: str) -> str:
        """根据模式选择合适的图片URL"""
        if mode == "original":
            # 原图模式：优先选择高分辨率原图
            if "image_raw" in image_data:
                return image_data["image_raw"]["url"]
            elif "image_ori" in image_data:
                return image_data["image_ori"]["url"]
            elif "image_thumb_ori" in image_data:
                return image_data["image_thumb_ori"]["url"]
        else:
            # 缩略图模式：优先选择无水印缩略图
            if "image_thumb_ori" in image_data:
                return image_data["image_thumb_ori"]["url"]
            elif "image_thumb" in image_data:
                return image_data["image_thumb"]["url"]
        
        # 兜底：返回任何可用的图片URL
        for key in ["image_raw", "image_ori", "image_thumb_ori", "image_thumb"]:
            if key in image_data and "url" in image_data[key]:
                return image_data[key]["url"]
        
        return ""

    async def _handle_drawing_request(self, msg, prompt: str):
        """处理画图请求"""
        try:
            target_id = msg.roomid if msg.from_group() else msg.sender

            # 检查用户限制
            wait_time = self._check_user_limit(target_id, msg.sender)
            if wait_time > 0:
                return

            # 调用豆包API生成图片
            result = await self.call_doubao_api(prompt)

            if result and result.get("type") == "mixed":
                # 先发送文本回复（如果有）
                text_reply = result.get("text", "").strip()
                if text_reply:
                    await self.dp.sendText(text_reply, target_id, msg.self_wxid)

                # 再发送生成的图片（如果有）
                images = result.get("images", [])
                if images:
                    await self._send_generated_images(msg, images, prompt)

        except Exception as e:
            logger.error(f"处理画图请求失败: {e}", exc_info=True)

    async def _send_generated_images(self, msg, images: list, prompt: str):
        """发送生成的图片"""
        target_id = msg.roomid if msg.from_group() else msg.sender
        success_count = 0
        failed_count = 0

        # 确定图片模式
        image_mode = self._determine_image_mode(prompt)

        for image in images:
            # 根据模式选择合适的图片URL
            selected_url = self._select_image_url(image, image_mode)
            if not selected_url:
                failed_count += 1
                continue

            try:
                # 下载图片
                client = await self.get_session()
                headers = {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
                }

                response = await client.get(
                    selected_url,
                    headers=headers,
                    timeout=httpx.Timeout(connect=10.0, read=60.0, write=30.0, pool=10.0)
                )

                if response.status_code != 200:
                    failed_count += 1
                    continue

                # 获取图片数据
                image_data = response.content

                # 检查图片数据是否有效
                if len(image_data) < 1000:
                    failed_count += 1
                    continue

                # 将图片数据转换为base64并发送
                import base64
                image_base64 = base64.b64encode(image_data).decode('utf-8')
                await self.dp.sendImage(image_base64, target_id, msg.self_wxid)
                success_count += 1

                # 每张图片发送后短暂等待
                await asyncio.sleep(1)

            except Exception as e:
                logger.error(f"发送图片失败: {e}")
                failed_count += 1

        # 如果所有图片都发送失败，记录日志
        if success_count == 0:
            logger.warning(f"所有图片发送失败，成功: {success_count}, 失败: {failed_count}")



    def _check_user_limit(self, wxid: str, user_wxid: str) -> float:
        """检查用户是否可以发送请求"""
        current_time = time.time()

        if wxid not in self._user_limits:
            self._user_limits[wxid] = {}

        last_request = self._user_limits[wxid].get(user_wxid, 0)
        wait_time = max(0, self.min_request_interval - (current_time - last_request))

        if wait_time == 0:
            self._user_limits[wxid][user_wxid] = current_time

        return wait_time

    async def call_doubao_api(self, prompt: str) -> Optional[Dict[str, Any]]:
        """调用豆包API生成图片"""
        try:
                # 构造请求URL参数
                params = {
                    "aid": "497858",
                    "device_id": self.device_id,
                    "device_platform": "web",
                    "language": "zh",
                    "pc_version": "2.16.4",
                    "pkg_type": "release_version",
                    "real_aid": "497858",
                    "region": "CN",
                    "samantha_web": "1",
                    "sys_region": "CN",
                    "tea_uuid": self.tea_uuid,
                    "use-olympus-account": "1",
                    "version_code": "20800",
                    "web_id": self.web_id
                }

                # 创建随机的会话ID和消息ID
                conversation_id = f"38{int(time.time() * 10000)}8"
                section_id = f"{conversation_id[:10]}4{conversation_id[10:]}"
                message_id = f"ba0d6520-{uuid.uuid4().hex[:4]}-11f0-a188-0d4d{uuid.uuid4().hex[:8]}"
                local_conversation_id = f"local_{uuid.uuid4().hex[:16]}"

                # 构造请求数据
                request_data = {
                    "messages": [{
                        "content": json.dumps({"text": prompt}),
                        "content_type": 2009,  # 图片生成请求的content_type
                        "attachments": [],
                    }],
                    "completion_option": {
                        "is_regen": False,
                        "with_suggest": False,  # 图片生成不需要建议
                        "need_create_conversation": True,
                        "launch_stage": 1,
                        "is_replace": False,
                        "is_delete": False,
                        "message_from": 0,
                        "use_auto_cot": False,
                        "event_id": "0"
                    },
                    "section_id": section_id,
                    "conversation_id": conversation_id,
                    "local_conversation_id": local_conversation_id,
                    "local_message_id": message_id
                }

                # 获取httpx客户端
                client = await self.get_session()

                # 设置请求头
                headers = self._generate_headers()

                # 构造完整URL
                url = f"{self.api_base_url}?" + "&".join([f"{k}={quote(str(v))}" for k,v in params.items()])

                # 发送POST请求
                response = await client.post(
                    url,
                    json=request_data,
                    headers=headers,
                    timeout=httpx.Timeout(connect=10.0, read=300.0, write=60.0, pool=10.0)
                )

                if response.status_code != 200:
                    error_text = response.content.decode('utf-8', errors='ignore') if response.content else "无响应内容"
                    logger.error(f"[DoubaoImageGen] API请求失败: 状态码={response.status_code}")
                    logger.error(f"[DoubaoImageGen] 响应内容: {error_text[:500]}...")  # 只显示前500字符
                    logger.error(f"[DoubaoImageGen] 请求URL: {self.api_base_url}")
                    logger.error(f"[DoubaoImageGen] 请求数据: {json.dumps(request_data, ensure_ascii=False)[:200]}...")
                    raise ValueError(f"API请求失败: {response.status_code}")

                # 创建自定义响应对象
                http_response = HttpResponse(
                    status_code=response.status_code,
                    body=response.content,
                    headers=dict(response.headers)
                )

                # 处理响应
                result = await self._process_stream(http_response)

                if result:
                    return result

                return {"type": "mixed", "text": "", "images": []}

        except Exception as e:
            logger.error(f"[DoubaoImageGen] API调用失败: {e}")
            return {"type": "mixed", "text": "", "images": []}

    async def _process_stream(self, response) -> Optional[Dict[str, Any]]:
        """处理SSE响应流，同时处理文本和图片生成"""
        result_data = {"type": "mixed", "text": "", "images": []}
        images_data = []
        text_content = ""

        try:
            # 解码响应数据
            if isinstance(response.body, str):
                chunk = response.body.encode('utf-8')
            else:
                chunk = response.body

            try:
                decoded_chunk = chunk.decode('utf-8', errors='ignore')
                buffer = decoded_chunk
            except UnicodeDecodeError:
                return None

            # 处理完整的SSE事件
            while "\n\n" in buffer:
                parts = buffer.split("\n\n", 1)
                event = parts[0]
                buffer = parts[1]

                if not event.strip():
                    continue

                # 提取"data:"行
                data_line = None
                for line in event.split("\n"):
                    if line.startswith("data:"):
                        data_line = line[5:].strip()
                        break

                if not data_line:
                    continue

                # 解析数据
                try:
                    event_data = json.loads(data_line)
                    if not isinstance(event_data, dict):
                        continue

                    if "event_type" not in event_data:
                        continue

                    event_type = event_data["event_type"]

                    # 处理结束事件
                    if event_type == 2003:
                        result_data = {
                            "type": "mixed",
                            "text": text_content.strip(),
                            "images": images_data
                        }
                        return result_data

                    # 处理正常消息事件
                    if event_type == 2001 and "event_data" in event_data:
                        try:
                            inner_data = json.loads(event_data["event_data"])

                            if "message" not in inner_data:
                                continue

                            message = inner_data["message"]
                            is_finish = inner_data.get("is_finish", False)

                            if "content_type" not in message or "content" not in message:
                                continue

                            content_type = message["content_type"]
                            try:
                                content = json.loads(message["content"])
                            except json.JSONDecodeError:
                                continue

                            # 处理文本内容 - 豆包生图使用 content_type 10000
                            if content_type == 10000:
                                if "text" in content:
                                    text_content += content["text"]

                            # 处理图片内容
                            if content_type == 2010 or content_type == 2074:
                                # 处理图片数据
                                if "data" in content:
                                    # 清空之前的图片数据（确保不重复）
                                    images_data = []

                                    for img_item in content["data"]:
                                        # 添加完整的图片数据到列表
                                        images_data.append(img_item)

                                    # 如果有图片数据，更新结果数据
                                    if images_data:
                                        result_data = {
                                            "type": "mixed",
                                            "text": text_content.strip(),
                                            "images": images_data
                                        }

                                        # 如果是完成状态，立即返回结果
                                        if is_finish:
                                            return result_data

                                # 处理图片生成响应 - 特殊处理content_type=2074的情况
                                elif "creations" in content:
                                    # 提取所有图片信息
                                    for creation in content["creations"]:
                                        if creation.get("type") == 1 and "image" in creation:
                                            img_info = creation["image"]

                                            # 检查图片是否已生成完成
                                            if img_info.get("status") == 2:  # 状态2表示生成完成
                                                # 添加完整的图片数据到列表
                                                images_data.append(img_info)

                                    # 如果有图片数据，更新结果数据
                                    if images_data:
                                        result_data = {
                                            "type": "mixed",
                                            "text": text_content.strip(),
                                            "images": images_data
                                        }

                                        # 如果是完成状态或reset标志为true，立即返回结果
                                        if is_finish or inner_data.get("reset", False):
                                            return result_data

                        except Exception:
                            continue

                except json.JSONDecodeError:
                    continue
                except Exception:
                    continue

            # 返回最终结果
            result_data = {
                "type": "mixed",
                "text": text_content.strip(),
                "images": images_data
            }
            return result_data

        except Exception as e:
            logger.error(f"[DoubaoImageGen] 处理响应流失败: {e}")
            result_data = {
                "type": "mixed",
                "text": text_content.strip(),
                "images": images_data
            }
            return result_data