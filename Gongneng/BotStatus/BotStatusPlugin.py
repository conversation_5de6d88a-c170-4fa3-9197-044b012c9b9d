from Plugins._Tools import Tools
from Core.PluginBase import PluginBase
import os
import time
import json

class BotStatusPlugin(PluginBase):
    def __init__(self):
        super().__init__()
        self.name = "BotStatus"
        self.description = "机器人运行时长查询插件"
        self.version = "1.0.0"
        self.author = "移植自XYBot"

        self.tools = Tools()
        self.configData = self.tools.returnConfigData(os.path.dirname(__file__))
        self.keywords = self.configData.get('keywords', [])
        self.reset_keywords = self.configData.get('reset_keywords', [])

        self.data_file = os.path.join(os.path.dirname(__file__), 'start_time.json')
        self.start_time = self._load_start_time()

    def _load_start_time(self) -> float:
        try:
            if os.path.exists(self.data_file):
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    return json.load(f).get('start_time', time.time())
            start_time = time.time()
            self._save_start_time(start_time)
            return start_time
        except:
            return time.time()

    def _save_start_time(self, start_time: float):
        try:
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump({'start_time': start_time}, f, ensure_ascii=False, indent=2)
        except:
            pass

    def _format_uptime(self) -> str:
        uptime = int(time.time() - self.start_time)
        days = uptime // (24 * 3600)
        hours = (uptime % (24 * 3600)) // 3600
        minutes = (uptime % 3600) // 60
        seconds = uptime % 60

        parts = []
        if days > 0:
            parts.append(f"{days}天")
        if hours > 0:
            parts.append(f"{hours}小时")
        if minutes > 0:
            parts.append(f"{minutes}分钟")
        if seconds > 0 or not parts:
            parts.append(f"{seconds}秒")

        return " ".join(parts)

    def _get_status_emoji(self) -> str:
        days = int(time.time() - self.start_time) // (24 * 3600)
        if days >= 30:
            return "🔥"
        elif days >= 7:
            return "💪"
        elif days >= 1:
            return "✨"
        else:
            return "🚀"

    async def handle_message(self, msg) -> bool:
        try:
            if msg.type != 1:
                return False

            content = msg.content.strip()
            if msg.from_group() and msg.atusers:
                content = msg.noAtMsg.strip()

            if self.tools.judgeEqualListWord(content, self.reset_keywords):
                self.start_time = time.time()
                self._save_start_time(self.start_time)
                target_id = msg.roomid if msg.from_group() else msg.sender
                await self.dp.sendText("🔄 运行时长已重置\n⏰ 重新开始计时...", target_id, msg.self_wxid)
                return True

            if self.tools.judgeEqualListWord(content, self.keywords):
                uptime = self._format_uptime()
                status_emoji = self._get_status_emoji()
                start_time_str = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(self.start_time))

                response = f"""🤖 机器人状态
{status_emoji} 运行状态：正常运行中
⏰ 运行时长：{uptime}
🚀 启动时间：{start_time_str}
💻 系统版本：DPbot v1.0.0"""

                target_id = msg.roomid if msg.from_group() else msg.sender
                await self.dp.sendText(response, target_id, msg.self_wxid)
                return True

            return False
        except:
            return False
