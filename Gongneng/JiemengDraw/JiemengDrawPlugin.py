from Plugins._Tools import Tools
from Config.logger import logger
from Core.PluginBase import PluginBase
import os
import json
import asyncio
import time
import uuid
import random
import urllib.parse
import httpx
from pathlib import Path
from typing import Optional, Dict

class JiemengDrawPlugin(PluginBase):
    def __init__(self):
        super().__init__()
        # 基本信息
        self.name = "JiemengDraw"
        self.description = "即梦AI绘画插件（文字生图）"
        self.version = "1.0.0"
        self.author = "移植自XYBot"

        # 工具类实例
        self.tools = Tools()

        # 创建临时目录
        self.temp_dir = Path("App/Plugins/JiemengDraw/temp")
        self.temp_dir.mkdir(parents=True, exist_ok=True)

        # 加载配置文件
        self.configData = self.tools.returnConfigData(os.path.dirname(__file__))
        self._load_config()

        # 用户冷却时间记录
        self.user_last_request = {}

    def _load_config(self):
        """加载配置"""
        self.enabled = self.configData.get('enabled', True)
        self.command = self.configData.get('command', ["即梦", "jm"])
        self.command_format = self.configData.get('command_format', "")
        self.cooldown = self.configData.get('cooldown', 30)

        # API配置
        api_config = self.configData.get('API', {})
        self.device_id = api_config.get('device_id', "86229099759449")
        self.iid = api_config.get('iid', "86229099218793")

        # 基本配置
        self.max_retries = 3

        # 基本API配置
        self.dreamina_api_url = "https://dreamina-app-hl.jianying.com"

        # APP端请求头（国内版使用APP接口，不需要cookie）
        self.base_headers = {
            "Connection": "keep-alive",
            "lan": "zh-hans",
            "loc": "cn",
            "pf": "0",
            "vr": "*********",
            "appvr": "1.3.8",
            "tdid": self.device_id,
            "sign-ver": "1",
            "appid": "581595",
            "ac": "wifi",
            "Cache-Control": "no-cache",
            "sysvr": "29",
            "ch": "oppo_64_581595",
            "uid": "329468416627917",
            "COMPRESSED": "1",
            "did": "00000000-50f9-90d5-ffff-ffffef05ac4a",
            "model": "UGl4ZWw=",
            "manu": "R29vZ2xl",
            "GPURender": "QWRyZW5vIChUTSkgNzUw",
            "HDR-TDID": self.device_id,
            "HDR-TIID": self.iid,
            "version_code": "*********",
            "HDR-Sign-Ver": "1",
            "x-vc-bdturing-sdk-version": "3.7.2.cn",
            "sdk-version": "2",
            "X-Tt-Token": "00a00b34aff3a919ca8990897d72a6685d04401ee9474e28c90221f554153c0bdd530fb8e99a032de30269120da2b4868ae53f9e54f0bd7e0dad469840d90199f9dcc3d5c00a9f1aa9a6f954669c4fcc557868d982f18a88c0b6303130a5f6bd2c9b5-1.0.1",
            "passport-sdk-version": "50561",
            "commerce-sign-version": "v1",
            "User-Agent": "com.bytedance.dreamina/1381600 (Linux; U; Android 10; zh_CN; Pixel; Build/NHG47O; Cronet/TTNetVersion:22f14a0c 2024-07-09 QuicVersion:46688bb4 2022-11-28)",
            "Accept-Encoding": "gzip, deflate",
        }

    def _update_headers_time(self, headers: Dict[str, str]) -> Dict[str, str]:
        """更新请求头中的时间相关参数"""
        timestamp = int(time.time())
        headers["device-time"] = str(timestamp)
        headers["HDR-Device-Time"] = str(timestamp)
        return headers

    def _build_common_params(self) -> Dict[str, str]:
        """构建通用API参数"""
        return {
            "iid": self.iid,
            "device_id": self.device_id,
            "ac": "wifi",
            "channel": "oppo_64_581595",
            "aid": "581595",
            "app_name": "dreamina",
            "version_code": "1381600",
            "version_name": "1.3.8",
            "device_platform": "android",
            "os": "android",
            "ssmix": "a",
            "device_type": "Pixel",
            "device_brand": "Google",
            "language": "zh",
            "os_api": "29",
            "os_version": "10",
            "manifest_version_code": "1381600",
            "resolution": "1080*2232",
            "dpi": "480",
            "update_version_code": "1381600",
            "_rticket": str(int(time.time() * 1000)),
            "cdid": "21c50631-efc2-40e8-930d-c989ffa79a98",
            "region": "cn",
            "aigc_flow_version": "3.1.0",
            "aigc_flow_support_features": "AIGC_BlendAbility_twoFace,AIGC_GenerateType_AI_Effect"
        }

    def _check_user_limit(self, wxid: str, user_wxid: str) -> float:
        """检查用户请求限制"""
        current_time = time.time()
        user_key = f"{wxid}_{user_wxid}"

        if user_key in self.user_last_request:
            time_diff = current_time - self.user_last_request[user_key]
            if time_diff < self.cooldown:
                return self.cooldown - time_diff

        self.user_last_request[user_key] = current_time
        return 0

    async def handle_message(self, msg) -> bool:
        """处理群聊消息"""
        # 只处理文本消息
        if msg.type != 1:
            return False
            
        # 检查插件是否启用
        if not self.enabled:
            return False

        content = str(msg.content).strip()
        command = content.split(" ", 1)
        
        # 检查是否是插件命令
        if command[0] not in self.command:
            return False

        logger.info(f"插件 {self.name} 处理绘画消息: {msg.content}")

        # 如果没有提供描述文本
        if len(command) == 1:
            target_id = msg.roomid if msg.from_group() else msg.sender
            await self.dp.sendText(self.command_format, target_id, msg.self_wxid)
            return True

        # 检查用户请求限制
        wait_time = self._check_user_limit(msg.roomid or msg.sender, msg.sender)
        if wait_time > 0:
            target_id = msg.roomid if msg.from_group() else msg.sender
            await self.dp.sendText(f"请等待 {wait_time:.1f} 秒", target_id, msg.self_wxid)
            return True

        # 获取提示词
        prompt_text = " ".join(command[1:])
        prompt, ratio, size = self._parse_command_args(prompt_text)

        # 生成并下载图片
        try:
            image_paths = await self._generate_and_download(prompt, ratio, size)
            target_id = msg.roomid if msg.from_group() else msg.sender

            if image_paths:
                import base64
                for idx, image_path in enumerate(image_paths):
                    try:
                        if not os.path.exists(image_path):
                            continue

                        with open(image_path, 'rb') as f:
                            image_data = f.read()

                        if idx > 0:
                            await asyncio.sleep(1.0 + random.random() * 0.5)

                        image_base64 = base64.b64encode(image_data).decode('utf-8')
                        await self.dp.sendImage(image_base64, target_id, msg.self_wxid)
                        os.remove(image_path)

                    except Exception as e:
                        logger.error(f"[{self.name}] 发送图片失败: {e}")
                        if os.path.exists(image_path):
                            try:
                                os.remove(image_path)
                            except:
                                pass
            else:
                await self.dp.sendText("生成失败，请稍后重试", target_id, msg.self_wxid)
        except Exception as e:
            logger.error(f"[{self.name}] 生成图片异常: {e}")
            target_id = msg.roomid if msg.from_group() else msg.sender
            await self.dp.sendText("生成失败，请稍后重试", target_id, msg.self_wxid)

        return True

    async def handle_private_message(self, msg) -> bool:
        """处理私聊消息"""
        return await self.handle_message(msg)

    def _parse_command_args(self, command_text: str) -> tuple:
        """解析命令参数"""
        ratio, size = 1.0, 1024
        parts = command_text.split(" ")
        prompt_parts = []

        for part in parts:
            if ":" in part and part.replace(":", "").isdigit():
                try:
                    w, h = part.split(":")
                    ratio = float(w) / float(h)
                except:
                    prompt_parts.append(part)
            elif part.isdigit() and 256 <= int(part) <= 2048:
                size = int(part)
            else:
                prompt_parts.append(part)

        return " ".join(prompt_parts), ratio, size

    async def _generate_and_download(self, prompt: str, ratio: float, size: int) -> list:
        """生成并下载图片"""
        try:
            # 1. 发起图片生成请求
            submit_id, task_id = await self._generate_image(prompt, ratio, size)
            if not submit_id:
                return []

            # 2. 等待任务创建
            await asyncio.sleep(5)

            # 3. 获取历史记录ID
            history_id = task_id or await self._get_history_id_by_submit_id(submit_id)
            if not history_id:
                history_id = await self._get_latest_history_id()

            if not history_id:
                return []

            # 4. 轮询任务状态并下载图片
            result = await self._poll_task_status(history_id)
            return await self._download_images(result) if result else []

        except Exception as e:
            logger.error(f"[{self.name}] 生成图片失败: {e}")
            return []

    async def _generate_image(self, prompt: str, ratio: float, size: int) -> tuple:
        """创建图片生成任务"""
        try:
            url = f"{self.dreamina_api_url}/mweb/v1/aigc_draft/generate"

            # 构建URL参数
            params = self._build_common_params()
            params["babi_param"] = "%7B%22scenario%22%3A%22image_video_generation%22%2C%22feature_key%22%3A%22text_to_image%22%2C%22feature_entrance%22%3A%22to_image%22%2C%22feature_entrance_detail%22%3A%22to_image-text_to_image%22%7D"

            # 计算宽高（ratio已经是浮点数）
            if ratio >= 1:
                width = size
                height = int(size / ratio)
            else:
                height = size
                width = int(size * ratio)

            # 确保宽高是8的倍数
            width = (width // 8) * 8
            height = (height // 8) * 8

            # 生成UUID和随机种子
            draft_id = str(uuid.uuid4())
            component_id = str(uuid.uuid4())
            ability_id = str(uuid.uuid4())
            generate_id = str(uuid.uuid4())
            core_param_id = str(uuid.uuid4())
            submit_id = f"581595_{str(uuid.uuid4())}"
            seed = random.randint(1000000000, 2000000000)

            # 构建请求体
            draft_content = {
                "type": "draft",
                "id": draft_id,
                "min_version": "3.0.2",
                "min_features": [],
                "is_from_tsn": True,
                "version": "3.1.0",
                "main_component_id": component_id,
                "component_list": [
                    {
                        "type": "image_base_component",
                        "id": component_id,
                        "min_version": "3.0.2",
                        "generate_type": "generate",
                        "aigc_mode": "workbench",
                        "abilities": {
                            "type": "",
                            "id": ability_id,
                            "generate": {
                                "type": "",
                                "id": generate_id,
                                "core_param": {
                                    "type": "",
                                    "id": core_param_id,
                                    "model": "high_aes_general_v30l_art_fangzhou:general_v3.0_18b",
                                    "prompt": prompt,
                                    "seed": seed,
                                    "sample_strength": 0.5,
                                    "image_ratio": 5,
                                    "large_image_info": {
                                        "type": "",
                                        "id": str(uuid.uuid4()),
                                        "height": height,
                                        "width": width
                                    }
                                }
                            }
                        }
                    }
                ]
            }

            payload = {
                "draft_content": json.dumps(draft_content),
                "extend": {
                    "root_model": "high_aes_general_v30l_art_fangzhou:general_v3.0_18b"
                },
                "submit_id": submit_id
            }

            headers = self.base_headers.copy()
            headers = self._update_headers_time(headers)
            headers["Content-Type"] = "application/json; charset=utf-8"
            headers["Host"] = "dreamina-app-hl.jianying.com"

            # 将params拼接到URL中
            url_with_params = f"{url}?{urllib.parse.urlencode(params)}"

            data = await self.tools.async_post(url_with_params, json_data=payload, headers=headers)

            if data and data.get('ret') == "0":
                data_obj = data.get("data", {})
                # 尝试获取任务ID
                task_id = (data_obj.get("history_record_id") or
                          data_obj.get("task_id") or
                          data_obj.get("aigc_data", {}).get("history_record_id"))
                return submit_id, task_id

            return None, None

        except Exception as e:
            logger.error(f"[{self.name}] 生成图片异常: {e}")
            return None, None

    async def _get_history_id_by_submit_id(self, submit_id: str) -> Optional[str]:
        """通过submit_id获取历史记录ID"""
        try:
            url = f"{self.dreamina_api_url}/mweb/v1/get_history_by_submit_id"
            params = self._build_common_params()
            payload = {"submit_id": submit_id}

            headers = self.base_headers.copy()
            headers = self._update_headers_time(headers)
            headers["Content-Type"] = "application/json; charset=utf-8"
            headers["Host"] = "dreamina-app-hl.jianying.com"

            url_with_params = f"{url}?{urllib.parse.urlencode(params)}"
            data = await self.tools.async_post(url_with_params, json_data=payload, headers=headers)

            if data and data.get("ret") == "0":
                return data.get("data", {}).get("history_id")
            return None

        except Exception as e:
            logger.error(f"[{self.name}] 获取历史记录ID异常: {e}")
            return None

    async def _get_latest_history_id(self) -> Optional[str]:
        """获取最新的历史记录ID"""
        try:
            url = f"{self.dreamina_api_url}/mweb/v1/get_history_list"
            params = self._build_common_params()
            payload = {"cursor": 0, "count": 1, "filter_type": 0}

            headers = self.base_headers.copy()
            headers = self._update_headers_time(headers)
            headers["Content-Type"] = "application/json; charset=utf-8"
            headers["Host"] = "dreamina-app-hl.jianying.com"

            url_with_params = f"{url}?{urllib.parse.urlencode(params)}"
            data = await self.tools.async_post(url_with_params, json_data=payload, headers=headers)

            if data and data.get("ret") == "0":
                history_list = data.get("data", {}).get("history_list", [])
                if history_list:
                    return history_list[0].get("history_id")
            return None

        except Exception as e:
            logger.error(f"[{self.name}] 获取最新历史记录ID异常: {e}")
            return None

    async def _poll_task_status(self, history_id: str, max_attempts: int = 30) -> Optional[Dict]:
        """轮询任务状态"""
        try:
            url = f"{self.dreamina_api_url}/mweb/v1/get_history_by_ids"
            params = self._build_common_params()
            payload = {"history_ids": [history_id]}

            headers = self.base_headers.copy()
            headers = self._update_headers_time(headers)
            headers["Content-Type"] = "application/json; charset=utf-8"
            headers["Host"] = "dreamina-app-hl.jianying.com"

            url_with_params = f"{url}?{urllib.parse.urlencode(params)}"

            for _ in range(max_attempts):
                data = await self.tools.async_post(url_with_params, json_data=payload, headers=headers)

                if data and data.get("ret") == "0":
                    history_data = data.get("data", {}).get(history_id, {})
                    status = history_data.get("status")

                    if status == 50:  # 任务完成
                        return history_data

                await asyncio.sleep(2)

            return None

        except Exception as e:
            logger.error(f"[{self.name}] 轮询任务状态异常: {e}")
            return None

    async def _download_images(self, history_data: Dict) -> list:
        """下载生成的图片"""
        item_list = history_data.get("item_list", [])
        if not item_list:
            return []

        image_paths = []
        for idx, item in enumerate(item_list):
            # 获取最佳图片URL
            best_img_url = self._get_best_image_url(item)
            if best_img_url:
                image_path = await self._download_single_image(best_img_url, idx)
                if image_path:
                    image_paths.append(image_path)

        return image_paths

    def _get_best_image_url(self, item: Dict) -> Optional[str]:
        """获取最佳图片URL"""
        # 优先选择原始大图
        large_images = item.get("image", {}).get("large_images", [])
        for img in large_images:
            img_url = img.get("image_url")
            if img_url and "aigc_resize_mark:0:0" in img_url:
                return img_url

        # 其次选择封面高清版本
        cover_url_map = item.get("common_attr", {}).get("cover_url_map", {})
        for size in ["2400", "1080", "720"]:
            if size in cover_url_map:
                return cover_url_map.get(size)

        # 最后使用第一个可用图片
        if large_images:
            return large_images[0].get("image_url")

        return None

    async def _download_single_image(self, image_url: str, idx: int) -> Optional[str]:
        """下载单张图片"""
        try:
            filename = f"jiemeng_{idx}_{uuid.uuid4().hex[:8]}.jpg"
            file_path = self.temp_dir / filename

            await asyncio.sleep(0.5 + random.random() * 0.5)

            timeout = httpx.Timeout(30.0)
            async with httpx.AsyncClient(timeout=timeout, verify=False) as client:
                response = await client.get(image_url)

                if response.status_code == 200:
                    img_data = response.content
                    if img_data and len(img_data) > 100:
                        with open(file_path, 'wb') as f:
                            f.write(img_data)
                        return str(file_path)

            return None

        except Exception as e:
            logger.error(f"[{self.name}] 下载图片失败: {e}")
            return None
