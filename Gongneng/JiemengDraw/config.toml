# 插件启用状态
enabled = true

# 触发关键词列表
command = ["即梦", "jm"]

# 插件描述
command_format = """⚙️即梦AI绘画使用说明：

🎨生成图片：
即梦 <详细描述文本>
jm <详细描述文本>

📝描述建议：
- 尽可能详细描述场景、人物、动作、环境等
- 提示词越详细，生成效果越好
- 建议使用中文描述"""

# 冷却时间（秒）
cooldown = 30

# API配置（国内版使用APP端接口，无需cookie）
[API]
device_id = "86229099759449"
iid = "86229099218793"

# 令牌桶限流配置
[rate_limit]
tokens_per_second = 0.1  # 每10秒生成1个令牌
bucket_size = 3  # 令牌桶容量

# 图片发送配置
[image_send]
max_retries = 3  # 图片发送最大重试次数
timeout = 60  # 图片发送超时时间（秒）
