Search.setIndex({
    "alltitles": {
        "WechatAPIClient": [[0, null]],
        "\u4fdd\u62a4": [[0, "module-WechatAPI.Client.protect"]],
        "\u57fa\u7840": [[0, "module-WechatAPI.Client.base"]],
        "\u597d\u53cb": [[0, "module-WechatAPI.Client.friend"]],
        "\u5de5\u5177": [[0, "module-WechatAPI.Client.tool"]],
        "\u6d88\u606f": [[0, "module-WechatAPI.Client.message"]],
        "\u7528\u6237": [[0, "module-WechatAPI.Client.user"]],
        "\u767b\u5f55": [[0, "module-WechatAPI.Client.login"]],
        "\u7d22\u5f15": [[0, "id10"]],
        "\u7ea2\u5305": [[0, "module-WechatAPI.Client.hongbao"]],
        "\u7fa4\u804a": [[0, "module-WechatAPI.Client.chatroom"]]
    },
    "docnames": ["index"],
    "envversion": {
        "sphinx": 64,
        "sphinx.domains.c": 3,
        "sphinx.domains.changeset": 1,
        "sphinx.domains.citation": 1,
        "sphinx.domains.cpp": 9,
        "sphinx.domains.index": 1,
        "sphinx.domains.javascript": 3,
        "sphinx.domains.math": 2,
        "sphinx.domains.python": 4,
        "sphinx.domains.rst": 2,
        "sphinx.domains.std": 2,
        "sphinx.ext.viewcode": 1
    },
    "filenames": ["index.rst"],
    "indexentries": {
        "__call__() \uff08wechatapi.client.protect.singleton \u65b9\u6cd5\uff09": [[0, "WechatAPI.Client.protect.Singleton.__call__", false]],
        "__init__() \uff08wechatapi.client.protect.protect \u65b9\u6cd5\uff09": [[0, "WechatAPI.Client.protect.Protect.__init__", false]],
        "_process_message_queue() \uff08wechatapi.client.message.messagemixin \u65b9\u6cd5\uff09": [[0, "WechatAPI.Client.message.MessageMixin._process_message_queue", false]],
        "_queue_message() \uff08wechatapi.client.message.messagemixin \u65b9\u6cd5\uff09": [[0, "WechatAPI.Client.message.MessageMixin._queue_message", false]],
        "_send_text_message() \uff08wechatapi.client.message.messagemixin \u65b9\u6cd5\uff09": [[0, "WechatAPI.Client.message.MessageMixin._send_text_message", false]],
        "accept_friend() \uff08wechatapi.client.friend.friendmixin \u65b9\u6cd5\uff09": [[0, "WechatAPI.Client.friend.FriendMixin.accept_friend", false]],
        "add_chatroom_member() \uff08wechatapi.client.chatroom.chatroommixin \u65b9\u6cd5\uff09": [[0, "WechatAPI.Client.chatroom.ChatroomMixin.add_chatroom_member", false]],
        "awaken_login() \uff08wechatapi.client.login.loginmixin \u65b9\u6cd5\uff09": [[0, "WechatAPI.Client.login.LoginMixin.awaken_login", false]],
        "base64_to_byte()\uff08wechatapi.client.tool.toolmixin \u9759\u6001\u65b9\u6cd5\uff09": [[0, "WechatAPI.Client.tool.ToolMixin.base64_to_byte", false]],
        "base64_to_file()\uff08wechatapi.client.tool.toolmixin \u9759\u6001\u65b9\u6cd5\uff09": [[0, "WechatAPI.Client.tool.ToolMixin.base64_to_file", false]],
        "byte_to_base64()\uff08wechatapi.client.tool.toolmixin \u9759\u6001\u65b9\u6cd5\uff09": [[0, "WechatAPI.Client.tool.ToolMixin.byte_to_base64", false]],
        "chatroommixin\uff08wechatapi.client.chatroom \u4e2d\u7684\u7c7b\uff09": [[0, "WechatAPI.Client.chatroom.ChatroomMixin", false]],
        "check() \uff08wechatapi.client.protect.protect \u65b9\u6cd5\uff09": [[0, "WechatAPI.Client.protect.Protect.check", false]],
        "check_database() \uff08wechatapi.client.tool.toolmixin \u65b9\u6cd5\uff09": [[0, "WechatAPI.Client.tool.ToolMixin.check_database", false]],
        "check_login_uuid() \uff08wechatapi.client.login.loginmixin \u65b9\u6cd5\uff09": [[0, "WechatAPI.Client.login.LoginMixin.check_login_uuid", false]],
        "create_device_id()\uff08wechatapi.client.login.loginmixin \u9759\u6001\u65b9\u6cd5\uff09": [[0, "WechatAPI.Client.login.LoginMixin.create_device_id", false]],
        "create_device_name()\uff08wechatapi.client.login.loginmixin \u9759\u6001\u65b9\u6cd5\uff09": [[0, "WechatAPI.Client.login.LoginMixin.create_device_name", false]],
        "data_len\uff08wechatapi.client.base.section \u5c5e\u6027\uff09": [[0, "WechatAPI.Client.base.Section.data_len", false]],
        "download_attach() \uff08wechatapi.client.tool.toolmixin \u65b9\u6cd5\uff09": [[0, "WechatAPI.Client.tool.ToolMixin.download_attach", false]],
        "download_image() \uff08wechatapi.client.tool.toolmixin \u65b9\u6cd5\uff09": [[0, "WechatAPI.Client.tool.ToolMixin.download_image", false]],
        "download_video() \uff08wechatapi.client.tool.toolmixin \u65b9\u6cd5\uff09": [[0, "WechatAPI.Client.tool.ToolMixin.download_video", false]],
        "download_voice() \uff08wechatapi.client.tool.toolmixin \u65b9\u6cd5\uff09": [[0, "WechatAPI.Client.tool.ToolMixin.download_voice", false]],
        "error_handler()\uff08wechatapi.client.base.wechatapiclientbase \u9759\u6001\u65b9\u6cd5\uff09": [[0, "WechatAPI.Client.base.WechatAPIClientBase.error_handler", false]],
        "file_to_base64()\uff08wechatapi.client.tool.toolmixin \u9759\u6001\u65b9\u6cd5\uff09": [[0, "WechatAPI.Client.tool.ToolMixin.file_to_base64", false]],
        "friendmixin\uff08wechatapi.client.friend \u4e2d\u7684\u7c7b\uff09": [[0, "WechatAPI.Client.friend.FriendMixin", false]],
        "get_auto_heartbeat_status() \uff08wechatapi.client.login.loginmixin \u65b9\u6cd5\uff09": [[0, "WechatAPI.Client.login.LoginMixin.get_auto_heartbeat_status", false]],
        "get_cached_info() \uff08wechatapi.client.login.loginmixin \u65b9\u6cd5\uff09": [[0, "WechatAPI.Client.login.LoginMixin.get_cached_info", false]],
        "get_chatroom_announce() \uff08wechatapi.client.chatroom.chatroommixin \u65b9\u6cd5\uff09": [[0, "WechatAPI.Client.chatroom.ChatroomMixin.get_chatroom_announce", false]],
        "get_chatroom_info() \uff08wechatapi.client.chatroom.chatroommixin \u65b9\u6cd5\uff09": [[0, "WechatAPI.Client.chatroom.ChatroomMixin.get_chatroom_info", false]],
        "get_chatroom_member_list() \uff08wechatapi.client.chatroom.chatroommixin \u65b9\u6cd5\uff09": [[0, "WechatAPI.Client.chatroom.ChatroomMixin.get_chatroom_member_list", false]],
        "get_chatroom_qrcode() \uff08wechatapi.client.chatroom.chatroommixin \u65b9\u6cd5\uff09": [[0, "WechatAPI.Client.chatroom.ChatroomMixin.get_chatroom_qrcode", false]],
        "get_contact() \uff08wechatapi.client.friend.friendmixin \u65b9\u6cd5\uff09": [[0, "WechatAPI.Client.friend.FriendMixin.get_contact", false]],
        "get_contract_detail() \uff08wechatapi.client.friend.friendmixin \u65b9\u6cd5\uff09": [[0, "WechatAPI.Client.friend.FriendMixin.get_contract_detail", false]],
        "get_contract_list() \uff08wechatapi.client.friend.friendmixin \u65b9\u6cd5\uff09": [[0, "WechatAPI.Client.friend.FriendMixin.get_contract_list", false]],
        "get_hongbao_detail() \uff08wechatapi.client.hongbao.hongbaomixin \u65b9\u6cd5\uff09": [[0, "WechatAPI.Client.hongbao.HongBaoMixin.get_hongbao_detail", false]],
        "get_my_qrcode() \uff08wechatapi.client.user.usermixin \u65b9\u6cd5\uff09": [[0, "WechatAPI.Client.user.UserMixin.get_my_qrcode", false]],
        "get_nickname() \uff08wechatapi.client.friend.friendmixin \u65b9\u6cd5\uff09": [[0, "WechatAPI.Client.friend.FriendMixin.get_nickname", false]],
        "get_profile() \uff08wechatapi.client.user.usermixin \u65b9\u6cd5\uff09": [[0, "WechatAPI.Client.user.UserMixin.get_profile", false]],
        "get_qr_code() \uff08wechatapi.client.login.loginmixin \u65b9\u6cd5\uff09": [[0, "WechatAPI.Client.login.LoginMixin.get_qr_code", false]],
        "heartbeat() \uff08wechatapi.client.login.loginmixin \u65b9\u6cd5\uff09": [[0, "WechatAPI.Client.login.LoginMixin.heartbeat", false]],
        "hongbaomixin\uff08wechatapi.client.hongbao \u4e2d\u7684\u7c7b\uff09": [[0, "WechatAPI.Client.hongbao.HongBaoMixin", false]],
        "invite_chatroom_member() \uff08wechatapi.client.chatroom.chatroommixin \u65b9\u6cd5\uff09": [[0, "WechatAPI.Client.chatroom.ChatroomMixin.invite_chatroom_member", false]],
        "ip\uff08wechatapi.client.base.proxy \u5c5e\u6027\uff09": [[0, "WechatAPI.Client.base.Proxy.ip", false]],
        "is_logged_in() \uff08wechatapi.client.user.usermixin \u65b9\u6cd5\uff09": [[0, "WechatAPI.Client.user.UserMixin.is_logged_in", false]],
        "is_running() \uff08wechatapi.client.login.loginmixin \u65b9\u6cd5\uff09": [[0, "WechatAPI.Client.login.LoginMixin.is_running", false]],
        "log_out() \uff08wechatapi.client.login.loginmixin \u65b9\u6cd5\uff09": [[0, "WechatAPI.Client.login.LoginMixin.log_out", false]],
        "loginmixin\uff08wechatapi.client.login \u4e2d\u7684\u7c7b\uff09": [[0, "WechatAPI.Client.login.LoginMixin", false]],
        "messagemixin\uff08wechatapi.client.message \u4e2d\u7684\u7c7b\uff09": [[0, "WechatAPI.Client.message.MessageMixin", false]],
        "module": [[0, "module-WechatAPI.Client.base", false], [0, "module-WechatAPI.Client.chatroom", false], [0, "module-WechatAPI.Client.friend", false], [0, "module-WechatAPI.Client.hongbao", false], [0, "module-WechatAPI.Client.login", false], [0, "module-WechatAPI.Client.message", false], [0, "module-WechatAPI.Client.protect", false], [0, "module-WechatAPI.Client.tool", false], [0, "module-WechatAPI.Client.user", false]],
        "password\uff08wechatapi.client.base.proxy \u5c5e\u6027\uff09": [[0, "WechatAPI.Client.base.Proxy.password", false]],
        "port\uff08wechatapi.client.base.proxy \u5c5e\u6027\uff09": [[0, "WechatAPI.Client.base.Proxy.port", false]],
        "protect\uff08wechatapi.client.protect \u4e2d\u7684\u7c7b\uff09": [[0, "WechatAPI.Client.protect.Protect", false]],
        "proxy\uff08wechatapi.client.base \u4e2d\u7684\u7c7b\uff09": [[0, "WechatAPI.Client.base.Proxy", false]],
        "revoke_message() \uff08wechatapi.client.message.messagemixin \u65b9\u6cd5\uff09": [[0, "WechatAPI.Client.message.MessageMixin.revoke_message", false]],
        "section\uff08wechatapi.client.base \u4e2d\u7684\u7c7b\uff09": [[0, "WechatAPI.Client.base.Section", false]],
        "send_app_message() \uff08wechatapi.client.message.messagemixin \u65b9\u6cd5\uff09": [[0, "WechatAPI.Client.message.MessageMixin.send_app_message", false]],
        "send_card_message() \uff08wechatapi.client.message.messagemixin \u65b9\u6cd5\uff09": [[0, "WechatAPI.Client.message.MessageMixin.send_card_message", false]],
        "send_cdn_file_msg() \uff08wechatapi.client.message.messagemixin \u65b9\u6cd5\uff09": [[0, "WechatAPI.Client.message.MessageMixin.send_cdn_file_msg", false]],
        "send_cdn_img_msg() \uff08wechatapi.client.message.messagemixin \u65b9\u6cd5\uff09": [[0, "WechatAPI.Client.message.MessageMixin.send_cdn_img_msg", false]],
        "send_cdn_video_msg() \uff08wechatapi.client.message.messagemixin \u65b9\u6cd5\uff09": [[0, "WechatAPI.Client.message.MessageMixin.send_cdn_video_msg", false]],
        "send_emoji_message() \uff08wechatapi.client.message.messagemixin \u65b9\u6cd5\uff09": [[0, "WechatAPI.Client.message.MessageMixin.send_emoji_message", false]],
        "send_image_message() \uff08wechatapi.client.message.messagemixin \u65b9\u6cd5\uff09": [[0, "WechatAPI.Client.message.MessageMixin.send_image_message", false]],
        "send_link_message() \uff08wechatapi.client.message.messagemixin \u65b9\u6cd5\uff09": [[0, "WechatAPI.Client.message.MessageMixin.send_link_message", false]],
        "send_text_message() \uff08wechatapi.client.message.messagemixin \u65b9\u6cd5\uff09": [[0, "WechatAPI.Client.message.MessageMixin.send_text_message", false]],
        "send_video_message() \uff08wechatapi.client.message.messagemixin \u65b9\u6cd5\uff09": [[0, "WechatAPI.Client.message.MessageMixin.send_video_message", false]],
        "send_voice_message() \uff08wechatapi.client.message.messagemixin \u65b9\u6cd5\uff09": [[0, "WechatAPI.Client.message.MessageMixin.send_voice_message", false]],
        "set_proxy() \uff08wechatapi.client.tool.toolmixin \u65b9\u6cd5\uff09": [[0, "WechatAPI.Client.tool.ToolMixin.set_proxy", false]],
        "set_step() \uff08wechatapi.client.tool.toolmixin \u65b9\u6cd5\uff09": [[0, "WechatAPI.Client.tool.ToolMixin.set_step", false]],
        "silk_base64_to_wav_byte()\uff08wechatapi.client.tool.toolmixin \u9759\u6001\u65b9\u6cd5\uff09": [[0, "WechatAPI.Client.tool.ToolMixin.silk_base64_to_wav_byte", false]],
        "silk_byte_to_byte_wav_byte()\uff08wechatapi.client.tool.toolmixin \u9759\u6001\u65b9\u6cd5\uff09": [[0, "WechatAPI.Client.tool.ToolMixin.silk_byte_to_byte_wav_byte", false]],
        "singleton\uff08wechatapi.client.protect \u4e2d\u7684\u7c7b\uff09": [[0, "WechatAPI.Client.protect.Singleton", false]],
        "start_auto_heartbeat() \uff08wechatapi.client.login.loginmixin \u65b9\u6cd5\uff09": [[0, "WechatAPI.Client.login.LoginMixin.start_auto_heartbeat", false]],
        "start_pos\uff08wechatapi.client.base.section \u5c5e\u6027\uff09": [[0, "WechatAPI.Client.base.Section.start_pos", false]],
        "stop_auto_heartbeat() \uff08wechatapi.client.login.loginmixin \u65b9\u6cd5\uff09": [[0, "WechatAPI.Client.login.LoginMixin.stop_auto_heartbeat", false]],
        "sync_message() \uff08wechatapi.client.message.messagemixin \u65b9\u6cd5\uff09": [[0, "WechatAPI.Client.message.MessageMixin.sync_message", false]],
        "toolmixin\uff08wechatapi.client.tool \u4e2d\u7684\u7c7b\uff09": [[0, "WechatAPI.Client.tool.ToolMixin", false]],
        "update_login_status() \uff08wechatapi.client.protect.protect \u65b9\u6cd5\uff09": [[0, "WechatAPI.Client.protect.Protect.update_login_status", false]],
        "usermixin\uff08wechatapi.client.user \u4e2d\u7684\u7c7b\uff09": [[0, "WechatAPI.Client.user.UserMixin", false]],
        "username\uff08wechatapi.client.base.proxy \u5c5e\u6027\uff09": [[0, "WechatAPI.Client.base.Proxy.username", false]],
        "wav_byte_to_amr_base64()\uff08wechatapi.client.tool.toolmixin \u9759\u6001\u65b9\u6cd5\uff09": [[0, "WechatAPI.Client.tool.ToolMixin.wav_byte_to_amr_base64", false]],
        "wav_byte_to_amr_byte()\uff08wechatapi.client.tool.toolmixin \u9759\u6001\u65b9\u6cd5\uff09": [[0, "WechatAPI.Client.tool.ToolMixin.wav_byte_to_amr_byte", false]],
        "wav_byte_to_silk_base64()\uff08wechatapi.client.tool.toolmixin \u9759\u6001\u65b9\u6cd5\uff09": [[0, "WechatAPI.Client.tool.ToolMixin.wav_byte_to_silk_base64", false]],
        "wav_byte_to_silk_byte()\uff08wechatapi.client.tool.toolmixin \u9759\u6001\u65b9\u6cd5\uff09": [[0, "WechatAPI.Client.tool.ToolMixin.wav_byte_to_silk_byte", false]],
        "wechatapi.client.base": [[0, "module-WechatAPI.Client.base", false]],
        "wechatapi.client.chatroom": [[0, "module-WechatAPI.Client.chatroom", false]],
        "wechatapi.client.friend": [[0, "module-WechatAPI.Client.friend", false]],
        "wechatapi.client.hongbao": [[0, "module-WechatAPI.Client.hongbao", false]],
        "wechatapi.client.login": [[0, "module-WechatAPI.Client.login", false]],
        "wechatapi.client.message": [[0, "module-WechatAPI.Client.message", false]],
        "wechatapi.client.protect": [[0, "module-WechatAPI.Client.protect", false]],
        "wechatapi.client.tool": [[0, "module-WechatAPI.Client.tool", false]],
        "wechatapi.client.user": [[0, "module-WechatAPI.Client.user", false]],
        "wechatapiclientbase\uff08wechatapi.client.base \u4e2d\u7684\u7c7b\uff09": [[0, "WechatAPI.Client.base.WechatAPIClientBase", false]]
    },
    "objects": {
        "WechatAPI.Client": [[0, 0, 0, "-", "base"], [0, 0, 0, "-", "chatroom"], [0, 0, 0, "-", "friend"], [0, 0, 0, "-", "hongbao"], [0, 0, 0, "-", "login"], [0, 0, 0, "-", "message"], [0, 0, 0, "-", "protect"], [0, 0, 0, "-", "tool"], [0, 0, 0, "-", "user"]],
        "WechatAPI.Client.base": [[0, 1, 1, "", "Proxy"], [0, 1, 1, "", "Section"], [0, 1, 1, "", "WechatAPIClientBase"]],
        "WechatAPI.Client.base.Proxy": [[0, 2, 1, "", "ip"], [0, 2, 1, "", "password"], [0, 2, 1, "", "port"], [0, 2, 1, "", "username"]],
        "WechatAPI.Client.base.Section": [[0, 2, 1, "", "data_len"], [0, 2, 1, "", "start_pos"]],
        "WechatAPI.Client.base.WechatAPIClientBase": [[0, 3, 1, "", "error_handler"]],
        "WechatAPI.Client.chatroom": [[0, 1, 1, "", "ChatroomMixin"]],
        "WechatAPI.Client.chatroom.ChatroomMixin": [[0, 3, 1, "", "add_chatroom_member"], [0, 3, 1, "", "get_chatroom_announce"], [0, 3, 1, "", "get_chatroom_info"], [0, 3, 1, "", "get_chatroom_member_list"], [0, 3, 1, "", "get_chatroom_qrcode"], [0, 3, 1, "", "invite_chatroom_member"]],
        "WechatAPI.Client.friend": [[0, 1, 1, "", "FriendMixin"]],
        "WechatAPI.Client.friend.FriendMixin": [[0, 3, 1, "", "accept_friend"], [0, 3, 1, "", "get_contact"], [0, 3, 1, "", "get_contract_detail"], [0, 3, 1, "", "get_contract_list"], [0, 3, 1, "", "get_nickname"]],
        "WechatAPI.Client.hongbao": [[0, 1, 1, "", "HongBaoMixin"]],
        "WechatAPI.Client.hongbao.HongBaoMixin": [[0, 3, 1, "", "get_hongbao_detail"]],
        "WechatAPI.Client.login": [[0, 1, 1, "", "LoginMixin"]],
        "WechatAPI.Client.login.LoginMixin": [[0, 3, 1, "", "awaken_login"], [0, 3, 1, "", "check_login_uuid"], [0, 3, 1, "", "create_device_id"], [0, 3, 1, "", "create_device_name"], [0, 3, 1, "", "get_auto_heartbeat_status"], [0, 3, 1, "", "get_cached_info"], [0, 3, 1, "", "get_qr_code"], [0, 3, 1, "", "heartbeat"], [0, 3, 1, "", "is_running"], [0, 3, 1, "", "log_out"], [0, 3, 1, "", "start_auto_heartbeat"], [0, 3, 1, "", "stop_auto_heartbeat"]],
        "WechatAPI.Client.message": [[0, 1, 1, "", "MessageMixin"]],
        "WechatAPI.Client.message.MessageMixin": [[0, 3, 1, "", "_process_message_queue"], [0, 3, 1, "", "_queue_message"], [0, 3, 1, "", "_send_text_message"], [0, 3, 1, "", "revoke_message"], [0, 3, 1, "", "send_app_message"], [0, 3, 1, "", "send_card_message"], [0, 3, 1, "", "send_cdn_file_msg"], [0, 3, 1, "", "send_cdn_img_msg"], [0, 3, 1, "", "send_cdn_video_msg"], [0, 3, 1, "", "send_emoji_message"], [0, 3, 1, "", "send_image_message"], [0, 3, 1, "", "send_link_message"], [0, 3, 1, "", "send_text_message"], [0, 3, 1, "", "send_video_message"], [0, 3, 1, "", "send_voice_message"], [0, 3, 1, "", "sync_message"]],
        "WechatAPI.Client.protect": [[0, 1, 1, "", "Protect"], [0, 1, 1, "", "Singleton"]],
        "WechatAPI.Client.protect.Protect": [[0, 3, 1, "", "__init__"], [0, 3, 1, "", "check"], [0, 3, 1, "", "update_login_status"]],
        "WechatAPI.Client.protect.Singleton": [[0, 3, 1, "", "__call__"]],
        "WechatAPI.Client.tool": [[0, 1, 1, "", "ToolMixin"]],
        "WechatAPI.Client.tool.ToolMixin": [[0, 3, 1, "", "base64_to_byte"], [0, 3, 1, "", "base64_to_file"], [0, 3, 1, "", "byte_to_base64"], [0, 3, 1, "", "check_database"], [0, 3, 1, "", "download_attach"], [0, 3, 1, "", "download_image"], [0, 3, 1, "", "download_video"], [0, 3, 1, "", "download_voice"], [0, 3, 1, "", "file_to_base64"], [0, 3, 1, "", "set_proxy"], [0, 3, 1, "", "set_step"], [0, 3, 1, "", "silk_base64_to_wav_byte"], [0, 3, 1, "", "silk_byte_to_byte_wav_byte"], [0, 3, 1, "", "wav_byte_to_amr_base64"], [0, 3, 1, "", "wav_byte_to_amr_byte"], [0, 3, 1, "", "wav_byte_to_silk_base64"], [0, 3, 1, "", "wav_byte_to_silk_byte"]],
        "WechatAPI.Client.user": [[0, 1, 1, "", "UserMixin"]],
        "WechatAPI.Client.user.UserMixin": [[0, 3, 1, "", "get_my_qrcode"], [0, 3, 1, "", "get_profile"], [0, 3, 1, "", "is_logged_in"]]
    },
    "objnames": {
        "0": ["py", "module", "Python \u6a21\u5757"],
        "1": ["py", "class", "Python \u7c7b"],
        "2": ["py", "attribute", "Python \u5c5e\u6027"],
        "3": ["py", "method", "Python \u65b9\u6cd5"]
    },
    "objtypes": {"0": "py:module", "1": "py:class", "2": "py:attribute", "3": "py:method"},
    "terms": {
        "&&": 0,
        "--": 0,
        "10": 0,
        "15": 0,
        "20": 0,
        "30": 0,
        "300": 0,
        "300kb": 0,
        "30s": 0,
        "40": 0,
        "40s": 0,
        "50": 0,
        "__": 0,
        "__call__": 0,
        "__init__": 0,
        "_instanc": 0,
        "_process_message_queu": 0,
        "_queue_messag": 0,
        "_send_text_messag": 0,
        "accept": 0,
        "accept_friend": 0,
        "add": 0,
        "add_chatroom_memb": 0,
        "aes": 0,
        "aeskey": 0,
        "alia": 0,
        "amr": 0,
        "ani": 0,
        "announc": 0,
        "api": 0,
        "app": 0,
        "arg": 0,
        "async": 0,
        "at": 0,
        "attach": 0,
        "attach_id": 0,
        "auto": 0,
        "awaken": 0,
        "awaken_login": 0,
        "banprotect": 0,
        "base": 0,
        "base64": 0,
        "base64_str": 0,
        "base64_to_byt": 0,
        "base64_to_fil": 0,
        "bool": 0,
        "byte": 0,
        "byte_to_base64": 0,
        "cach": 0,
        "call": 0,
        "card": 0,
        "card_alia": 0,
        "card_nicknam": 0,
        "card_wxid": 0,
        "cdn": 0,
        "cdnmidimgurl": 0,
        "chatroom": 0,
        "chatroom_seq": 0,
        "chatroommixin": 0,
        "check": 0,
        "check_databas": 0,
        "check_login_uuid": 0,
        "class": 0,
        "client": 0,
        "client_msg_id": 0,
        "clientimgid": 0,
        "clientmsgid": 0,
        "code": 0,
        "contact": 0,
        "content": 0,
        "contract": 0,
        "count": 0,
        "creat": 0,
        "create_device_id": 0,
        "create_device_nam": 0,
        "create_tim": 0,
        "createtim": 0,
        "data": 0,
        "data_len": 0,
        "databas": 0,
        "databaseerror": 0,
        "default": 0,
        "descript": 0,
        "detail": 0,
        "devic": 0,
        "device_id": 0,
        "device_nam": 0,
        "dict": 0,
        "download": 0,
        "download_attach": 0,
        "download_imag": 0,
        "download_video": 0,
        "download_voic": 0,
        "emoji": 0,
        "emojiitem": 0,
        "encrypt": 0,
        "encrypt_key": 0,
        "encrypt_userinfo": 0,
        "error": 0,
        "error_handl": 0,
        "except": 0,
        "fals": 0,
        "file": 0,
        "file_nam": 0,
        "file_path": 0,
        "file_to_base64": 0,
        "format": 0,
        "friend": 0,
        "friendmixin": 0,
        "func": 0,
        "get": 0,
        "get_auto_heartbeat_status": 0,
        "get_cached_info": 0,
        "get_chatroom_announc": 0,
        "get_chatroom_info": 0,
        "get_chatroom_member_list": 0,
        "get_chatroom_qrcod": 0,
        "get_contact": 0,
        "get_contract_detail": 0,
        "get_contract_list": 0,
        "get_hongbao_detail": 0,
        "get_my_qrcod": 0,
        "get_nicknam": 0,
        "get_profil": 0,
        "get_qr_cod": 0,
        "handler": 0,
        "heartbeat": 0,
        "hongbao": 0,
        "hongbaomixin": 0,
        "id": 0,
        "ignor": 0,
        "ignore_protect": 0,
        "imag": 0,
        "image_base64": 0,
        "image_path": 0,
        "img": 0,
        "in": 0,
        "info": 0,
        "init": 0,
        "instanc": 0,
        "int": 0,
        "invit": 0,
        "invite_chatroom_memb": 0,
        "ip": 0,
        "ip\u5730\u5740": 0,
        "is": 0,
        "is_logged_in": 0,
        "is_run": 0,
        "json": 0,
        "json_resp": 0,
        "key": 0,
        "kwarg": 0,
        "len": 0,
        "length": 0,
        "link": 0,
        "list": 0,
        "log": 0,
        "log_out": 0,
        "login": 0,
        "login_device_id": 0,
        "login_stat": 0,
        "login_stat_path": 0,
        "login_tim": 0,
        "loginerror": 0,
        "loginmixin": 0,
        "marshallingerror": 0,
        "md5": 0,
        "member": 0,
        "messag": 0,
        "messagemixin": 0,
        "mmtls": 0,
        "mmtlserror": 0,
        "mp3": 0,
        "msg": 0,
        "msg_id": 0,
        "msgid": 0,
        "my": 0,
        "name": 0,
        "new": 0,
        "new_msg_id": 0,
        "newmsgid": 0,
        "nicknam": 0,
        "none": 0,
        "object": 0,
        "of": 0,
        "option": 0,
        "os": 0,
        "out": 0,
        "packeterror": 0,
        "parsepacketerror": 0,
        "password": 0,
        "path": 0,
        "pathlik": 0,
        "phone": 0,
        "port": 0,
        "pos": 0,
        "print": 0,
        "print_qr": 0,
        "process": 0,
        "profil": 0,
        "protect": 0,
        "proxi": 0,
        "qr": 0,
        "qrcode": 0,
        "queue": 0,
        "resp": 0,
        "revok": 0,
        "revoke_messag": 0,
        "run": 0,
        "scene": 0,
        "second": 0,
        "section": 0,
        "send": 0,
        "send_app_messag": 0,
        "send_card_messag": 0,
        "send_cdn_file_msg": 0,
        "send_cdn_img_msg": 0,
        "send_cdn_video_msg": 0,
        "send_emoji_messag": 0,
        "send_image_messag": 0,
        "send_link_messag": 0,
        "send_text_messag": 0,
        "send_video_messag": 0,
        "send_voice_messag": 0,
        "seq": 0,
        "set": 0,
        "set_proxi": 0,
        "set_step": 0,
        "silk": 0,
        "silk_base64": 0,
        "silk_base64_to_wav_byt": 0,
        "silk_byt": 0,
        "silk_byte_to_byte_wav_byt": 0,
        "singleton": 0,
        "start": 0,
        "start_auto_heartbeat": 0,
        "start_po": 0,
        "stat": 0,
        "static": 0,
        "status": 0,
        "step": 0,
        "stop": 0,
        "stop_auto_heartbeat": 0,
        "str": 0,
        "style": 0,
        "sync": 0,
        "sync_messag": 0,
        "text": 0,
        "thumb": 0,
        "thumb_url": 0,
        "time": 0,
        "titl": 0,
        "to": 0,
        "tool": 0,
        "toolmixin": 0,
        "total": 0,
        "total_length": 0,
        "true": 0,
        "tupl": 0,
        "type": 0,
        "union": 0,
        "unmarshallingerror": 0,
        "updat": 0,
        "update_login_status": 0,
        "url": 0,
        "user": 0,
        "userinfo": 0,
        "userloggedout": 0,
        "usermixin": 0,
        "usernam": 0,
        "uuid": 0,
        "v1": 0,
        "v1key": 0,
        "v2": 0,
        "v2key": 0,
        "valueerror": 0,
        "video": 0,
        "voic": 0,
        "voice_base64": 0,
        "voice_path": 0,
        "voiceurl": 0,
        "wav": 0,
        "wav_byt": 0,
        "wav_byte_to_amr_base64": 0,
        "wav_byte_to_amr_byt": 0,
        "wav_byte_to_silk_base64": 0,
        "wav_byte_to_silk_byt": 0,
        "wechatapi": 0,
        "wechatapiclientbas": 0,
        "wx": 0,
        "wx_seq": 0,
        "wxid": 0,
        "xml": 0,
        "\u4e00\u4e2a": 0,
        "\u4e00\u6b21": 0,
        "\u4e00\u76f4": 0,
        "\u4e0a\u4f20": 0,
        "\u4e0a\u6b21": 0,
        "\u4e0a\u9650": 0,
        "\u4e0b\u8f7d": 0,
        "\u4e0d\u5230": 0,
        "\u4e0d\u662f": 0,
        "\u4e2a\u4eba": 0,
        "\u4e3a\u7a7a": 0,
        "\u4e3b\u52a8": 0,
        "\u4e8c\u7ef4": 0,
        "\u4e8c\u7ef4\u7801": 0,
        "\u4ee3\u7406": 0,
        "\u4ee3\u7406\u670d\u52a1\u5668": 0,
        "\u4ee3\u7801": 0,
        "\u4ee5\u4e0a": 0,
        "\u4f4d\u7f6e": 0,
        "\u4f7f\u7528": 0,
        "\u4fdd\u5b58": 0,
        "\u4fe1\u606f": 0,
        "\u505c\u6b62": 0,
        "\u5143\u7c7b": 0,
        "\u5168\u5c40": 0,
        "\u516c\u544a": 0,
        "\u5173\u952e": 0,
        "\u5173\u952e\u5b57": 0,
        "\u5176\u4ed6": 0,
        "\u5185\u5bb9": 0,
        "\u5185\u8bf7": 0,
        "\u5217\u8868": 0,
        "\u521b\u5efa": 0,
        "\u521d\u59cb": 0,
        "\u521d\u59cb\u5316": 0,
        "\u522b\u540d": 0,
        "\u522b\u7528": 0,
        "\u52a0\u5bc6": 0,
        "\u52a0\u8f7d": 0,
        "\u52a1\u5668": 0,
        "\u5355\u4e2a": 0,
        "\u5355\u4f8b": 0,
        "\u5355\u5929": 0,
        "\u5361\u7247": 0,
        "\u538b\u7f29": 0,
        "\u53c2\u6570": 0,
        "\u53d1\u51fa": 0,
        "\u53d1\u751f": 0,
        "\u53d1\u751f\u53d8\u5316": 0,
        "\u53d1\u9001": 0,
        "\u53d8\u5316": 0,
        "\u53d8\u91cf": 0,
        "\u53ea\u6709": 0,
        "\u53ef\u4ee5": 0,
        "\u53ef\u538b": 0,
        "\u53ef\u538b\u7f29": 0,
        "\u540c\u6b65": 0,
        "\u540d\u7247": 0,
        "\u540d\u79f0": 0,
        "\u5426\u5219": 0,
        "\u54cd\u5e94": 0,
        "\u5524\u9192": 0,
        "\u56fe\u7247": 0,
        "\u5730\u5740": 0,
        "\u57fa\u7c7b": 0,
        "\u5904\u7406": 0,
        "\u5904\u7406\u9519\u8bef": 0,
        "\u5907\u6ce8": 0,
        "\u591a\u4e2a": 0,
        "\u5927\u4e8e": 0,
        "\u5931\u8d25": 0,
        "\u5982\u4e0b": 0,
        "\u5982\u679c": 0,
        "\u5b57\u5178": 0,
        "\u5b57\u7b26": 0,
        "\u5b57\u7b26\u4e32": 0,
        "\u5b57\u8282": 0,
        "\u5b58\u50a8": 0,
        "\u5b9e\u4f8b": 0,
        "\u5b9e\u9645": 0,
        "\u5ba2\u6237": 0,
        "\u5ba2\u6237\u7aef": 0,
        "\u5bc6\u7801": 0,
        "\u5bc6\u94a5": 0,
        "\u5bf9\u5e94": 0,
        "\u5bf9\u8c61": 0,
        "\u5c01\u9762": 0,
        "\u5c0f\u4e8e": 0,
        "\u5c0f\u65f6": 0,
        "\u5e8f\u5217": 0,
        "\u5e8f\u5217\u5316": 0,
        "\u5e94\u7528": 0,
        "\u5f00\u59cb": 0,
        "\u5f02\u5e38": 0,
        "\u5f02\u6b65": 0,
        "\u5f53\u524d": 0,
        "\u5f88\u6162": 0,
        "\u5fae\u4fe1": 0,
        "\u5fc3\u8df3": 0,
        "\u5ffd\u7565": 0,
        "\u603b\u957f": 0,
        "\u603b\u957f\u5ea6": 0,
        "\u6210\u529f": 0,
        "\u6210\u5458": 0,
        "\u6216\u8005": 0,
        "\u6237\u540d": 0,
        "\u6240\u793a": 0,
        "\u624b\u673a": 0,
        "\u624b\u673a\u53f7": 0,
        "\u6253\u5370": 0,
        "\u629b\u51fa": 0,
        "\u62a5\u9519": 0,
        "\u6302\u673a": 0,
        "\u6307\u5b9a": 0,
        "\u636e\u5e93": 0,
        "\u63a5\u53d7": 0,
        "\u63a5\u6536": 0,
        "\u63a7\u5236": 0,
        "\u63a7\u5236\u53f0": 0,
        "\u63a8\u8350": 0,
        "\u63cf\u8ff0": 0,
        "\u63d0\u4f9b": 0,
        "\u641c\u7d22": 0,
        "\u64a4\u56de": 0,
        "\u64cd\u4f5c": 0,
        "\u652f\u6301": 0,
        "\u6536\u4e0d\u5230": 0,
        "\u6536\u5230": 0,
        "\u6548\u679c": 0,
        "\u6570\u636e": 0,
        "\u6570\u636e\u5305": 0,
        "\u6570\u636e\u5e93": 0,
        "\u6587\u4ef6": 0,
        "\u6587\u4ef6\u540d": 0,
        "\u6587\u672c": 0,
        "\u65b9\u6cd5": 0,
        "\u65e0\u6cd5": 0,
        "\u65f6\u95f4": 0,
        "\u662f\u5426": 0,
        "\u6635\u79f0": 0,
        "\u66f4\u65b0": 0,
        "\u6700\u540e": 0,
        "\u6700\u591a": 0,
        "\u670d\u52a1": 0,
        "\u670d\u52a1\u5668": 0,
        "\u670d\u52a1\u5668\u7aef": 0,
        "\u673a\u5236": 0,
        "\u6765\u6e90": 0,
        "\u67e5\u8be2": 0,
        "\u6807\u9898": 0,
        "\u6837\u5f0f": 0,
        "\u6839\u636e": 0,
        "\u683c\u5f0f": 0,
        "\u68c0\u67e5": 0,
        "\u6a21\u5f0f": 0,
        "\u6b63\u5728": 0,
        "\u6b63\u5e38": 0,
        "\u6b65\u6570": 0,
        "\u6bcf\u5929": 0,
        "\u6ce8\u518c": 0,
        "\u6dfb\u52a0": 0,
        "\u6e90\u4ee3\u7801": 0,
        "\u72b6\u6001": 0,
        "\u751f\u53d8": 0,
        "\u751f\u6210": 0,
        "\u7528\u4e8e": 0,
        "\u7528\u6237\u540d": 0,
        "\u7533\u8bf7": 0,
        "\u7535\u8111": 0,
        "\u7565\u56fe": 0,
        "\u767b\u51fa": 0,
        "\u767b\u9646": 0,
        "\u786e\u4fdd": 0,
        "\u79d2\u6570": 0,
        "\u7a7a\u65f6": 0,
        "\u7aef\u53e3": 0,
        "\u7c7b\u578b": 0,
        "\u7ecf\u5e38": 0,
        "\u7f13\u5b58": 0,
        "\u7f16\u7801": 0,
        "\u7f29\u7565": 0,
        "\u7f29\u7565\u56fe": 0,
        "\u8054\u7cfb": 0,
        "\u8054\u7cfb\u4eba": 0,
        "\u81ea\u52a8": 0,
        "\u83b7\u53d6": 0,
        "\u8868\u60c5": 0,
        "\u89c6\u9891": 0,
        "\u89e3\u6790": 0,
        "\u89e3\u7801": 0,
        "\u8ba4\u8bc1": 0,
        "\u8bbe\u5907": 0,
        "\u8bbe\u7f6e": 0,
        "\u8be6\u60c5": 0,
        "\u8bed\u97f3": 0,
        "\u8bef\u7801": 0,
        "\u8bf7\u6c42": 0,
        "\u8c03\u7528": 0,
        "\u8d26\u53f7": 0,
        "\u8d77\u59cb": 0,
        "\u8d85\u8fc7": 0,
        "\u8def\u5f84": 0,
        "\u8df3\u8f6c": 0,
        "\u8f6c\u53d1": 0,
        "\u8f6c\u6362": 0,
        "\u8f93\u5165": 0,
        "\u8fc7\u671f": 0,
        "\u8fc7\u8be5": 0,
        "\u8fd0\u884c": 0,
        "\u8fd1\u671f": 0,
        "\u8fd4\u56de": 0,
        "\u8fd4\u56de\u503c": 0,
        "\u9000\u51fa": 0,
        "\u901a\u8fc7": 0,
        "\u901f\u5ea6": 0,
        "\u9080\u8bf7": 0,
        "\u914d\u7f6e": 0,
        "\u94fe\u63a5": 0,
        "\u9519\u8bef": 0,
        "\u9519\u8bef\u7801": 0,
        "\u957f\u5ea6": 0,
        "\u95f4\u9694": 0,
        "\u961f\u5217": 0,
        "\u9644\u4ef6": 0,
        "\u968f\u673a": 0,
        "\u9875\u9762": 0,
        "\u9891\u7e41": 0,
        "\u98ce\u63a7": 0,
        "\u9ad8\u6e05": 0,
        "\u9ed8\u8ba4": 0
    },
    "titles": ["WechatAPIClient"],
    "titleterms": {
        "wechatapicli": 0,
        "\u4fdd\u62a4": 0,
        "\u57fa\u7840": 0,
        "\u597d\u53cb": 0,
        "\u5de5\u5177": 0,
        "\u6d88\u606f": 0,
        "\u7528\u6237": 0,
        "\u767b\u5f55": 0,
        "\u7d22\u5f15": 0,
        "\u7ea2\u5305": 0,
        "\u7fa4\u804a": 0
    }
})