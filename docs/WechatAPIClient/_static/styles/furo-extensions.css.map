{"version": 3, "file": "styles/furo-extensions.css", "mappings": "AAGA,2BACE,oFACA,4CAKE,6CAHA,YACA,eAEA,CACA,kDACE,yCAEF,8CACE,sCAEJ,8CACE,kDAEJ,2BAGE,uBACA,cAHA,gBACA,UAEA,CAGA,yCACE,mBAEF,gDAEE,gDADA,YACA,CACA,sDACE,gDACF,yDACE,sCAEJ,+CACE,UACA,qDACE,UAGF,mDACE,eAEJ,yEAEE,4DAEA,mHASE,mBAPA,kBAEA,YADA,oBAGA,aADA,gBAIA,CAEA,qIAEE,WADA,UACA,CAEJ,uGACE,aAEF,iUAGE,cAEF,mHACE,aC1EJ,gCACE,mCAEF,0BAEE,mBAUA,8CACA,YAFA,mCAKA,eAZA,cAIA,YADA,YAYA,iCAdA,YAcA,CAEA,gCAEE,8CADA,gCACA,CAEF,gCAGE,6BADA,mCADA,YAEA,CAEF,kCAEE,cADA,oBACA,CACA,wCACE,cAEJ,8BACE,UCzCN,KAEE,6CAA8C,CAC9C,uDAAwD,CACxD,uDAAwD,CAGxD,iCAAsC,CAGtC,+CAAgD,CAChD,uDAAwD,CACxD,uDAAwD,CACxD,oDAAqD,CACrD,6DAA8D,CAC9D,6DAA8D,CAG9D,uDAAwD,CACxD,yDAA0D,CAC1D,4DAA6D,CAC7D,2DAA4D,CAC5D,8DAA+D,CAC/D,iEAAkE,CAClE,uDAAwD,CACxD,wDAAyD,CAG3D,gBACE,qFAGF,SACE,6EAEF,cACE,uFAEF,cACE,uFAEF,cACE,uFAGF,qBACE,eAEF,mBACE,WACA,eChDF,KACE,gDAAiD,CACjD,uDAAwD,CACxD,qDAAsD,CACtD,4DAA6D,CAC7D,oCAAqC,CACrC,2CAA4C,CAC5C,4CAA6C,CAC7C,mDAAoD,CACpD,wBAAyB,CACzB,oBAAqB,CACrB,6CAA8C,CAC9C,gCAAiC,CACjC,yDAA0D,CAC1D,uDAAwD,CACxD,8DAA+D,CCbjE,uBACE,eACA,eACA,gBAGF,iBACE,YACA,+EAGF,iBACE,mDACA", "sources": ["webpack:///./src/furo/assets/styles/extensions/_readthedocs.sass", "webpack:///./src/furo/assets/styles/extensions/_copybutton.sass", "webpack:///./src/furo/assets/styles/extensions/_sphinx-design.sass", "webpack:///./src/furo/assets/styles/extensions/_sphinx-inline-tabs.sass", "webpack:///./src/furo/assets/styles/extensions/_sphinx-panels.sass"], "sourcesContent": ["// This file contains the styles used for tweaking how ReadTheDoc's embedded\n// contents would show up inside the theme.\n\n#furo-sidebar-ad-placement\n  padding: var(--sidebar-item-spacing-vertical) var(--sidebar-item-spacing-horizontal)\n  .ethical-sidebar\n    // Remove the border and box-shadow.\n    border: none\n    box-shadow: none\n    // Manage the background colors.\n    background: var(--color-background-secondary)\n    &:hover\n      background: var(--color-background-hover)\n    // Ensure the text is legible.\n    a\n      color: var(--color-foreground-primary)\n\n  .ethical-callout a\n    color: var(--color-foreground-secondary) !important\n\n#furo-readthedocs-versions\n  position: static\n  width: 100%\n  background: transparent\n  display: block\n\n  // Make the background color fit with the theme's aesthetic.\n  .rst-versions\n    background: rgb(26, 28, 30)\n\n  .rst-current-version\n    cursor: unset\n    background: var(--color-sidebar-item-background)\n    &:hover\n      background: var(--color-sidebar-item-background)\n    .fa-book\n      color: var(--color-foreground-primary)\n\n  > .rst-other-versions\n    padding: 0\n    small\n      opacity: 1\n\n  .injected\n    .rst-versions\n      position: unset\n\n  &:hover,\n  &:focus-within\n    box-shadow: 0 0 0 1px var(--color-sidebar-background-border)\n\n    .rst-current-version\n      // Undo the tweaks done in RTD's CSS\n      font-size: inherit\n      line-height: inherit\n      height: auto\n      text-align: right\n      padding: 12px\n\n      // Match the rest of the body\n      background: #1a1c1e\n\n      .fa-book\n        float: left\n        color: white\n\n    .fa-caret-down\n      display: none\n\n    .rst-current-version,\n    .rst-other-versions,\n    .injected\n      display: block\n\n    > .rst-current-version\n      display: none\n", ".highlight\n  &:hover button.copybtn\n    color: var(--color-code-foreground)\n\n  button.copybtn\n    // Align things correctly\n    align-items: center\n\n    height: 1.25em\n    width: 1.25em\n\n    top: 0.625rem // $code-spacing-vertical\n    right: 0.5rem\n\n    // Make it look better\n    color: var(--color-background-item)\n    background-color: var(--color-code-background)\n    border: none\n\n    // Change to cursor to make it obvious that you can click on it\n    cursor: pointer\n\n    // Transition smoothly, for aesthetics\n    transition: color 300ms, opacity 300ms\n\n    &:hover\n      color: var(--color-brand-content)\n      background-color: var(--color-code-background)\n\n    &::after\n      display: none\n      color: var(--color-code-foreground)\n      background-color: transparent\n\n    &.success\n      transition: color 0ms\n      color: #22863a\n      &::after\n        display: block\n\n    svg\n      padding: 0\n", "body\n  // Colors\n  --sd-color-primary: var(--color-brand-primary)\n  --sd-color-primary-highlight: var(--color-brand-content)\n  --sd-color-primary-text: var(--color-background-primary)\n\n  // Shadows\n  --sd-color-shadow: rgba(0, 0, 0, 0.05)\n\n  // Cards\n  --sd-color-card-border: var(--color-card-border)\n  --sd-color-card-border-hover: var(--color-brand-content)\n  --sd-color-card-background: var(--color-card-background)\n  --sd-color-card-text: var(--color-foreground-primary)\n  --sd-color-card-header: var(--color-card-marginals-background)\n  --sd-color-card-footer: var(--color-card-marginals-background)\n\n  // Tabs\n  --sd-color-tabs-label-active: var(--color-brand-content)\n  --sd-color-tabs-label-hover: var(--color-foreground-muted)\n  --sd-color-tabs-label-inactive: var(--color-foreground-muted)\n  --sd-color-tabs-underline-active: var(--color-brand-content)\n  --sd-color-tabs-underline-hover: var(--color-foreground-border)\n  --sd-color-tabs-underline-inactive: var(--color-background-border)\n  --sd-color-tabs-overline: var(--color-background-border)\n  --sd-color-tabs-underline: var(--color-background-border)\n\n// Tabs\n.sd-tab-content\n  box-shadow: 0 -2px var(--sd-color-tabs-overline), 0 1px var(--sd-color-tabs-underline)\n\n// Shadows\n.sd-card  // Have a shadow by default\n  box-shadow: 0 0.1rem 0.25rem var(--sd-color-shadow), 0 0 0.0625rem rgba(0, 0, 0, 0.1)\n\n.sd-shadow-sm\n  box-shadow: 0 0.1rem 0.25rem var(--sd-color-shadow), 0 0 0.0625rem rgba(0, 0, 0, 0.1) !important\n\n.sd-shadow-md\n  box-shadow: 0 0.3rem 0.75rem var(--sd-color-shadow), 0 0 0.0625rem rgba(0, 0, 0, 0.1) !important\n\n.sd-shadow-lg\n  box-shadow: 0 0.6rem 1.5rem var(--sd-color-shadow), 0 0 0.0625rem rgba(0, 0, 0, 0.1) !important\n\n// Cards\n.sd-card-hover:hover  // Don't change scale on hover\n  transform: none\n\n.sd-cards-carousel  // Have a bit of gap in the carousel by default\n  gap: 0.25rem\n  padding: 0.25rem\n", "// This file contains styles to tweak sphinx-inline-tabs to work well with Furo.\n\nbody\n  --tabs--label-text: var(--color-foreground-muted)\n  --tabs--label-text--hover: var(--color-foreground-muted)\n  --tabs--label-text--active: var(--color-brand-content)\n  --tabs--label-text--active--hover: var(--color-brand-content)\n  --tabs--label-background: transparent\n  --tabs--label-background--hover: transparent\n  --tabs--label-background--active: transparent\n  --tabs--label-background--active--hover: transparent\n  --tabs--padding-x: 0.25em\n  --tabs--margin-x: 1em\n  --tabs--border: var(--color-background-border)\n  --tabs--label-border: transparent\n  --tabs--label-border--hover: var(--color-foreground-muted)\n  --tabs--label-border--active: var(--color-brand-content)\n  --tabs--label-border--active--hover: var(--color-brand-content)\n", "// This file contains styles to tweak sphinx-panels to work well with Furo.\n\n// sphinx-panels includes Bootstrap 4, which uses .container which can conflict\n// with docutils' `.. container::` directive.\n[role=\"main\"] .container\n  max-width: initial\n  padding-left: initial\n  padding-right: initial\n\n// Make the panels look nicer!\n.shadow.docutils\n  border: none\n  box-shadow: 0 0.2rem 0.5rem rgba(0, 0, 0, 0.05), 0 0 0.0625rem rgba(0, 0, 0, 0.1) !important\n\n// Make panel colors respond to dark mode\n.sphinx-bs .card\n  background-color: var(--color-background-secondary)\n  color: var(--color-foreground)\n"], "names": [], "sourceRoot": ""}