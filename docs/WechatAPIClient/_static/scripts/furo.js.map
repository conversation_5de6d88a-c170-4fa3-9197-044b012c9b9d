{"version": 3, "file": "scripts/furo.js", "mappings": ";iCAAA,MAQWA,SAWS,IAAX,EAAAC,EACH,EAAAA,EACkB,oBAAXC,OACLA,OACAC,KAbO,EAAF,WACP,OAaJ,SAAUD,GACR,aAMA,IAAIE,EAAW,CAEbC,SAAU,SACVC,aAAc,SAGdC,QAAQ,EACRC,YAAa,SAGbC,OAAQ,EACRC,QAAQ,EAGRC,QAAQ,GA6BNC,EAAY,SAAUC,EAAMC,EAAMC,GAEpC,GAAKA,EAAOC,SAASL,OAArB,CAGA,IAAIM,EAAQ,IAAIC,YAAYL,EAAM,CAChCM,SAAS,EACTC,YAAY,EACZL,OAAQA,IAIVD,EAAKO,cAAcJ,EAVgB,CAWrC,EAOIK,EAAe,SAAUR,GAC3B,IAAIS,EAAW,EACf,GAAIT,EAAKU,aACP,KAAOV,GACLS,GAAYT,EAAKW,UACjBX,EAAOA,EAAKU,aAGhB,OAAOD,GAAY,EAAIA,EAAW,CACpC,EAMIG,EAAe,SAAUC,GACvBA,GACFA,EAASC,MAAK,SAAUC,EAAOC,GAG7B,OAFcR,EAAaO,EAAME,SACnBT,EAAaQ,EAAMC,UACF,EACxB,CACT,GAEJ,EAwCIC,EAAW,SAAUlB,EAAME,EAAUiB,GACvC,IAAIC,EAASpB,EAAKqB,wBACd1B,EAnCU,SAAUO,GAExB,MAA+B,mBAApBA,EAASP,OACX2B,WAAWpB,EAASP,UAItB2B,WAAWpB,EAASP,OAC7B,CA2Be4B,CAAUrB,GACvB,OAAIiB,EAEAK,SAASJ,EAAOD,OAAQ,KACvB/B,EAAOqC,aAAeC,SAASC,gBAAgBC,cAG7CJ,SAASJ,EAAOS,IAAK,KAAOlC,CACrC,EAMImC,EAAa,WACf,OACEC,KAAKC,KAAK5C,EAAOqC,YAAcrC,EAAO6C,cAnCjCF,KAAKG,IACVR,SAASS,KAAKC,aACdV,SAASC,gBAAgBS,aACzBV,SAASS,KAAKE,aACdX,SAASC,gBAAgBU,aACzBX,SAASS,KAAKP,aACdF,SAASC,gBAAgBC,aAkC7B,EAmBIU,EAAY,SAAUzB,EAAUX,GAClC,IAAIqC,EAAO1B,EAASA,EAAS2B,OAAS,GACtC,GAbgB,SAAUC,EAAMvC,GAChC,SAAI4B,MAAgBZ,EAASuB,EAAKxB,QAASf,GAAU,GAEvD,CAUMwC,CAAYH,EAAMrC,GAAW,OAAOqC,EACxC,IAAK,IAAII,EAAI9B,EAAS2B,OAAS,EAAGG,GAAK,EAAGA,IACxC,GAAIzB,EAASL,EAAS8B,GAAG1B,QAASf,GAAW,OAAOW,EAAS8B,EAEjE,EAOIC,EAAmB,SAAUC,EAAK3C,GAEpC,GAAKA,EAAST,QAAWoD,EAAIC,WAA7B,CAGA,IAAIC,EAAKF,EAAIC,WAAWE,QAAQ,MAC3BD,IAGLA,EAAGE,UAAUC,OAAOhD,EAASR,aAG7BkD,EAAiBG,EAAI7C,GAV0B,CAWjD,EAOIiD,EAAa,SAAUC,EAAOlD,GAEhC,GAAKkD,EAAL,CAGA,IAAIL,EAAKK,EAAMP,IAAIG,QAAQ,MACtBD,IAGLA,EAAGE,UAAUC,OAAOhD,EAASX,UAC7B6D,EAAMnC,QAAQgC,UAAUC,OAAOhD,EAASV,cAGxCoD,EAAiBG,EAAI7C,GAGrBJ,EAAU,oBAAqBiD,EAAI,CACjCM,KAAMD,EAAMP,IACZ5B,QAASmC,EAAMnC,QACff,SAAUA,IAjBM,CAmBpB,EAOIoD,EAAiB,SAAUT,EAAK3C,GAElC,GAAKA,EAAST,OAAd,CAGA,IAAIsD,EAAKF,EAAIC,WAAWE,QAAQ,MAC3BD,IAGLA,EAAGE,UAAUM,IAAIrD,EAASR,aAG1B4D,EAAeP,EAAI7C,GAVS,CAW9B,EA6LA,OA1JkB,SAAUsD,EAAUC,GAKpC,IACIC,EAAU7C,EAAU8C,EAASC,EAAS1D,EADtC2D,EAAa,CAUjBA,MAAmB,WAEjBH,EAAWhC,SAASoC,iBAAiBN,GAGrC3C,EAAW,GAGXkD,MAAMC,UAAUC,QAAQC,KAAKR,GAAU,SAAUjB,GAE/C,IAAIxB,EAAUS,SAASyC,eACrBC,mBAAmB3B,EAAK4B,KAAKC,OAAO,KAEjCrD,GAGLJ,EAAS0D,KAAK,CACZ1B,IAAKJ,EACLxB,QAASA,GAEb,IAGAL,EAAaC,EACf,EAKAgD,OAAoB,WAElB,IAAIW,EAASlC,EAAUzB,EAAUX,GAG5BsE,EASDb,GAAWa,EAAOvD,UAAY0C,EAAQ1C,UAG1CkC,EAAWQ,EAASzD,GAzFT,SAAUkD,EAAOlD,GAE9B,GAAKkD,EAAL,CAGA,IAAIL,EAAKK,EAAMP,IAAIG,QAAQ,MACtBD,IAGLA,EAAGE,UAAUM,IAAIrD,EAASX,UAC1B6D,EAAMnC,QAAQgC,UAAUM,IAAIrD,EAASV,cAGrC8D,EAAeP,EAAI7C,GAGnBJ,EAAU,kBAAmBiD,EAAI,CAC/BM,KAAMD,EAAMP,IACZ5B,QAASmC,EAAMnC,QACff,SAAUA,IAjBM,CAmBpB,CAqEIuE,CAASD,EAAQtE,GAGjByD,EAAUa,GAfJb,IACFR,EAAWQ,EAASzD,GACpByD,EAAU,KAchB,GAMIe,EAAgB,SAAUvE,GAExByD,GACFxE,EAAOuF,qBAAqBf,GAI9BA,EAAUxE,EAAOwF,sBAAsBf,EAAWgB,OACpD,EAMIC,EAAgB,SAAU3E,GAExByD,GACFxE,EAAOuF,qBAAqBf,GAI9BA,EAAUxE,EAAOwF,uBAAsB,WACrChE,EAAaC,GACbgD,EAAWgB,QACb,GACF,EAkDA,OA7CAhB,EAAWkB,QAAU,WAEfpB,GACFR,EAAWQ,EAASzD,GAItBd,EAAO4F,oBAAoB,SAAUN,GAAe,GAChDxE,EAASN,QACXR,EAAO4F,oBAAoB,SAAUF,GAAe,GAItDjE,EAAW,KACX6C,EAAW,KACXC,EAAU,KACVC,EAAU,KACV1D,EAAW,IACb,EAOEA,EA3XS,WACX,IAAI+E,EAAS,CAAC,EAOd,OANAlB,MAAMC,UAAUC,QAAQC,KAAKgB,WAAW,SAAUC,GAChD,IAAK,IAAIC,KAAOD,EAAK,CACnB,IAAKA,EAAIE,eAAeD,GAAM,OAC9BH,EAAOG,GAAOD,EAAIC,EACpB,CACF,IACOH,CACT,CAkXeK,CAAOhG,EAAUmE,GAAW,CAAC,GAGxCI,EAAW0B,QAGX1B,EAAWgB,SAGXzF,EAAOoG,iBAAiB,SAAUd,GAAe,GAC7CxE,EAASN,QACXR,EAAOoG,iBAAiB,SAAUV,GAAe,GAS9CjB,CACT,CAOF,CArcW4B,CAAQvG,EAChB,UAFM,SAEN,uBCXDwG,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,GAE5B,IAAIC,EAAeH,EAAyBE,GAC5C,QAAqBE,IAAjBD,EACH,OAAOA,EAAaE,QAGrB,IAAIC,EAASN,EAAyBE,GAAY,CAGjDG,QAAS,CAAC,GAOX,OAHAE,EAAoBL,GAAU1B,KAAK8B,EAAOD,QAASC,EAAQA,EAAOD,QAASJ,GAGpEK,EAAOD,OACf,CCrBAJ,EAAoBO,EAAKF,IACxB,IAAIG,EAASH,GAAUA,EAAOI,WAC7B,IAAOJ,EAAiB,QACxB,IAAM,EAEP,OADAL,EAAoBU,EAAEF,EAAQ,CAAEG,EAAGH,IAC5BA,CAAM,ECLdR,EAAoBU,EAAI,CAACN,EAASQ,KACjC,IAAI,IAAInB,KAAOmB,EACXZ,EAAoBa,EAAED,EAAYnB,KAASO,EAAoBa,EAAET,EAASX,IAC5EqB,OAAOC,eAAeX,EAASX,EAAK,CAAEuB,YAAY,EAAMC,IAAKL,EAAWnB,IAE1E,ECNDO,EAAoBxG,EAAI,WACvB,GAA0B,iBAAf0H,WAAyB,OAAOA,WAC3C,IACC,OAAOxH,MAAQ,IAAIyH,SAAS,cAAb,EAChB,CAAE,MAAOC,GACR,GAAsB,iBAAX3H,OAAqB,OAAOA,MACxC,CACA,CAPuB,GCAxBuG,EAAoBa,EAAI,CAACrB,EAAK6B,IAAUP,OAAOzC,UAAUqB,eAAenB,KAAKiB,EAAK6B,4CCK9EC,EAAY,KACZC,EAAS,KACTC,EAAgBzF,SAASC,gBAAgByF,UAC7C,MAAMC,EAAmB,GA8EzB,SAASC,IACP,MAAMC,EAAeC,aAAaC,QAAQ,UAAY,OAZxD,IAAkBC,EACH,WADGA,EAaItI,OAAOuI,WAAW,gCAAgCC,QAI/C,SAAjBL,EACO,QACgB,SAAhBA,EACA,OAEA,OAIU,SAAjBA,EACO,OACgB,QAAhBA,EACA,QAEA,SA9BoB,SAATG,GAA4B,SAATA,IACzCG,QAAQC,MAAM,2BAA2BJ,yBACzCA,EAAO,QAGThG,SAASS,KAAK4F,QAAQC,MAAQN,EAC9BF,aAAaS,QAAQ,QAASP,GAC9BG,QAAQK,IAAI,cAAcR,UA0B5B,CAkDA,SAASnC,KART,WAEE,MAAM4C,EAAUzG,SAAS0G,uBAAuB,gBAChDrE,MAAMsE,KAAKF,GAASlE,SAASqE,IAC3BA,EAAI9C,iBAAiB,QAAS8B,EAAe,GAEjD,CAGEiB,GA9CF,WAEE,IAAIC,EAA6B,EAC7BC,GAAU,EAEdrJ,OAAOoG,iBAAiB,UAAU,SAAUuB,GAC1CyB,EAA6BpJ,OAAOsJ,QAE/BD,IACHrJ,OAAOwF,uBAAsB,WAzDnC,IAAuB+D,GAxDvB,SAAgCA,GAC9B,MAAMC,EAAY7G,KAAK8G,MAAM3B,EAAO7F,wBAAwBQ,KAE5DgG,QAAQK,IAAI,cAAcU,KACT,GAAbA,GAAkBD,GAAaC,EACjC1B,EAAOjE,UAAUM,IAAI,YAErB2D,EAAOjE,UAAUC,OAAO,WAE5B,EAgDE4F,CADqBH,EA0DDH,GAvGtB,SAAmCG,GAC7BA,EAAYtB,EACd3F,SAASC,gBAAgBsB,UAAUC,OAAO,oBAEtCyF,EAAYxB,EACdzF,SAASC,gBAAgBsB,UAAUM,IAAI,oBAC9BoF,EAAYxB,GACrBzF,SAASC,gBAAgBsB,UAAUC,OAAO,oBAG9CiE,EAAgBwB,CAClB,CAoCEI,CAA0BJ,GAlC5B,SAA6BA,GACT,OAAd1B,IAKa,GAAb0B,EACF1B,EAAU+B,SAAS,EAAG,GAGtBjH,KAAKC,KAAK2G,IACV5G,KAAK8G,MAAMnH,SAASC,gBAAgBS,aAAehD,OAAOqC,aAE1DwF,EAAU+B,SAAS,EAAG/B,EAAU7E,cAGhBV,SAASuH,cAAc,mBAc3C,CAKEC,CAAoBP,GAwDdF,GAAU,CACZ,IAEAA,GAAU,EAEd,IACArJ,OAAO+J,QACT,CA6BEC,GA1BkB,OAAdnC,GAKJ,IAAI,IAAJ,CAAY,cAAe,CACzBrH,QAAQ,EACRyJ,WAAW,EACX9J,SAAU,iBACVI,OAAQ,KACN,IAAI2J,EAAMhI,WAAWiI,iBAAiB7H,SAASC,iBAAiB6H,UAChE,OAAOtC,EAAO7F,wBAAwBoI,OAAS,IAAMH,EAAM,CAAC,GAiBlE,CAcA5H,SAAS8D,iBAAiB,oBAT1B,WACE9D,SAASS,KAAKW,WAAWG,UAAUC,OAAO,SAE1CgE,EAASxF,SAASuH,cAAc,UAChChC,EAAYvF,SAASuH,cAAc,eAEnC1D,GACF", "sources": ["webpack:///./src/furo/assets/scripts/gumshoe-patched.js", "webpack:///webpack/bootstrap", "webpack:///webpack/runtime/compat get default export", "webpack:///webpack/runtime/define property getters", "webpack:///webpack/runtime/global", "webpack:///webpack/runtime/hasOwnProperty shorthand", "webpack:///./src/furo/assets/scripts/furo.js"], "sourcesContent": ["/*!\n * gumshoejs v5.1.2 (patched by @pradyunsg)\n * A simple, framework-agnostic scrollspy script.\n * (c) 2019 <PERSON>\n * MIT License\n * http://github.com/cferdinandi/gumshoe\n */\n\n(function (root, factory) {\n  if (typeof define === \"function\" && define.amd) {\n    define([], function () {\n      return factory(root);\n    });\n  } else if (typeof exports === \"object\") {\n    module.exports = factory(root);\n  } else {\n    root.Gumshoe = factory(root);\n  }\n})(\n  typeof global !== \"undefined\"\n    ? global\n    : typeof window !== \"undefined\"\n      ? window\n      : this,\n  function (window) {\n    \"use strict\";\n\n    //\n    // Defaults\n    //\n\n    var defaults = {\n      // Active classes\n      navClass: \"active\",\n      contentClass: \"active\",\n\n      // Nested navigation\n      nested: false,\n      nestedClass: \"active\",\n\n      // Offset & reflow\n      offset: 0,\n      reflow: false,\n\n      // Event support\n      events: true,\n    };\n\n    //\n    // Methods\n    //\n\n    /**\n     * Merge two or more objects together.\n     * @param   {Object}   objects  The objects to merge together\n     * @returns {Object}            Merged values of defaults and options\n     */\n    var extend = function () {\n      var merged = {};\n      Array.prototype.forEach.call(arguments, function (obj) {\n        for (var key in obj) {\n          if (!obj.hasOwnProperty(key)) return;\n          merged[key] = obj[key];\n        }\n      });\n      return merged;\n    };\n\n    /**\n     * Emit a custom event\n     * @param  {String} type   The event type\n     * @param  {Node}   elem   The element to attach the event to\n     * @param  {Object} detail Any details to pass along with the event\n     */\n    var emitEvent = function (type, elem, detail) {\n      // Make sure events are enabled\n      if (!detail.settings.events) return;\n\n      // Create a new event\n      var event = new CustomEvent(type, {\n        bubbles: true,\n        cancelable: true,\n        detail: detail,\n      });\n\n      // Dispatch the event\n      elem.dispatchEvent(event);\n    };\n\n    /**\n     * Get an element's distance from the top of the Document.\n     * @param  {Node} elem The element\n     * @return {Number}    Distance from the top in pixels\n     */\n    var getOffsetTop = function (elem) {\n      var location = 0;\n      if (elem.offsetParent) {\n        while (elem) {\n          location += elem.offsetTop;\n          elem = elem.offsetParent;\n        }\n      }\n      return location >= 0 ? location : 0;\n    };\n\n    /**\n     * Sort content from first to last in the DOM\n     * @param  {Array} contents The content areas\n     */\n    var sortContents = function (contents) {\n      if (contents) {\n        contents.sort(function (item1, item2) {\n          var offset1 = getOffsetTop(item1.content);\n          var offset2 = getOffsetTop(item2.content);\n          if (offset1 < offset2) return -1;\n          return 1;\n        });\n      }\n    };\n\n    /**\n     * Get the offset to use for calculating position\n     * @param  {Object} settings The settings for this instantiation\n     * @return {Float}           The number of pixels to offset the calculations\n     */\n    var getOffset = function (settings) {\n      // if the offset is a function run it\n      if (typeof settings.offset === \"function\") {\n        return parseFloat(settings.offset());\n      }\n\n      // Otherwise, return it as-is\n      return parseFloat(settings.offset);\n    };\n\n    /**\n     * Get the document element's height\n     * @private\n     * @returns {Number}\n     */\n    var getDocumentHeight = function () {\n      return Math.max(\n        document.body.scrollHeight,\n        document.documentElement.scrollHeight,\n        document.body.offsetHeight,\n        document.documentElement.offsetHeight,\n        document.body.clientHeight,\n        document.documentElement.clientHeight,\n      );\n    };\n\n    /**\n     * Determine if an element is in view\n     * @param  {Node}    elem     The element\n     * @param  {Object}  settings The settings for this instantiation\n     * @param  {Boolean} bottom   If true, check if element is above bottom of viewport instead\n     * @return {Boolean}          Returns true if element is in the viewport\n     */\n    var isInView = function (elem, settings, bottom) {\n      var bounds = elem.getBoundingClientRect();\n      var offset = getOffset(settings);\n      if (bottom) {\n        return (\n          parseInt(bounds.bottom, 10) <\n          (window.innerHeight || document.documentElement.clientHeight)\n        );\n      }\n      return parseInt(bounds.top, 10) <= offset;\n    };\n\n    /**\n     * Check if at the bottom of the viewport\n     * @return {Boolean} If true, page is at the bottom of the viewport\n     */\n    var isAtBottom = function () {\n      if (\n        Math.ceil(window.innerHeight + window.pageYOffset) >=\n        getDocumentHeight()\n      )\n        return true;\n      return false;\n    };\n\n    /**\n     * Check if the last item should be used (even if not at the top of the page)\n     * @param  {Object} item     The last item\n     * @param  {Object} settings The settings for this instantiation\n     * @return {Boolean}         If true, use the last item\n     */\n    var useLastItem = function (item, settings) {\n      if (isAtBottom() && isInView(item.content, settings, true)) return true;\n      return false;\n    };\n\n    /**\n     * Get the active content\n     * @param  {Array}  contents The content areas\n     * @param  {Object} settings The settings for this instantiation\n     * @return {Object}          The content area and matching navigation link\n     */\n    var getActive = function (contents, settings) {\n      var last = contents[contents.length - 1];\n      if (useLastItem(last, settings)) return last;\n      for (var i = contents.length - 1; i >= 0; i--) {\n        if (isInView(contents[i].content, settings)) return contents[i];\n      }\n    };\n\n    /**\n     * Deactivate parent navs in a nested navigation\n     * @param  {Node}   nav      The starting navigation element\n     * @param  {Object} settings The settings for this instantiation\n     */\n    var deactivateNested = function (nav, settings) {\n      // If nesting isn't activated, bail\n      if (!settings.nested || !nav.parentNode) return;\n\n      // Get the parent navigation\n      var li = nav.parentNode.closest(\"li\");\n      if (!li) return;\n\n      // Remove the active class\n      li.classList.remove(settings.nestedClass);\n\n      // Apply recursively to any parent navigation elements\n      deactivateNested(li, settings);\n    };\n\n    /**\n     * Deactivate a nav and content area\n     * @param  {Object} items    The nav item and content to deactivate\n     * @param  {Object} settings The settings for this instantiation\n     */\n    var deactivate = function (items, settings) {\n      // Make sure there are items to deactivate\n      if (!items) return;\n\n      // Get the parent list item\n      var li = items.nav.closest(\"li\");\n      if (!li) return;\n\n      // Remove the active class from the nav and content\n      li.classList.remove(settings.navClass);\n      items.content.classList.remove(settings.contentClass);\n\n      // Deactivate any parent navs in a nested navigation\n      deactivateNested(li, settings);\n\n      // Emit a custom event\n      emitEvent(\"gumshoeDeactivate\", li, {\n        link: items.nav,\n        content: items.content,\n        settings: settings,\n      });\n    };\n\n    /**\n     * Activate parent navs in a nested navigation\n     * @param  {Node}   nav      The starting navigation element\n     * @param  {Object} settings The settings for this instantiation\n     */\n    var activateNested = function (nav, settings) {\n      // If nesting isn't activated, bail\n      if (!settings.nested) return;\n\n      // Get the parent navigation\n      var li = nav.parentNode.closest(\"li\");\n      if (!li) return;\n\n      // Add the active class\n      li.classList.add(settings.nestedClass);\n\n      // Apply recursively to any parent navigation elements\n      activateNested(li, settings);\n    };\n\n    /**\n     * Activate a nav and content area\n     * @param  {Object} items    The nav item and content to activate\n     * @param  {Object} settings The settings for this instantiation\n     */\n    var activate = function (items, settings) {\n      // Make sure there are items to activate\n      if (!items) return;\n\n      // Get the parent list item\n      var li = items.nav.closest(\"li\");\n      if (!li) return;\n\n      // Add the active class to the nav and content\n      li.classList.add(settings.navClass);\n      items.content.classList.add(settings.contentClass);\n\n      // Activate any parent navs in a nested navigation\n      activateNested(li, settings);\n\n      // Emit a custom event\n      emitEvent(\"gumshoeActivate\", li, {\n        link: items.nav,\n        content: items.content,\n        settings: settings,\n      });\n    };\n\n    /**\n     * Create the Constructor object\n     * @param {String} selector The selector to use for navigation items\n     * @param {Object} options  User options and settings\n     */\n    var Constructor = function (selector, options) {\n      //\n      // Variables\n      //\n\n      var publicAPIs = {};\n      var navItems, contents, current, timeout, settings;\n\n      //\n      // Methods\n      //\n\n      /**\n       * Set variables from DOM elements\n       */\n      publicAPIs.setup = function () {\n        // Get all nav items\n        navItems = document.querySelectorAll(selector);\n\n        // Create contents array\n        contents = [];\n\n        // Loop through each item, get it's matching content, and push to the array\n        Array.prototype.forEach.call(navItems, function (item) {\n          // Get the content for the nav item\n          var content = document.getElementById(\n            decodeURIComponent(item.hash.substr(1)),\n          );\n          if (!content) return;\n\n          // Push to the contents array\n          contents.push({\n            nav: item,\n            content: content,\n          });\n        });\n\n        // Sort contents by the order they appear in the DOM\n        sortContents(contents);\n      };\n\n      /**\n       * Detect which content is currently active\n       */\n      publicAPIs.detect = function () {\n        // Get the active content\n        var active = getActive(contents, settings);\n\n        // if there's no active content, deactivate and bail\n        if (!active) {\n          if (current) {\n            deactivate(current, settings);\n            current = null;\n          }\n          return;\n        }\n\n        // If the active content is the one currently active, do nothing\n        if (current && active.content === current.content) return;\n\n        // Deactivate the current content and activate the new content\n        deactivate(current, settings);\n        activate(active, settings);\n\n        // Update the currently active content\n        current = active;\n      };\n\n      /**\n       * Detect the active content on scroll\n       * Debounced for performance\n       */\n      var scrollHandler = function (event) {\n        // If there's a timer, cancel it\n        if (timeout) {\n          window.cancelAnimationFrame(timeout);\n        }\n\n        // Setup debounce callback\n        timeout = window.requestAnimationFrame(publicAPIs.detect);\n      };\n\n      /**\n       * Update content sorting on resize\n       * Debounced for performance\n       */\n      var resizeHandler = function (event) {\n        // If there's a timer, cancel it\n        if (timeout) {\n          window.cancelAnimationFrame(timeout);\n        }\n\n        // Setup debounce callback\n        timeout = window.requestAnimationFrame(function () {\n          sortContents(contents);\n          publicAPIs.detect();\n        });\n      };\n\n      /**\n       * Destroy the current instantiation\n       */\n      publicAPIs.destroy = function () {\n        // Undo DOM changes\n        if (current) {\n          deactivate(current, settings);\n        }\n\n        // Remove event listeners\n        window.removeEventListener(\"scroll\", scrollHandler, false);\n        if (settings.reflow) {\n          window.removeEventListener(\"resize\", resizeHandler, false);\n        }\n\n        // Reset variables\n        contents = null;\n        navItems = null;\n        current = null;\n        timeout = null;\n        settings = null;\n      };\n\n      /**\n       * Initialize the current instantiation\n       */\n      var init = function () {\n        // Merge user options into defaults\n        settings = extend(defaults, options || {});\n\n        // Setup variables based on the current DOM\n        publicAPIs.setup();\n\n        // Find the currently active content\n        publicAPIs.detect();\n\n        // Setup event listeners\n        window.addEventListener(\"scroll\", scrollHandler, false);\n        if (settings.reflow) {\n          window.addEventListener(\"resize\", resizeHandler, false);\n        }\n      };\n\n      //\n      // Initialize and return the public APIs\n      //\n\n      init();\n      return publicAPIs;\n    };\n\n    //\n    // Return the Constructor\n    //\n\n    return Constructor;\n  },\n);\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.g = (function() {\n\tif (typeof globalThis === 'object') return globalThis;\n\ttry {\n\t\treturn this || new Function('return this')();\n\t} catch (e) {\n\t\tif (typeof window === 'object') return window;\n\t}\n})();", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "import <PERSON><PERSON><PERSON> from \"./gumshoe-patched.js\";\n\n////////////////////////////////////////////////////////////////////////////////\n// Scroll Handling\n////////////////////////////////////////////////////////////////////////////////\nvar tocScroll = null;\nvar header = null;\nvar lastScrollTop = document.documentElement.scrollTop;\nconst GO_TO_TOP_OFFSET = 64;\n\nfunction scrollHandlerForHeader(positionY) {\n  const headerTop = Math.floor(header.getBoundingClientRect().top);\n\n  console.log(`headerTop: ${headerTop}`);\n  if (headerTop == 0 && positionY != headerTop) {\n    header.classList.add(\"scrolled\");\n  } else {\n    header.classList.remove(\"scrolled\");\n  }\n}\n\nfunction scrollHandlerForBackToTop(positionY) {\n  if (positionY < GO_TO_TOP_OFFSET) {\n    document.documentElement.classList.remove(\"show-back-to-top\");\n  } else {\n    if (positionY < lastScrollTop) {\n      document.documentElement.classList.add(\"show-back-to-top\");\n    } else if (positionY > lastScrollTop) {\n      document.documentElement.classList.remove(\"show-back-to-top\");\n    }\n  }\n  lastScrollTop = positionY;\n}\n\nfunction scrollHandlerForTOC(positionY) {\n  if (tocScroll === null) {\n    return;\n  }\n\n  // top of page.\n  if (positionY == 0) {\n    tocScroll.scrollTo(0, 0);\n  } else if (\n    // bottom of page.\n    Math.ceil(positionY) >=\n    Math.floor(document.documentElement.scrollHeight - window.innerHeight)\n  ) {\n    tocScroll.scrollTo(0, tocScroll.scrollHeight);\n  } else {\n    // somewhere in the middle.\n    const current = document.querySelector(\".scroll-current\");\n    if (current == null) {\n      return;\n    }\n\n    // https://github.com/pypa/pip/issues/9159 This breaks scroll behaviours.\n    // // scroll the currently \"active\" heading in toc, into view.\n    // const rect = current.getBoundingClientRect();\n    // if (0 > rect.top) {\n    //   current.scrollIntoView(true); // the argument is \"alignTop\"\n    // } else if (rect.bottom > window.innerHeight) {\n    //   current.scrollIntoView(false);\n    // }\n  }\n}\n\nfunction scrollHandler(positionY) {\n  scrollHandlerForHeader(positionY);\n  scrollHandlerForBackToTop(positionY);\n  scrollHandlerForTOC(positionY);\n}\n\n////////////////////////////////////////////////////////////////////////////////\n// Theme Toggle\n////////////////////////////////////////////////////////////////////////////////\nfunction setTheme(mode) {\n  if (mode !== \"light\" && mode !== \"dark\" && mode !== \"auto\") {\n    console.error(`Got invalid theme mode: ${mode}. Resetting to auto.`);\n    mode = \"auto\";\n  }\n\n  document.body.dataset.theme = mode;\n  localStorage.setItem(\"theme\", mode);\n  console.log(`Changed to ${mode} mode.`);\n}\n\nfunction cycleThemeOnce() {\n  const currentTheme = localStorage.getItem(\"theme\") || \"auto\";\n  const prefersDark = window.matchMedia(\"(prefers-color-scheme: dark)\").matches;\n\n  if (prefersDark) {\n    // Auto (dark) -> Light -> Dark\n    if (currentTheme === \"auto\") {\n      setTheme(\"light\");\n    } else if (currentTheme == \"light\") {\n      setTheme(\"dark\");\n    } else {\n      setTheme(\"auto\");\n    }\n  } else {\n    // Auto (light) -> Dark -> Light\n    if (currentTheme === \"auto\") {\n      setTheme(\"dark\");\n    } else if (currentTheme == \"dark\") {\n      setTheme(\"light\");\n    } else {\n      setTheme(\"auto\");\n    }\n  }\n}\n\n////////////////////////////////////////////////////////////////////////////////\n// Setup\n////////////////////////////////////////////////////////////////////////////////\nfunction setupScrollHandler() {\n  // Taken from https://developer.mozilla.org/en-US/docs/Web/API/Document/scroll_event\n  let last_known_scroll_position = 0;\n  let ticking = false;\n\n  window.addEventListener(\"scroll\", function (e) {\n    last_known_scroll_position = window.scrollY;\n\n    if (!ticking) {\n      window.requestAnimationFrame(function () {\n        scrollHandler(last_known_scroll_position);\n        ticking = false;\n      });\n\n      ticking = true;\n    }\n  });\n  window.scroll();\n}\n\nfunction setupScrollSpy() {\n  if (tocScroll === null) {\n    return;\n  }\n\n  // Scrollspy -- highlight table on contents, based on scroll\n  new Gumshoe(\".toc-tree a\", {\n    reflow: true,\n    recursive: true,\n    navClass: \"scroll-current\",\n    offset: () => {\n      let rem = parseFloat(getComputedStyle(document.documentElement).fontSize);\n      return header.getBoundingClientRect().height + 2.5 * rem + 1;\n    },\n  });\n}\n\nfunction setupTheme() {\n  // Attach event handlers for toggling themes\n  const buttons = document.getElementsByClassName(\"theme-toggle\");\n  Array.from(buttons).forEach((btn) => {\n    btn.addEventListener(\"click\", cycleThemeOnce);\n  });\n}\n\nfunction setup() {\n  setupTheme();\n  setupScrollHandler();\n  setupScrollSpy();\n}\n\n////////////////////////////////////////////////////////////////////////////////\n// Main entrypoint\n////////////////////////////////////////////////////////////////////////////////\nfunction main() {\n  document.body.parentNode.classList.remove(\"no-js\");\n\n  header = document.querySelector(\"header\");\n  tocScroll = document.querySelector(\".toc-scroll\");\n\n  setup();\n}\n\ndocument.addEventListener(\"DOMContentLoaded\", main);\n"], "names": ["root", "g", "window", "this", "defaults", "navClass", "contentClass", "nested", "nestedClass", "offset", "reflow", "events", "emitEvent", "type", "elem", "detail", "settings", "event", "CustomEvent", "bubbles", "cancelable", "dispatchEvent", "getOffsetTop", "location", "offsetParent", "offsetTop", "sortContents", "contents", "sort", "item1", "item2", "content", "isInView", "bottom", "bounds", "getBoundingClientRect", "parseFloat", "getOffset", "parseInt", "innerHeight", "document", "documentElement", "clientHeight", "top", "isAtBottom", "Math", "ceil", "pageYOffset", "max", "body", "scrollHeight", "offsetHeight", "getActive", "last", "length", "item", "useLastItem", "i", "deactivateNested", "nav", "parentNode", "li", "closest", "classList", "remove", "deactivate", "items", "link", "activateNested", "add", "selector", "options", "navItems", "current", "timeout", "publicAPIs", "querySelectorAll", "Array", "prototype", "for<PERSON>ach", "call", "getElementById", "decodeURIComponent", "hash", "substr", "push", "active", "activate", "<PERSON><PERSON><PERSON><PERSON>", "cancelAnimationFrame", "requestAnimationFrame", "detect", "resize<PERSON><PERSON>ler", "destroy", "removeEventListener", "merged", "arguments", "obj", "key", "hasOwnProperty", "extend", "setup", "addEventListener", "factory", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "exports", "module", "__webpack_modules__", "n", "getter", "__esModule", "d", "a", "definition", "o", "Object", "defineProperty", "enumerable", "get", "globalThis", "Function", "e", "prop", "tocScroll", "header", "lastScrollTop", "scrollTop", "GO_TO_TOP_OFFSET", "cycleThemeOnce", "currentTheme", "localStorage", "getItem", "mode", "matchMedia", "matches", "console", "error", "dataset", "theme", "setItem", "log", "buttons", "getElementsByClassName", "from", "btn", "setupTheme", "last_known_scroll_position", "ticking", "scrollY", "positionY", "headerTop", "floor", "scrollHandlerForHeader", "scrollHandlerForBackToTop", "scrollTo", "querySelector", "scrollHandlerForTOC", "scroll", "setupScrollHandler", "recursive", "rem", "getComputedStyle", "fontSize", "height"], "sourceRoot": ""}