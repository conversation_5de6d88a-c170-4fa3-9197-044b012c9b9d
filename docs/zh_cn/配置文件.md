# XYBotV2 配置文件

XYBotV2 有两个主要配置文件需要修改：

- `main_config.toml`：主配置文件
- `plugins/all_in_one_config.toml`：插件配置文件

## 不同系统下的配置文件修改方法

### Windows 系统

1. 直接使用记事本或其他文本编辑器（推荐使用 VSCode、Sublime Text 等）打开配置文件：

```bash
# 主配置文件位置
XYBotV2/main_config.toml

# 插件配置文件位置
XYBotV2/plugins/all_in_one_config.toml
```

### Linux 系统

1. 使用命令行文本编辑器（如 vim、nano）编辑：

```bash
# 使用 vim 编辑
vim main_config.toml
vim plugins/all_in_one_config.toml

# 或使用 nano 编辑
nano main_config.toml
nano plugins/all_in_one_config.toml
```

2. 也可以使用图形界面编辑器（如果是桌面环境）：

```bash
# 使用 gedit（GNOME）
gedit main_config.toml
gedit plugins/all_in_one_config.toml

# 使用 kate（KDE）
kate main_config.toml
kate plugins/all_in_one_config.toml
```

### Docker 环境

1. 首先找到数据卷位置：

```bash
# 查看数据卷位置
docker volume inspect xybotv2
```

2. 进入数据卷目录编辑配置文件：

```bash
# 配置文件通常位于：
xybotv2-volumes-dir/_data/main_config.toml
xybotv2-volumes-dir/_data/plugins/all_in_one_config.toml
```

3. 修改后重启容器使配置生效：

```bash
docker-compose restart xybotv2
```

## 配置文件修改后生效方式

1. 主配置文件（`main_config.toml`）修改后：

- 需要重启机器人才能生效
- Windows/Linux：按 `Ctrl+C` 停止后重新运行 `python main.py`
- Docker：执行 `docker-compose restart xybotv2`

2. 插件配置文件（`plugins/all_in_one_config.toml`）修改后：

- 可以使用热重载命令，无需重启机器人
- 在聊天中发送以下命令之一（需要机器人管理员权限）：
    - `重载插件 插件名`
    - `重载所有插件`
- 也可以重启机器人来生效

## 注意事项

1. 确保配置文件格式正确：

- 使用 `UTF-8` 编码
- 遵循 `TOML` 格式规范
- 修改后检查是否有语法错误

2. 权限问题：

- Linux/Docker 环境下确保有正确的读写权限
- 如遇权限问题，可使用 sudo 或调整文件权限：

```bash
sudo chmod 644 main_config.toml
sudo chmod 644 plugins/all_in_one_config.toml
```

3. 管理员权限说明：

- 可在主配置文件中设置管理员

4. Docker 环境特别说明：

- 配置文件位于数据卷中，修改后会持久保存
- 重建容器不会影响配置文件
- 确保数据卷正确挂载

## 配置说明

# main_config.toml 配置说明

```toml
[WechatAPIServer]
port = 9000                # WechatAPI服务器端口，默认9000，如有冲突可修改
mode = "release"           # 运行模式：release(生产环境)，debug(调试模式)
redis-host = "127.0.0.1"   # Redis服务器地址，本地使用127.0.0.1
redis-port = 6379          # Redis端口，默认6379
redis-password = ""        # Redis密码，如果有设置密码则填写
redis-db = 0               # Redis数据库编号，默认0

# XYBot 核心设置
[XYBot]
version = "v1.0.0"                    # 版本号，请勿修改
ignore-protection = false             # 是否忽略风控保护机制，建议保持false

# SQLite数据库地址，一般无需修改
XYBotDB-url = "sqlite:///database/xybot.db"
msgDB-url = "sqlite+aiosqlite:///database/message.db"
keyvalDB-url = "sqlite+aiosqlite:///database/keyval.db"

# 管理员设置
admins = ["admin-wxid", "admin-wxid"]  # 管理员的wxid列表，可从消息日志中获取
disabled-plugins = ["ExamplePlugin", "TencentLke"]   # 禁用的插件列表，不需要的插件名称填在这里
timezone = "Asia/Shanghai"             # 时区设置，中国用户使用 Asia/Shanghai

# 实验性功能，如果main_config.toml配置改动，或者plugins文件夹有改动，自动重启。可以在开发时使用，不建议在生产环境使用。
auto-restart = false                 # 仅建议在开发时启用，生产环境保持false

# 消息过滤设置
ignore-mode = "None"            # 消息处理模式：
# "None" - 处理所有消息
# "Whitelist" - 仅处理白名单消息
# "Blacklist" - 屏蔽黑名单消息

whitelist = [# 白名单列表
    "wxid_1", # 个人用户微信ID
    "wxid_2",
    "111@chatroom", # 群聊ID
    "222@chatroom"
]

blacklist = [# 黑名单列表
    "wxid_3", # 个人用户微信ID
    "wxid_4",
    "333@chatroom", # 群聊ID
    "444@chatroom"
]
```

## 说明

1. **管理员设置**
    - 管理员ID获取方法：
        1. 先启动机器人
        2. 私聊机器人任意消息
        3. 在日志中找到自己的 `wxid`

2. **消息过滤模式**
    - `None` 模式：处理所有消息
    - `Whitelist` 模式：仅处理白名单中的用户/群消息
    - `Blacklist` 模式：屏蔽黑名单中的用户/群消息

3. **数据安全**
    - 建议定期备份数据库文件(`xybot.db`)
    - 请勿泄露配置文件中的敏感信息（如 `API` 密钥）

## 插件配置

每个插件现在都在单独的文件夹中，都包含 `config.toml` 插件配置文件。

## Dify插件 [Dify]

用于接入 Dify AI 对话能力的功能模块。

```toml
[Dify]
enable = true                           # 是否启用此功能
api-key = ""                           # Dify的API Key，必填
base-url = "https://api.dify.ai/v1"    # Dify API接口地址

# 支持的指令列表
commands = ["ai", "dify", "聊天", "AI"]

# 指令提示信息
command-tip = """-----XYBot-----
💬AI聊天指令：
聊天 请求内容
"""

# 其他插件的指令，避免冲突
other-plugin-cmd = ["status", "bot", ...]  # 其他插件指令列表

price = 0              # 每次使用扣除的积分，0表示不扣除
admin_ignore = true    # 管理员是否忽略积分扣除
whitelist_ignore = true # 白名单用户是否忽略积分扣除

# Http代理设置(可选)
# 格式: http://用户名:密码@代理地址:代理端口
# 例如：http://127.0.0.1:7890
http-proxy = ""
```

### 配置说明

1. 基础配置

- `enable`: 是否启用Dify插件
- `api-key`: Dify平台的API密钥，必须填写
- `base-url`: Dify API的接口地址，默认为 https://api.dify.ai/v1

2. 指令配置

- `commands`: 支持的指令列表，可以添加多个指令
- `command-tip`: 指令提示信息，可以自定义
- `other-plugin-cmd`: 其他插件的指令，避免冲突

3. 积分配置

- `price`: 每次使用扣除的积分，0表示不扣除
- `admin_ignore`: 管理员是否忽略积分扣除
- `whitelist_ignore`: 白名单用户是否忽略积分扣除

4. 代理配置

- `http-proxy`: Http代理设置(可选)

### 获取API密钥方法

1. 登录 [Dify](https://dify.ai/) 平台
2. 创建或选择一个应用
   - 可导入本项目提供的 [Dify应用模版](https://github.com/HenryXiaoYang/XYBotV2/blob/main/XYBot_Dify_Template.yml)
   - 可配置使用的AI模型
3. 在左侧导航栏找到"访问API"
4. 创建新的API密钥
5. 将获得的密钥填入配置文件的 api-key 字段

Dify相关信息：

- Dify官方文档: https://docs.dify.ai/zh-hans
- CSDN的教程：https://blog.csdn.net/2301_81940605/article/details/143730438
- 学会使用搜索引擎: https://www.bing.com/search?q=Dify+API+新手教程
- 学会使用搜索引擎: https://www.google.com/search?q=Dify+API+新手教程
- 学会使用Github: https://github.com/langgenius/dify

### 使用示例

1. 基础对话
   `帮我写一首诗`

2. 群聊中使用
   `@机器人 今天心情不好`

### 注意事项

- API密钥不要泄露
- 建议配置代理以提高访问稳定性
- 合理设置积分规则，避免滥用


## 获取天气 [GetWeather]

用于查询城市天气信息的功能模块。

```toml
enable = true      # 是否启用此功能
command-format = """⚙️获取天气：    # 支持的命令格式说明
天气 城市名
天气城市名
城市名天气
城市名 天气"""
api-key = "api-key"    # 和风天气API密钥
# 申请方法：
# 1. 访问 https://dev.qweather.com/
# 2. 注册账号并选择免费订阅
# 3. 获取 Private KEY（注意不是 Public ID）
```